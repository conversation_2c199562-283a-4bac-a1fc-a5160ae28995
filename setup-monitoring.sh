#!/bin/bash

# Animalia Monitoring Setup Script
# Sets up Prometheus + Grafana for workflow monitoring

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${CYAN}ℹ️  $1${NC}"
}

# Check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker first."
        exit 1
    fi
    print_success "Docker is running"
}

# Check if application is running
check_app() {
    print_info "Checking if Animalia application is running..."
    if curl -s http://localhost:8080/actuator/health > /dev/null 2>&1; then
        print_success "Animalia application is running"
    else
        print_warning "Animalia application is not running"
        print_info "Please start it with: ./gradlew bootRun"
        read -p "Continue anyway? (y/n): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
}

# Setup monitoring stack
setup_monitoring() {
    print_header "SETTING UP MONITORING STACK"
    
    # Stop existing containers if they exist
    print_info "Stopping existing monitoring containers..."
    docker-compose -f docker-compose.monitoring.yml down 2>/dev/null || true
    
    # Start the monitoring stack
    print_info "Starting Prometheus and Grafana..."
    docker-compose -f docker-compose.monitoring.yml up -d
    
    # Wait for services to be ready
    print_info "Waiting for services to start..."
    sleep 10
    
    # Check if Prometheus is running
    if curl -s http://localhost:9090/-/healthy > /dev/null 2>&1; then
        print_success "Prometheus is running at http://localhost:9090"
    else
        print_error "Prometheus failed to start"
        return 1
    fi
    
    # Check if Grafana is running
    if curl -s http://localhost:3000/api/health > /dev/null 2>&1; then
        print_success "Grafana is running at http://localhost:3000"
    else
        print_error "Grafana failed to start"
        return 1
    fi
}

# Configure Grafana data source
configure_grafana() {
    print_header "CONFIGURING GRAFANA"
    
    print_info "Waiting for Grafana to be fully ready..."
    sleep 15
    
    # Add Prometheus data source
    print_info "Adding Prometheus data source to Grafana..."
    curl -X POST \
        -H "Content-Type: application/json" \
        -d '{
            "name": "Prometheus",
            "type": "prometheus",
            "url": "http://prometheus:9090",
            "access": "proxy",
            "isDefault": true
        }' \
        *********************************/api/datasources 2>/dev/null || print_warning "Data source might already exist"
    
    print_success "Grafana configured with Prometheus data source"
}

# Import dashboard
import_dashboard() {
    print_header "IMPORTING DASHBOARD"
    
    print_info "Importing Animalia Workflow Monitoring dashboard..."
    
    # Import the dashboard
    curl -X POST \
        -H "Content-Type: application/json" \
        -d @animalia-workflow-dashboard.json \
        *********************************/api/dashboards/db 2>/dev/null && \
        print_success "Dashboard imported successfully" || \
        print_warning "Dashboard import failed (might already exist)"
}

# Generate test data
generate_test_data() {
    print_header "GENERATING TEST DATA"
    
    print_info "Making test API calls to generate metrics..."
    
    for i in {1..10}; do
        echo "Making test call $i/10..."
        
        # Test various endpoints to generate metrics
        curl -s http://localhost:8080/actuator/health > /dev/null 2>&1 || true
        curl -s http://localhost:8080/actuator/metrics > /dev/null 2>&1 || true
        curl -s http://localhost:8080/actuator/prometheus > /dev/null 2>&1 || true
        
        # Test our monitoring endpoints
        curl -s http://localhost:8080/api/monitoring/health > /dev/null 2>&1 || true
        curl -s http://localhost:8080/api/monitoring/workflows > /dev/null 2>&1 || true
        
        sleep 1
    done
    
    print_success "Test data generation completed"
}

# Show access information
show_access_info() {
    print_header "ACCESS INFORMATION"
    
    echo -e "${CYAN}🚀 Your monitoring stack is ready!${NC}"
    echo ""
    echo -e "${GREEN}📊 Grafana Dashboard:${NC}"
    echo "   URL: http://localhost:3000"
    echo "   Username: admin"
    echo "   Password: admin"
    echo ""
    echo -e "${GREEN}📈 Prometheus:${NC}"
    echo "   URL: http://localhost:9090"
    echo ""
    echo -e "${GREEN}🎯 Key Dashboards:${NC}"
    echo "   • Animalia Workflow Monitoring (imported)"
    echo "   • Create custom dashboards using the queries in GRAFANA_PROMETHEUS_SETUP.md"
    echo ""
    echo -e "${GREEN}📋 Useful Queries:${NC}"
    echo "   • Workflow success rate: (rate(workflow_executions_total{success=\"true\"}[5m]) / rate(workflow_executions_total[5m])) * 100"
    echo "   • Processing time: histogram_quantile(0.95, rate(workflow_execution_seconds_bucket[5m])) * 1000"
    echo "   • Error rate: rate(workflow_executions_total{success=\"false\"}[5m]) by (error_type)"
    echo ""
    echo -e "${YELLOW}💡 Next Steps:${NC}"
    echo "   1. Open Grafana and explore the imported dashboard"
    echo "   2. Use your appointment API to generate real workflow data"
    echo "   3. Set up alerts for critical metrics"
    echo "   4. Customize dashboards for your specific needs"
}

# Main execution
main() {
    echo -e "${PURPLE}"
    echo "🚀 Animalia Monitoring Setup"
    echo "============================"
    echo -e "${NC}"
    
    check_docker
    check_app
    setup_monitoring
    configure_grafana
    import_dashboard
    
    # Ask if user wants to generate test data
    read -p "🤔 Would you like to generate test data to see metrics in action? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        generate_test_data
    fi
    
    show_access_info
    
    echo ""
    print_success "🎉 Monitoring setup completed successfully!"
    echo ""
    print_info "💡 To stop the monitoring stack: docker-compose -f docker-compose.monitoring.yml down"
    print_info "💡 To restart: docker-compose -f docker-compose.monitoring.yml up -d"
}

# Check if required files exist
if [ ! -f "docker-compose.monitoring.yml" ]; then
    print_error "docker-compose.monitoring.yml not found"
    exit 1
fi

if [ ! -f "animalia-workflow-dashboard.json" ]; then
    print_error "animalia-workflow-dashboard.json not found"
    exit 1
fi

# Run the setup
main "$@"
