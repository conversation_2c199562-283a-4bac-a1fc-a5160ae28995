# Grafana + Prometheus Setup for Animalia Workflow Metrics

## 🚀 Quick Setup Guide

### Step 1: Start Your Application
```bash
# Make sure your application is running
./gradlew bootRun
```

### Step 2: Set Up Prometheus

Create a `prometheus.yml` configuration file:

```yaml
# prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'animalia-backend'
    static_configs:
      - targets: ['localhost:8080']
    metrics_path: '/actuator/prometheus'
    scrape_interval: 5s
    scrape_timeout: 5s
```

Start Prometheus using Docker:
```bash
# Create the config file first
cat > prometheus.yml << 'EOF'
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'animalia-backend'
    static_configs:
      - targets: ['host.docker.internal:8080']
    metrics_path: '/actuator/prometheus'
    scrape_interval: 5s
    scrape_timeout: 5s
EOF

# Start Prometheus
docker run -d \
  --name prometheus \
  -p 9090:9090 \
  -v $(pwd)/prometheus.yml:/etc/prometheus/prometheus.yml \
  prom/prometheus:latest
```

### Step 3: Set Up Grafana

Start Grafana using Docker:
```bash
docker run -d \
  --name grafana \
  -p 3000:3000 \
  -e "GF_SECURITY_ADMIN_PASSWORD=admin" \
  grafana/grafana:latest
```

### Step 4: Configure Grafana Data Source

1. Open Grafana: http://localhost:3000
2. Login with `admin/admin`
3. Go to **Configuration** → **Data Sources**
4. Click **Add data source**
5. Select **Prometheus**
6. Set URL to: `http://host.docker.internal:9090` (or `http://prometheus:9090` if using docker-compose)
7. Click **Save & Test**

## 📊 Dashboard Queries for Your Workflow Metrics

### 1. Workflow Success Rate
```promql
# Overall workflow success rate
(
  rate(workflow_executions_total{success="true"}[5m]) / 
  rate(workflow_executions_total[5m])
) * 100
```

### 2. Workflow Processing Time (95th Percentile)
```promql
# 95th percentile processing time by workflow
histogram_quantile(0.95, 
  rate(workflow_execution_seconds_bucket[5m])
) * 1000
```

### 3. Method Execution Time
```promql
# Average method execution time
rate(method_execution_seconds_sum[5m]) / 
rate(method_execution_seconds_count[5m]) * 1000
```

### 4. Error Rate by Type
```promql
# Error rate by error type
rate(workflow_executions_total{success="false"}[5m]) by (error_type)
```

### 5. Appointment Operations Rate
```promql
# Appointment operations per second
rate(appointment_operations_total[5m]) by (operation)
```

### 6. Conflict Detection Rate
```promql
# Conflicts detected per minute
rate(appointment_conflicts_detected[5m]) * 60
```

### 7. Alternative Suggestions Efficiency
```promql
# Alternatives generated per conflict
rate(appointment_alternatives_generated[5m]) / 
rate(appointment_conflicts_detected[5m])
```

## 🎨 Ready-to-Import Dashboard JSON

Here's a complete Grafana dashboard configuration you can import:

### Dashboard: "Animalia Workflow Monitoring"

1. In Grafana, go to **+** → **Import**
2. Copy and paste this JSON configuration:

```json
{
  "dashboard": {
    "id": null,
    "title": "Animalia Workflow Monitoring",
    "tags": ["animalia", "workflows", "appointments"],
    "timezone": "browser",
    "panels": [
      {
        "id": 1,
        "title": "Workflow Success Rate",
        "type": "stat",
        "targets": [
          {
            "expr": "(rate(workflow_executions_total{success=\"true\"}[5m]) / rate(workflow_executions_total[5m])) * 100",
            "legendFormat": "Success Rate %"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "unit": "percent",
            "min": 0,
            "max": 100,
            "thresholds": {
              "steps": [
                {"color": "red", "value": 0},
                {"color": "yellow", "value": 80},
                {"color": "green", "value": 95}
              ]
            }
          }
        },
        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}
      },
      {
        "id": 2,
        "title": "Workflow Processing Time (95th Percentile)",
        "type": "timeseries",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(workflow_execution_seconds_bucket[5m])) * 1000",
            "legendFormat": "{{workflow}} - 95th percentile (ms)"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "unit": "ms"
          }
        },
        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}
      },
      {
        "id": 3,
        "title": "Error Rate by Type",
        "type": "timeseries",
        "targets": [
          {
            "expr": "rate(workflow_executions_total{success=\"false\"}[5m]) by (error_type)",
            "legendFormat": "{{error_type}}"
          }
        ],
        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}
      },
      {
        "id": 4,
        "title": "Appointment Operations",
        "type": "timeseries",
        "targets": [
          {
            "expr": "rate(appointment_operations_total[5m]) by (operation)",
            "legendFormat": "{{operation}}"
          }
        ],
        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}
      },
      {
        "id": 5,
        "title": "Method Execution Time",
        "type": "table",
        "targets": [
          {
            "expr": "rate(method_execution_seconds_sum[5m]) / rate(method_execution_seconds_count[5m]) * 1000",
            "legendFormat": "{{class}}.{{method}}"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "unit": "ms"
          }
        },
        "gridPos": {"h": 8, "w": 24, "x": 0, "y": 16}
      }
    ],
    "time": {
      "from": "now-1h",
      "to": "now"
    },
    "refresh": "5s"
  }
}
```

## 🔧 Docker Compose Setup (Recommended)

For easier management, create a `docker-compose.yml` file:

```yaml
version: '3.8'

services:
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'

  grafana:
    image: grafana/grafana:latest
    container_name: grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana-storage:/var/lib/grafana

volumes:
  grafana-storage:
```

Start both services:
```bash
docker-compose up -d
```

## 📈 Key Metrics to Monitor

### Business Metrics
- **Appointment Success Rate**: `(rate(workflow_executions_total{workflow="schedule_appointment",success="true"}[5m]) / rate(workflow_executions_total{workflow="schedule_appointment"}[5m])) * 100`
- **Average Scheduling Time**: `rate(workflow_execution_seconds_sum{workflow="schedule_appointment"}[5m]) / rate(workflow_execution_seconds_count{workflow="schedule_appointment"}[5m]) * 1000`
- **Conflict Rate**: `rate(appointment_conflicts_detected[5m])`

### Performance Metrics
- **Method Performance**: `histogram_quantile(0.95, rate(method_execution_seconds_bucket[5m])) by (class, method) * 1000`
- **Error Frequency**: `rate(workflow_executions_total{success="false"}[5m]) by (error_type)`
- **Throughput**: `rate(workflow_executions_total[5m]) by (workflow)`

### System Health
- **Overall Success Rate**: `(sum(rate(workflow_executions_total{success="true"}[5m])) / sum(rate(workflow_executions_total[5m]))) * 100`
- **Response Time Trends**: `histogram_quantile(0.50, rate(workflow_execution_seconds_bucket[5m])) * 1000`

## 🚨 Recommended Alerts

Set up alerts for:
- Success rate below 95%
- Processing time above 5 seconds
- Error rate above 5%
- High conflict detection rate

## 🔍 Troubleshooting

### Common Issues:

1. **No metrics showing**:
   - Check if your app is running: `curl http://localhost:8080/actuator/prometheus`
   - Verify Prometheus is scraping: http://localhost:9090/targets

2. **Prometheus can't reach app**:
   - Use `host.docker.internal:8080` instead of `localhost:8080` in Docker
   - Check firewall settings

3. **Grafana can't connect to Prometheus**:
   - Use `http://prometheus:9090` if using docker-compose
   - Use `http://host.docker.internal:9090` if running separately

## 🚀 Quick Start (Automated Setup)

**The easiest way to get started:**

```bash
# 1. Make sure your Animalia app is running
./gradlew bootRun

# 2. In another terminal, run the automated setup
./setup-monitoring.sh
```

This script will:
- ✅ Set up Prometheus + Grafana with Docker
- ✅ Configure data sources automatically
- ✅ Import a ready-to-use dashboard
- ✅ Generate test data to see metrics immediately

## 🎯 Your Specific Workflow Metrics

### Key Metrics Available in Your Application:

1. **`workflow_execution_seconds`** - Timing for your workflows
   - Labels: `workflow`, `success`, `salon_id`, `staff_id`
   - Available workflows: `schedule_appointment`, `cancel_appointment`

2. **`method_execution_seconds`** - Method-level performance
   - Labels: `class`, `method`, `success`
   - Key methods: `validateAppointmentRequest`, `createAppointment`, `saveAppointment`

3. **`appointment_operations_total`** - Business operation counters
   - Labels: `operation`, `salon_id`, `staff_id`, `success`
   - Operations: `schedule`, `cancel`, `reschedule`

### Grafana Queries for Your Business:

**Appointment Booking Success Rate:**
```promql
(rate(workflow_executions_total{workflow="schedule_appointment",success="true"}[5m]) /
 rate(workflow_executions_total{workflow="schedule_appointment"}[5m])) * 100
```

**Average Appointment Scheduling Time:**
```promql
rate(workflow_execution_seconds_sum{workflow="schedule_appointment"}[5m]) /
rate(workflow_execution_seconds_count{workflow="schedule_appointment"}[5m]) * 1000
```

**Conflicts per Hour by Salon:**
```promql
rate(appointment_conflicts_detected[1h]) * 3600 by (salon_id)
```

## 🎯 Next Steps

1. **Run the setup**: `./setup-monitoring.sh`
2. **Generate data**: Use your appointment API or run `./monitoring-workflow-demo.sh`
3. **Explore dashboards**: Open http://localhost:3000 (admin/admin)
4. **Customize**: Add panels for your specific business needs
5. **Set up alerts**: Configure notifications for critical metrics
