{"dashboard": {"id": null, "title": "Animalia Workflow Monitoring", "tags": ["animalia", "workflows", "appointments", "performance"], "timezone": "browser", "panels": [{"id": 1, "title": "Overall System Health", "type": "stat", "targets": [{"expr": "(sum(rate(workflow_executions_total{success=\"true\"}[5m])) / sum(rate(workflow_executions_total[5m]))) * 100", "legendFormat": "Success Rate %", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percent", "min": 0, "max": 100, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 80}, {"color": "green", "value": 95}]}}}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 0}}, {"id": 2, "title": "Total Operations/min", "type": "stat", "targets": [{"expr": "sum(rate(workflow_executions_total[5m])) * 60", "legendFormat": "Operations/min", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "ops", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 6, "x": 6, "y": 0}}, {"id": 3, "title": "Average Response Time", "type": "stat", "targets": [{"expr": "(sum(rate(workflow_execution_seconds_sum[5m])) / sum(rate(workflow_execution_seconds_count[5m]))) * 1000", "legendFormat": "Avg Response Time (ms)", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "ms", "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 1000}, {"color": "red", "value": 5000}]}}}, "gridPos": {"h": 8, "w": 6, "x": 12, "y": 0}}, {"id": 4, "title": "Error Rate", "type": "stat", "targets": [{"expr": "(sum(rate(workflow_executions_total{success=\"false\"}[5m])) / sum(rate(workflow_executions_total[5m]))) * 100", "legendFormat": "Error Rate %", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percent", "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 1}, {"color": "red", "value": 5}]}}}, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 0}}, {"id": 5, "title": "Workflow Success Rate by Type", "type": "timeseries", "targets": [{"expr": "(rate(workflow_executions_total{success=\"true\"}[5m]) / rate(workflow_executions_total[5m])) * 100", "legendFormat": "{{workflow}} Success Rate", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percent", "min": 0, "max": 100}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 6, "title": "Workflow Processing Time (95th Percentile)", "type": "timeseries", "targets": [{"expr": "histogram_quantile(0.95, rate(workflow_execution_seconds_bucket[5m])) * 1000", "legendFormat": "{{workflow}} - 95th percentile", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "ms"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}}, {"id": 7, "title": "Appointment Operations Rate", "type": "timeseries", "targets": [{"expr": "rate(appointment_operations_total[5m]) * 60", "legendFormat": "{{operation}} per minute", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "ops"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}}, {"id": 8, "title": "Error Types Distribution", "type": "piechart", "targets": [{"expr": "sum by (error_type) (rate(workflow_executions_total{success=\"false\"}[5m]))", "legendFormat": "{{error_type}}", "refId": "A"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}}, {"id": 9, "title": "Method Execution Performance", "type": "table", "targets": [{"expr": "topk(10, rate(method_execution_seconds_sum[5m]) / rate(method_execution_seconds_count[5m]) * 1000)", "legendFormat": "{{class}}.{{method}}", "refId": "A", "format": "table"}], "fieldConfig": {"defaults": {"unit": "ms"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}}, {"id": 10, "title": "Appointment Conflicts & Alternatives", "type": "timeseries", "targets": [{"expr": "rate(appointment_conflicts_detected[5m]) * 60", "legendFormat": "Conflicts per minute", "refId": "A"}, {"expr": "rate(appointment_alternatives_generated[5m]) * 60", "legendFormat": "Alternatives per minute", "refId": "B"}], "fieldConfig": {"defaults": {"unit": "short"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}}, {"id": 11, "title": "Workflow Throughput", "type": "timeseries", "targets": [{"expr": "rate(workflow_executions_total[5m]) by (workflow) * 60", "legendFormat": "{{workflow}} per minute", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "ops"}}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 32}}], "time": {"from": "now-1h", "to": "now"}, "refresh": "5s", "schemaVersion": 30, "version": 1}}