# 🔥 Advanced Monitoring with @Timed and Firebase Integration
## Pet Grooming Appointment System

### 🎯 **Overview**

This guide shows you how to use `@Timed` annotations for method-level monitoring and integrate with Firebase for real-time data visualization.

---

## 📊 **1. Using @Timed Annotations**

Your system already has `TimedAspect` configured! Here's how to use it effectively:

### **A. Basic @Timed Usage**

```kotlin
import io.micrometer.core.annotation.Timed

@Service
class AppointmentManagementUseCaseImpl(
    private val appointmentRepository: AppointmentRepository,
    private val monitoringService: MonitoringService
) : AppointmentManagementUseCase {

    @Timed(
        value = "appointment.creation",
        description = "Time taken to create an appointment",
        percentiles = [0.5, 0.95, 0.99]
    )
    override fun createAppointment(command: CreateAppointmentCommand): Appointment {
        // Your appointment creation logic
        return appointmentRepository.save(appointment)
    }

    @Timed(
        value = "appointment.conflict.detection",
        description = "Time taken to detect appointment conflicts",
        extraTags = ["operation", "conflict-detection"]
    )
    override fun detectConflicts(request: ConflictDetectionRequest): ConflictDetectionResult {
        // Conflict detection logic
    }
}
```

### **B. Advanced @Timed with Dynamic Tags**

```kotlin
@RestController
@RequestMapping("/salons/{salonId}/appointments")
class SalonAppointmentController(
    private val appointmentUseCase: AppointmentManagementUseCase
) {

    @Timed(
        value = "api.appointments.get",
        description = "Get appointments for salon",
        extraTags = ["endpoint", "get-appointments"]
    )
    @GetMapping
    fun getAppointments(
        @PathVariable salonId: String,
        @RequestParam(required = false) date: LocalDate?,
        @RequestParam(required = false) staffId: String?
    ): ResponseEntity<List<AppointmentDto>> {
        // Add custom tags programmatically
        Metrics.counter("api.requests.salon.appointments", 
            "salon_id", salonId,
            "has_date_filter", (date != null).toString(),
            "has_staff_filter", (staffId != null).toString()
        ).increment()
        
        // Your logic here
    }
}
```

### **C. Conditional Timing with @Timed**

```kotlin
@Service
class ConflictDetectionService {

    @Timed(
        value = "conflict.detection.working.hours",
        description = "Working hours validation timing"
    )
    fun validateWorkingHours(request: ValidationRequest): ValidationResult {
        // Working hours validation
    }

    @Timed(
        value = "conflict.detection.existing.appointments", 
        description = "Existing appointments check timing"
    )
    fun checkExistingAppointments(request: ValidationRequest): List<Conflict> {
        // Existing appointments check
    }

    @Timed(
        value = "conflict.detection.block.times",
        description = "Block times validation timing"
    )
    fun validateBlockTimes(request: ValidationRequest): List<Conflict> {
        // Block times validation
    }
}
```

---

## 🔥 **2. Firebase Integration for Real-time Monitoring**

### **A. Firebase Configuration**

First, add Firebase dependencies to your `build.gradle.kts`:

```kotlin
dependencies {
    // Existing dependencies...
    
    // Firebase Admin SDK
    implementation("com.google.firebase:firebase-admin:9.2.0")
    
    // Firebase Realtime Database
    implementation("com.google.firebase:firebase-database:20.3.0")
    
    // JSON processing
    implementation("com.fasterxml.jackson.module:jackson-module-kotlin")
}
```

### **B. Firebase Service Implementation**

```kotlin
@Service
class FirebaseMonitoringService(
    private val firebaseDatabase: DatabaseReference
) {
    
    private val logger = LoggerFactory.getLogger(FirebaseMonitoringService::class.java)
    
    fun sendMetricToFirebase(metricName: String, value: Double, tags: Map<String, String> = emptyMap()) {
        try {
            val timestamp = System.currentTimeMillis()
            val metricData = mapOf(
                "value" to value,
                "timestamp" to timestamp,
                "tags" to tags,
                "date" to LocalDateTime.now().toString()
            )
            
            firebaseDatabase
                .child("metrics")
                .child(metricName.replace(".", "_"))
                .child(timestamp.toString())
                .setValueAsync(metricData)
                
            logger.debug("Sent metric to Firebase: $metricName = $value")
        } catch (e: Exception) {
            logger.error("Failed to send metric to Firebase: $metricName", e)
        }
    }
    
    fun sendEventToFirebase(eventType: String, data: Map<String, Any>) {
        try {
            val timestamp = System.currentTimeMillis()
            val eventData = mapOf(
                "eventType" to eventType,
                "data" to data,
                "timestamp" to timestamp,
                "date" to LocalDateTime.now().toString()
            )
            
            firebaseDatabase
                .child("events")
                .child(eventType.replace(".", "_"))
                .child(timestamp.toString())
                .setValueAsync(eventData)
                
            logger.debug("Sent event to Firebase: $eventType")
        } catch (e: Exception) {
            logger.error("Failed to send event to Firebase: $eventType", e)
        }
    }
    
    fun sendRealtimeAppointmentMetrics(salonId: String, metrics: AppointmentMetrics) {
        try {
            val metricsData = mapOf(
                "totalAppointments" to metrics.totalAppointments,
                "todayAppointments" to metrics.todayAppointments,
                "conflictsDetected" to metrics.conflictsDetected,
                "averageResponseTime" to metrics.averageResponseTime,
                "lastUpdated" to System.currentTimeMillis()
            )
            
            firebaseDatabase
                .child("realtime")
                .child("salons")
                .child(salonId)
                .child("metrics")
                .setValueAsync(metricsData)
                
        } catch (e: Exception) {
            logger.error("Failed to send realtime metrics to Firebase", e)
        }
    }
}
```

### **C. Enhanced Monitoring Service with Firebase**

```kotlin
@Component
class EnhancedMicrometerMonitoringAdapter(
    private val meterRegistry: MeterRegistry,
    private val firebaseMonitoringService: FirebaseMonitoringService
) : MonitoringService {

    override fun recordMetric(name: String, value: Double, tags: Map<String, String>) {
        try {
            // Record to Micrometer (existing functionality)
            val tagList = convertToTags(tags)
            meterRegistry.gauge(name, tagList, value)
            
            // Send to Firebase for real-time visualization
            firebaseMonitoringService.sendMetricToFirebase(name, value, tags)
            
            logger.debug("Recorded metric: $name = $value with tags: $tags")
        } catch (e: Exception) {
            logger.error("Failed to record metric: $name", e)
        }
    }

    override fun recordEvent(eventType: String, details: Map<String, Any>) {
        try {
            // Record to Micrometer
            val tags = mapOf("eventType" to eventType)
            incrementCounter("app.events", tags)
            
            // Send to Firebase
            firebaseMonitoringService.sendEventToFirebase(eventType, details)
            
            logger.info("Event recorded: $eventType, details: $details")
        } catch (e: Exception) {
            logger.error("Failed to record event: $eventType", e)
        }
    }
}
```

---

## 📱 **3. Real-time Dashboard Data Structure**

### **A. Firebase Database Structure**

```json
{
  "metrics": {
    "appointment_creation": {
      "1703123456789": {
        "value": 245.5,
        "timestamp": 1703123456789,
        "tags": {
          "salon_id": "salon-123",
          "staff_id": "staff-456"
        },
        "date": "2024-06-11T10:30:45"
      }
    },
    "conflict_detection_duration": {
      "1703123456790": {
        "value": 89.2,
        "timestamp": 1703123456790,
        "tags": {
          "conflict_type": "working_hours",
          "salon_id": "salon-123"
        }
      }
    }
  },
  "events": {
    "AppointmentScheduled": {
      "1703123456791": {
        "eventType": "AppointmentScheduled",
        "data": {
          "appointmentId": "apt-789",
          "salonId": "salon-123",
          "clientId": "client-456",
          "totalPrice": 150.0
        },
        "timestamp": 1703123456791
      }
    }
  },
  "realtime": {
    "salons": {
      "salon-123": {
        "metrics": {
          "totalAppointments": 1250,
          "todayAppointments": 15,
          "conflictsDetected": 3,
          "averageResponseTime": 125.5,
          "lastUpdated": 1703123456792
        }
      }
    }
  }
}
```

### **B. Scheduled Metrics Collection**

```kotlin
@Component
class RealtimeMetricsCollector(
    private val appointmentRepository: AppointmentRepository,
    private val firebaseMonitoringService: FirebaseMonitoringService,
    private val meterRegistry: MeterRegistry
) {

    @Scheduled(fixedRate = 30000) // Every 30 seconds
    fun collectAndSendRealtimeMetrics() {
        try {
            // Collect metrics for each salon
            val salons = salonRepository.findAll()
            
            salons.forEach { salon ->
                val metrics = collectSalonMetrics(salon.id)
                firebaseMonitoringService.sendRealtimeAppointmentMetrics(
                    salon.id.value, 
                    metrics
                )
            }
        } catch (e: Exception) {
            logger.error("Failed to collect realtime metrics", e)
        }
    }
    
    private fun collectSalonMetrics(salonId: SalonId): AppointmentMetrics {
        val today = LocalDate.now()
        
        return AppointmentMetrics(
            totalAppointments = appointmentRepository.countBySalonId(salonId),
            todayAppointments = appointmentRepository.countBySalonIdAndDate(salonId, today),
            conflictsDetected = getConflictCount(salonId),
            averageResponseTime = getAverageResponseTime("appointment.creation")
        )
    }
    
    private fun getAverageResponseTime(metricName: String): Double {
        return meterRegistry.find(metricName)
            .timer()
            ?.mean(TimeUnit.MILLISECONDS) ?: 0.0
    }
}
```

---

## 🎨 **4. Frontend Dashboard Integration**

### **A. React/Flutter Firebase Listener**

```javascript
// React example for real-time monitoring dashboard
import { database } from './firebase-config';
import { ref, onValue } from 'firebase/database';

function MonitoringDashboard({ salonId }) {
  const [metrics, setMetrics] = useState({});
  const [recentEvents, setRecentEvents] = useState([]);

  useEffect(() => {
    // Listen to real-time metrics
    const metricsRef = ref(database, `realtime/salons/${salonId}/metrics`);
    const unsubscribeMetrics = onValue(metricsRef, (snapshot) => {
      const data = snapshot.val();
      if (data) {
        setMetrics(data);
      }
    });

    // Listen to recent events
    const eventsRef = ref(database, 'events');
    const unsubscribeEvents = onValue(eventsRef, (snapshot) => {
      const data = snapshot.val();
      if (data) {
        const events = Object.values(data)
          .flat()
          .sort((a, b) => b.timestamp - a.timestamp)
          .slice(0, 10);
        setRecentEvents(events);
      }
    });

    return () => {
      unsubscribeMetrics();
      unsubscribeEvents();
    };
  }, [salonId]);

  return (
    <div className="monitoring-dashboard">
      <div className="metrics-grid">
        <MetricCard 
          title="Today's Appointments" 
          value={metrics.todayAppointments || 0}
          trend="up"
        />
        <MetricCard 
          title="Total Appointments" 
          value={metrics.totalAppointments || 0}
        />
        <MetricCard 
          title="Conflicts Detected" 
          value={metrics.conflictsDetected || 0}
          trend="down"
        />
        <MetricCard 
          title="Avg Response Time" 
          value={`${metrics.averageResponseTime || 0}ms`}
        />
      </div>
      
      <RealtimeChart data={metrics} />
      <EventsTimeline events={recentEvents} />
    </div>
  );
}
```

### **B. Flutter Real-time Dashboard**

```dart
// Flutter example for mobile monitoring
class MonitoringDashboard extends StatefulWidget {
  final String salonId;

  MonitoringDashboard({required this.salonId});

  @override
  _MonitoringDashboardState createState() => _MonitoringDashboardState();
}

class _MonitoringDashboardState extends State<MonitoringDashboard> {
  final FirebaseDatabase _database = FirebaseDatabase.instance;
  Map<String, dynamic> metrics = {};
  List<dynamic> recentEvents = [];

  @override
  void initState() {
    super.initState();
    _listenToMetrics();
    _listenToEvents();
  }

  void _listenToMetrics() {
    _database
        .ref('realtime/salons/${widget.salonId}/metrics')
        .onValue
        .listen((event) {
      if (event.snapshot.value != null) {
        setState(() {
          metrics = Map<String, dynamic>.from(event.snapshot.value as Map);
        });
      }
    });
  }

  void _listenToEvents() {
    _database
        .ref('events')
        .limitToLast(10)
        .onValue
        .listen((event) {
      if (event.snapshot.value != null) {
        final data = event.snapshot.value as Map;
        final events = data.values
            .expand((eventType) => (eventType as Map).values)
            .toList()
          ..sort((a, b) => (b['timestamp'] as int).compareTo(a['timestamp'] as int));

        setState(() {
          recentEvents = events.take(10).toList();
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Salon Monitoring')),
      body: Column(
        children: [
          MetricsGrid(metrics: metrics),
          Expanded(child: RealtimeChart(data: metrics)),
          EventsList(events: recentEvents),
        ],
      ),
    );
  }
}
```

---

## 🚀 **5. Configuration and Setup**

### **A. Application Configuration**

Add to your `application.properties`:

```properties
# Enable Firebase monitoring
monitoring.firebase.enabled=true

# Enable real-time metrics collection
monitoring.realtime.enabled=true

# Firebase configuration (use environment variables)
# FIREBASE_DATABASE_URL=https://your-project-default-rtdb.firebaseio.com
# FIREBASE_SECRET=your-firebase-secret

# @Timed configuration
management.metrics.web.server.request.autotime.enabled=true
management.metrics.web.server.request.autotime.percentiles=0.5,0.95,0.99
```

### **B. Environment Variables**

```bash
# Firebase configuration
export FIREBASE_DATABASE_URL="https://your-project-default-rtdb.firebaseio.com"
export FIREBASE_SECRET="your-firebase-secret"

# Application environment
export ENVIRONMENT="production"
export APP_VERSION="1.0.0"
```

### **C. Build Dependencies**

Add to your `build.gradle.kts`:

```kotlin
dependencies {
    // Existing dependencies...

    // Firebase Admin SDK (optional - for server-side Firebase)
    implementation("com.google.firebase:firebase-admin:9.2.0")

    // HTTP client for Firebase REST API
    implementation("org.springframework.boot:spring-boot-starter-webflux")
}
```

---

## 📊 **6. Practical Usage Examples**

### **A. Start Monitoring**

```bash
# 1. Start your application with monitoring enabled
./gradlew bootRun --args='--spring.profiles.active=development'

# 2. Check that @Timed is working
curl http://localhost:8080/api/actuator/metrics/appointment.creation

# 3. Verify Firebase integration (if enabled)
curl http://localhost:8080/api/actuator/metrics | grep firebase
```

### **B. Generate Test Data**

```bash
# Create some appointments to generate metrics
for i in {1..10}; do
  curl -X POST http://localhost:8080/api/salons/salon-123/appointments \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer YOUR_JWT_TOKEN" \
    -d '{
      "clientId": "client-'$i'",
      "petId": "pet-'$i'",
      "staffId": "staff-123",
      "serviceIds": ["service-1"],
      "appointmentDate": "2024-06-15",
      "startTime": "10:00",
      "endTime": "11:00"
    }'
done
```

### **C. View Metrics**

```bash
# View @Timed metrics
curl http://localhost:8080/api/actuator/metrics/appointment.creation
curl http://localhost:8080/api/actuator/metrics/api.appointments.create

# View Prometheus format
curl http://localhost:8080/api/actuator/prometheus | grep appointment
```

---

## 🎨 **7. Firebase Dashboard Setup**

### **A. Firebase Project Setup**

1. **Create Firebase Project**: Go to https://console.firebase.google.com
2. **Enable Realtime Database**: Database → Realtime Database → Create
3. **Get Database URL**: Copy your database URL
4. **Generate Secret**: Project Settings → Service Accounts → Database Secrets

### **B. Database Security Rules**

```json
{
  "rules": {
    "metrics": {
      ".read": "auth != null",
      ".write": "auth != null"
    },
    "events": {
      ".read": "auth != null",
      ".write": "auth != null"
    },
    "realtime": {
      ".read": "auth != null",
      ".write": "auth != null"
    },
    "alerts": {
      ".read": "auth != null",
      ".write": "auth != null"
    }
  }
}
```

---

## 📈 **8. Monitoring Best Practices**

### **A. @Timed Usage Guidelines**

1. **Use descriptive names**: `appointment.creation` not `create`
2. **Add meaningful tags**: Include operation type, scope, etc.
3. **Set percentiles**: Always include 0.5, 0.95, 0.99
4. **Don't over-instrument**: Focus on critical business operations

### **B. Firebase Integration Tips**

1. **Use environment variables** for Firebase credentials
2. **Implement retry logic** for Firebase API calls
3. **Monitor Firebase quota** usage in production
4. **Use structured data** for easy querying

### **C. Performance Considerations**

1. **Async processing**: Firebase calls don't block main operations
2. **Batch metrics**: Send multiple metrics in batches when possible
3. **Error handling**: Never let monitoring failures affect business logic
4. **Resource limits**: Monitor Firebase usage and costs

---

## 🎯 **9. Production Deployment Checklist**

- [ ] Firebase project created and configured
- [ ] Environment variables set for Firebase credentials
- [ ] Monitoring configuration reviewed and tested
- [ ] @Timed annotations added to critical methods
- [ ] Real-time metrics collection tested
- [ ] Dashboard frontend deployed and connected
- [ ] Alert thresholds configured appropriately
- [ ] Performance impact assessed and acceptable
- [ ] Security rules configured for Firebase
- [ ] Monitoring documentation updated

**🎉 Your advanced monitoring system with @Timed and Firebase integration is ready for production!**
