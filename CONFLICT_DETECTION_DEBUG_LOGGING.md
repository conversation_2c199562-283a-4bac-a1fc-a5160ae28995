# Appointment Conflict Detection Debug Logging

## Overview

Comprehensive debug logging has been added to the appointment conflict detection system to help identify exactly what conflicts are being detected and why appointments are being rejected.

## How to Enable Debug Logging

To enable debug logging for conflict detection, uncomment the following lines in `src/main/resources/application.properties`:

```properties
# Conflict Detection Debug Logging - Enable for troubleshooting appointment scheduling conflicts
logging.level.ro.animaliaprogramari.animalia.domain.service.SchedulingConflictService=DEBUG
logging.level.ro.animaliaprogramari.animalia.domain.service.BlockTimeSchedulingService=DEBUG
logging.level.ro.animaliaprogramari.animalia.application.usecase.AppointmentManagementUseCaseImpl=DEBUG
logging.level.ro.animaliaprogramari.animalia.adapter.inbound.rest.SalonAppointmentController=DEBUG
```

## What Gets Logged

### 1. SalonAppointmentController
- **Request Details**: Complete appointment request information including salon ID, staff ID, date, time, services
- **Authentication**: User authentication status and user ID
- **Validation**: Client/pet validation results for new entities
- **Success/Failure**: Detailed error information for failed requests

### 2. AppointmentManagementUseCaseImpl
- **Business Rules Validation**: Step-by-step validation process
- **Working Hours Loading**: Salon and staff working hours configuration
- **Data Loading**: Existing appointments and block times count
- **Conflict Detection Results**: Number and types of conflicts found

### 3. SchedulingConflictService
- **Conflict Detection Process**: Complete flow for each appointment request
- **Salon Working Hours**: Detailed validation of salon open/closed status, working hours, breaks
- **Staff Working Hours**: Staff availability validation including holidays and custom closures
- **Appointment Overlaps**: Existing appointment conflict detection with time overlap details
- **Block Time Overlaps**: Block time conflict detection with detailed overlap information
- **Final Results**: Summary of all conflicts found or confirmation that slot is available

### 4. BlockTimeSchedulingService
- **Block Time Conflicts**: Detection of conflicts with existing block times
- **Appointment Conflicts**: Detection of conflicts with existing appointments
- **Detailed Overlap Analysis**: Exact time ranges and conflict reasons

## Log Message Format

All debug messages follow a structured format:

```
2024-01-15 10:30:45 [http-nio-8080-exec-1] DEBUG SchedulingConflictService - === CONFLICT DETECTION START ===
2024-01-15 10:30:45 [http-nio-8080-exec-1] DEBUG SchedulingConflictService - Appointment Request Details:
2024-01-15 10:30:45 [http-nio-8080-exec-1] DEBUG SchedulingConflictService -   - Date: 2024-01-16
2024-01-15 10:30:45 [http-nio-8080-exec-1] DEBUG SchedulingConflictService -   - Time: 10:00 - 11:00
2024-01-15 10:30:45 [http-nio-8080-exec-1] DEBUG SchedulingConflictService -   - Staff ID: staff-123
2024-01-15 10:30:45 [http-nio-8080-exec-1] DEBUG SchedulingConflictService -   - Salon ID: salon-456
```

## Conflict Types Logged

### 1. SALON_CLOSED
- Salon is closed on the requested date
- Requested time is outside salon working hours
- Salon break time conflicts

### 2. STAFF_UNAVAILABLE
- Staff is not available on the requested date
- Requested time is outside staff working hours
- Staff break time conflicts
- Staff holidays or custom closures

### 3. APPOINTMENT
- Overlapping existing appointments
- Time slot conflicts with confirmed appointments

### 4. BLOCK_TIME
- Conflicts with blocked time periods
- Staff-specific or salon-wide blocks

## Troubleshooting Common Issues

### Frontend Shows Available but Backend Rejects

1. **Enable debug logging** as described above
2. **Make the appointment request** from the frontend
3. **Check the logs** for the exact conflict detection flow
4. **Look for specific conflict types** and their details

### Example Debug Flow

```
=== APPOINTMENT CREATION REQUEST START ===
REST request to create appointment in salon: salon-123
Request details:
  - Staff ID: staff-456
  - Date: 2024-01-16
  - Time: 10:00 - 11:00

=== APPOINTMENT BUSINESS RULES VALIDATION START ===
--- Loading Working Hours and Schedules ---
Salon working hours loaded: true
Staff working hours loaded: true

--- Running Comprehensive Conflict Detection ---
=== CONFLICT DETECTION START ===
--- Checking Salon Working Hours ---
Salon working hours check PASSED
--- Checking Staff Working Hours ---
Staff working hours check PASSED
--- Checking Existing Appointment Overlaps ---
Found 1 existing appointments for staff staff-456 on date 2024-01-16
CONFLICT DETECTED: Appointment overlap found
  - Existing appointment: 09:30 - 10:30
  - Requested appointment: 10:00 - 11:00

=== CONFLICT DETECTION COMPLETE ===
Total conflicts found: 1
Conflict types: [APPOINTMENT]

VALIDATION FAILED: Conflicts detected for appointment request
```

## Performance Considerations

- Debug logging is **disabled by default** to avoid performance impact
- Only enable debug logging when troubleshooting specific issues
- Consider enabling logging for specific components only if needed
- Disable debug logging in production environments

## Log File Location

Logs are written to the console by default. To write to a file, add to `application.properties`:

```properties
logging.file.name=logs/animalia-debug.log
logging.file.max-size=10MB
logging.file.max-history=5
```
