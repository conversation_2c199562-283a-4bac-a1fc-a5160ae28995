-- Test data setup for BlockTimeAppointmentConflictTest
-- This file inserts the necessary test data directly into the database
-- to avoid JPA/Hibernate JSON serialization issues with H2

-- Insert test user
INSERT INTO users (id, firebase_uid, email, phone_number, name, role, is_active, created_at, updated_at)
VALUES (
    'test-staff-conflict',
    'firebase-test-staff-conflict',
    '<EMAIL>',
    '+40123456789',
    'Test Staff Member',
    'USER',
    true,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);

-- Insert test salon
INSERT INTO salons (id, name, address, city, phone, email, owner_id, client_ids, is_active, created_at, updated_at)
VALUES (
    'test-salon-conflict',
    'Test Salon for Conflict Testing',
    '123 Test Street',
    'Test City',
    '+40987654321',
    '<EMAIL>',
    'test-staff-conflict',
    'test-client-conflict',
    true,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);

-- Insert test staff record (with <PERSON>IE<PERSON>_GROOMER role for permissions)
INSERT INTO staff (id, user_id, nickname, salon_id, role, permissions, hire_date, is_active, created_at, updated_at)
VALUES (
    'staff-test-conflict',
    'test-staff-conflict',
    'Test Chief Groomer',
    'test-salon-conflict',
    'CHIEF_GROOMER',
    '{"clientDataAccess":"FULL","canManageAppointments":true,"canManageServices":true,"canViewReports":true,"canManageSchedule":true}',
    CURRENT_DATE,
    true,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);

-- Insert test client
INSERT INTO clients (id, salon_id, name, phone, email, address, notes, is_active, created_at, updated_at)
VALUES (
    'test-client-conflict',
    'test-salon-conflict',
    'Test Client',
    '+40111222333',
    '<EMAIL>',
    '456 Client Street',
    'Test client for conflict testing',
    true,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);

-- Insert test pet
INSERT INTO pets (id, client_id, name, breed, age, weight, color, gender, medical_conditions, notes, is_active, created_at, updated_at)
VALUES (
    'test-pet-conflict',
    'test-client-conflict',
    'Test Pet',
    'Golden Retriever',
    3,
    25.5,
    'Golden',
    'MALE',
    'None',
    'Test pet for conflict testing',
    true,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);

-- Insert test service
INSERT INTO services (id, salon_id, name, description, duration, price, category, display_order, requirements, is_active, created_at, updated_at)
VALUES (
    'test-service-conflict',
    'test-salon-conflict',
    'Test Grooming Service',
    'Basic grooming service for testing',
    60,
    50.00,
    'GROOMING',
    1,
    'None',
    true,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);

-- Insert test block time (will be updated by the test with actual dates)
-- This is just a placeholder that will be replaced by the test
INSERT INTO block_times (id, salon_id, start_time, end_time, reason, custom_reason, staff_ids, created_by, created_at, updated_by, updated_at, is_recurring, recurrence_pattern, notes, status)
VALUES (
    'test-block-conflict',
    'test-salon-conflict',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP,
    'PAUZA',
    NULL,
    'staff-test-conflict',
    'test-staff-conflict',
    CURRENT_TIMESTAMP,
    NULL,
    CURRENT_TIMESTAMP,
    false,
    NULL,
    'Test block time for conflict testing',
    'ACTIVE'
);
