-- H2-compatible schema for tests
-- This schema avoids PostgreSQL-specific features like arrays and JSONB

-- Users table
CREATE TABLE users (
    id VARCHAR(255) PRIMARY KEY,
    firebase_uid VARCHAR(255) NOT NULL UNIQUE,
    email VARCHAR(255) UNIQUE,
    phone_number VARCHAR(20),
    name VA<PERSON>HA<PERSON>(255) NOT NULL,
    role VARCHAR(50) DEFAULT 'USER',
    current_salon_id VARCHAR(255),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Salons table
CREATE TABLE salons (
    id VARCHAR(255) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    address TEXT,
    city VARCHAR(255),
    phone VARCHAR(50),
    email VARCHAR(255),
    owner_id VARCHAR(255) NOT NULL,
    client_ids TEXT, -- Store as comma-separated string instead of array
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Clients table
CREATE TABLE clients (
    id VARCHAR(255) PRIMARY KEY,
    salon_id VARCHAR(255),
    name VARCHAR(255) NOT NULL,
    phone VARCHAR(50),
    email VARCHAR(255),
    address TEXT,
    notes TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (salon_id) REFERENCES salons(id)
);

-- Pets table
CREATE TABLE pets (
    id VARCHAR(255) PRIMARY KEY,
    client_id VARCHAR(255) NOT NULL,
    name VARCHAR(255) NOT NULL,
    breed VARCHAR(255),
    species VARCHAR(255),
    size VARCHAR(3) DEFAULT 'M',
    age INTEGER,
    weight DECIMAL(5,2),
    color VARCHAR(255),
    gender VARCHAR(20),
    medical_conditions TEXT,
    notes TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES clients(id)
);

-- Groomers table removed - replaced by Staff system

-- Services table
CREATE TABLE services (
    id VARCHAR(255) PRIMARY KEY,
    salon_id VARCHAR(255) NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    duration INTEGER NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    category VARCHAR(100),
    display_order INTEGER DEFAULT 0,
    requirements TEXT,
    is_active BOOLEAN DEFAULT true,
    -- Variable pricing fields (H2 compatible)
    size_prices TEXT, -- JSON as TEXT for H2 compatibility
    size_durations TEXT, -- JSON as TEXT for H2 compatibility
    -- Min-max pricing fields
    min_price DECIMAL(10,2),
    max_price DECIMAL(10,2),
    size_min_prices TEXT, -- JSON as TEXT for H2 compatibility
    size_max_prices TEXT, -- JSON as TEXT for H2 compatibility
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (salon_id) REFERENCES salons(id)
);

-- Service requirements table (for @ElementCollection mapping)
CREATE TABLE service_requirements (
    service_id VARCHAR(255) NOT NULL,
    requirement VARCHAR(255) NOT NULL,
    FOREIGN KEY (service_id) REFERENCES services(id)
);

-- Staff table (replaces user_salon_associations) - MOVED BEFORE appointments
CREATE TABLE staff (
    id VARCHAR(255) PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    nickname VARCHAR(255),
    salon_id VARCHAR(255) NOT NULL,
    role VARCHAR(50) NOT NULL DEFAULT 'GROOMER',
    permissions TEXT, -- JSON representation of StaffPermissions
    working_hours TEXT, -- JSON representation for H2 compatibility
    hire_date DATE NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (salon_id) REFERENCES salons(id),
    UNIQUE(user_id, salon_id)
);

-- Staff specializations table
CREATE TABLE staff_specializations (
    staff_id VARCHAR(255) NOT NULL,
    specialization VARCHAR(50) NOT NULL,
    FOREIGN KEY (staff_id) REFERENCES staff(id),
    PRIMARY KEY (staff_id, specialization)
);

-- Appointments table
CREATE TABLE appointments (
    id VARCHAR(255) PRIMARY KEY,
    salon_id VARCHAR(255) NOT NULL,
    client_id VARCHAR(255) NOT NULL,
    pet_id VARCHAR(255) NOT NULL,
    staff_id VARCHAR(255) NOT NULL,
    appointment_date DATE NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    status VARCHAR(50) DEFAULT 'SCHEDULED',
    total_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    total_duration INTEGER NOT NULL DEFAULT 0,
    notes TEXT,
    repetition_frequency VARCHAR(50),
    version INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (salon_id) REFERENCES salons(id),
    FOREIGN KEY (client_id) REFERENCES clients(id),
    FOREIGN KEY (pet_id) REFERENCES pets(id),
    FOREIGN KEY (staff_id) REFERENCES staff(id)
);

-- Appointment service IDs table (for @ElementCollection mapping)
CREATE TABLE appointment_service_ids (
    appointment_id VARCHAR(255) NOT NULL,
    service_id VARCHAR(255) NOT NULL,
    FOREIGN KEY (appointment_id) REFERENCES appointments(id)
);

-- Appointment services junction table (legacy - may be removed later)
CREATE TABLE appointment_services (
    appointment_id VARCHAR(255) NOT NULL,
    service_id VARCHAR(255) NOT NULL,
    PRIMARY KEY (appointment_id, service_id),
    FOREIGN KEY (appointment_id) REFERENCES appointments(id),
    FOREIGN KEY (service_id) REFERENCES services(id)
);

-- Groomer clients junction table removed - replaced by Staff system

-- Salon invitations table (updated for Staff system)
CREATE TABLE salon_invitations (
    id VARCHAR(255) PRIMARY KEY,
    salon_id VARCHAR(255) NOT NULL,
    inviter_user_id VARCHAR(255) NOT NULL,
    invited_user_phone VARCHAR(20) NOT NULL,
    proposed_role VARCHAR(50) NOT NULL,
    proposed_permissions TEXT NOT NULL, -- JSON representation of StaffPermissions
    status VARCHAR(50) DEFAULT 'PENDING',
    message TEXT,
    invited_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    responded_at TIMESTAMP,
    expires_at TIMESTAMP,
    resend_count INTEGER NOT NULL DEFAULT 0,
    last_resend_at TIMESTAMP,
    cancelled_at TIMESTAMP,
    cancelled_by VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (salon_id) REFERENCES salons(id),
    FOREIGN KEY (inviter_user_id) REFERENCES users(id),
    FOREIGN KEY (cancelled_by) REFERENCES users(id)
);

-- Salon settings table
CREATE TABLE salon_settings (
    id VARCHAR(255) PRIMARY KEY,
    salon_id VARCHAR(255) NOT NULL UNIQUE,
    working_hours TEXT, -- Store as JSON string
    appointment_duration_default INTEGER DEFAULT 60,
    break_duration_default INTEGER DEFAULT 15,
    advance_booking_days INTEGER DEFAULT 30,
    cancellation_hours INTEGER DEFAULT 24,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (salon_id) REFERENCES salons(id)
);

-- FCM tokens table
CREATE TABLE fcm_tokens (
    id VARCHAR(255) PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    salon_id VARCHAR(255) NOT NULL,
    fcm_token VARCHAR(500) NOT NULL,
    device_id VARCHAR(255),
    device_type VARCHAR(50) NOT NULL DEFAULT 'MOBILE',
    is_active BOOLEAN DEFAULT true,
    last_used TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (salon_id) REFERENCES salons(id)
);

-- Notification settings table
CREATE TABLE notification_settings (
    id VARCHAR(255) PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    salon_id VARCHAR(255) NOT NULL,
    sms_enabled BOOLEAN DEFAULT true,
    push_enabled BOOLEAN DEFAULT true,
    email_enabled BOOLEAN DEFAULT true,
    sound_preference VARCHAR(50) DEFAULT 'DEFAULT',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (salon_id) REFERENCES salons(id)
);

-- SMS reminder settings table
CREATE TABLE sms_reminder_settings (
    id VARCHAR(255) PRIMARY KEY,
    salon_id VARCHAR(255) NOT NULL,
    enabled BOOLEAN DEFAULT true,
    hours_before INTEGER DEFAULT 24,
    message_template TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (salon_id) REFERENCES salons(id)
);

-- Staff working hours settings table (for individual staff members)
CREATE TABLE staff_working_hours (
    id VARCHAR(255) PRIMARY KEY,
    staff_id VARCHAR(255) NOT NULL,
    salon_id VARCHAR(255) NOT NULL,
    weekly_schedule TEXT, -- JSON representation
    holidays TEXT, -- JSON representation
    custom_closures TEXT, -- JSON representation
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (staff_id) REFERENCES staff(id),
    FOREIGN KEY (salon_id) REFERENCES salons(id)
);

-- Salon working hours settings table (for salon-wide settings)
CREATE TABLE salon_working_hours_settings (
    salon_id VARCHAR(255) PRIMARY KEY,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (salon_id) REFERENCES salons(id)
);

-- Staff weekly schedule table (for individual staff members)
CREATE TABLE weekly_schedules (
    id VARCHAR(255) PRIMARY KEY,
    staff_id VARCHAR(255) NOT NULL,
    salon_id VARCHAR(255) NOT NULL,
    day_of_week VARCHAR(20) NOT NULL,
    is_working_day BOOLEAN DEFAULT false,
    start_time TIME,
    end_time TIME,
    break_start_time TIME,
    break_end_time TIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (staff_id) REFERENCES staff(id),
    FOREIGN KEY (salon_id) REFERENCES salons(id)
);

-- Salon weekly schedule table (for salon-wide settings)
CREATE TABLE weekly_schedule (
    id VARCHAR(255) PRIMARY KEY,
    salon_id VARCHAR(255) NOT NULL,
    day_of_week VARCHAR(20) NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    is_day_off BOOLEAN DEFAULT false,
    lunch_break_enabled BOOLEAN DEFAULT false,
    lunch_break_start_time TIME,
    lunch_break_end_time TIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(salon_id, day_of_week),
    FOREIGN KEY (salon_id) REFERENCES salons(id)
);

-- Holidays table
CREATE TABLE holidays (
    id VARCHAR(255) PRIMARY KEY,
    salon_id VARCHAR(255) NOT NULL,
    name VARCHAR(255) NOT NULL,
    type VARCHAR(50), -- Added missing type column
    date DATE NOT NULL,
    is_working_day BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (salon_id) REFERENCES salons(id)
);

-- Custom closures table
CREATE TABLE custom_closures (
    id VARCHAR(255) PRIMARY KEY,
    salon_id VARCHAR(255) NOT NULL,
    reason VARCHAR(255) NOT NULL,
    description TEXT, -- Added missing description column
    date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (salon_id) REFERENCES salons(id)
);

-- Block times table (H2-compatible version)
CREATE TABLE block_times (
    id VARCHAR(255) PRIMARY KEY,
    salon_id VARCHAR(255) NOT NULL,
    start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    end_time TIMESTAMP WITH TIME ZONE NOT NULL,
    reason VARCHAR(50) NOT NULL,
    custom_reason TEXT,
    staff_ids TEXT NOT NULL, -- Store as comma-separated string instead of array
    created_by VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(255),
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_recurring BOOLEAN DEFAULT FALSE,
    recurrence_pattern TEXT, -- JSON as TEXT for H2 compatibility
    notes TEXT,
    status VARCHAR(20) DEFAULT 'ACTIVE',
    FOREIGN KEY (salon_id) REFERENCES salons(id),
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (updated_by) REFERENCES users(id)
);

-- Indexes for performance
CREATE INDEX idx_clients_phone ON clients(phone);
CREATE INDEX idx_clients_email ON clients(email);
CREATE INDEX idx_pets_client_id ON pets(client_id);
CREATE INDEX idx_appointments_date ON appointments(appointment_date);
CREATE INDEX idx_appointments_staff_date ON appointments(staff_id, appointment_date);
CREATE INDEX idx_appointments_client ON appointments(client_id);
CREATE INDEX idx_appointments_salon ON appointments(salon_id);
CREATE INDEX idx_users_firebase_uid ON users(firebase_uid);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_staff_user_id ON staff(user_id);
CREATE INDEX idx_staff_salon_id ON staff(salon_id);
CREATE INDEX idx_staff_active ON staff(is_active);
CREATE INDEX idx_salon_invitations_salon_id ON salon_invitations(salon_id);
CREATE INDEX idx_salon_invitations_phone ON salon_invitations(invited_user_phone);
CREATE INDEX idx_fcm_tokens_user_id ON fcm_tokens(user_id);
CREATE INDEX idx_fcm_tokens_salon_id ON fcm_tokens(salon_id);
CREATE INDEX idx_fcm_tokens_active ON fcm_tokens(is_active);
CREATE INDEX idx_services_salon_id ON services(salon_id);
CREATE INDEX idx_notification_settings_user_salon ON notification_settings(user_id, salon_id);
CREATE INDEX idx_staff_working_hours_staff_salon ON staff_working_hours(staff_id, salon_id);
CREATE INDEX idx_weekly_schedules_staff ON weekly_schedules(staff_id);
CREATE INDEX idx_weekly_schedule_salon ON weekly_schedule(salon_id, day_of_week);
CREATE INDEX idx_block_times_salon_id ON block_times(salon_id);
CREATE INDEX idx_block_times_time_range ON block_times(start_time, end_time);
CREATE INDEX idx_block_times_status ON block_times(status);
CREATE INDEX idx_holidays_salon_date ON holidays(salon_id, date);
CREATE INDEX idx_custom_closures_salon_date ON custom_closures(salon_id, date);
