package ro.animaliaprogramari.animalia.test

import io.mockk.every
import io.mockk.mockk
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.domain.validation.ValidationResult
import java.time.LocalDate
import java.time.LocalTime
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

/**
 * Test utilities and helper functions for testing
 * Provides common assertions, mock setups, and test data validation
 */
object TestUtils {

    // ===== ASSERTION HELPERS =====

    /**
     * Assert that an appointment has the expected basic properties
     */
    fun assertAppointmentBasics(
        appointment: Appointment,
        expectedSalonId: SalonId,
        expectedClientId: ClientId,
        expectedPetId: PetId,
        expectedStaffId: StaffId
    ) {
        assertNotNull(appointment)
        assertEquals(expectedSalonId, appointment.salonId)
        assertEquals(expectedClientId, appointment.clientId)
        assertEquals(expectedPetId, appointment.petId)
        assertEquals(expectedStaffId, appointment.staffId)
        assertNotNull(appointment.id)
        assertNotNull(appointment.createdAt)
        assertNotNull(appointment.updatedAt)
    }

    /**
     * Assert that an appointment has the expected time slot
     */
    fun assertAppointmentTimeSlot(
        appointment: Appointment,
        expectedDate: LocalDate,
        expectedStartTime: LocalTime,
        expectedEndTime: LocalTime
    ) {
        assertEquals(expectedDate, appointment.appointmentDate)
        assertEquals(expectedStartTime, appointment.startTime)
        assertEquals(expectedEndTime, appointment.endTime)
    }

    /**
     * Assert that an appointment has the expected status
     */
    fun assertAppointmentStatus(appointment: Appointment, expectedStatus: AppointmentStatus) {
        assertEquals(expectedStatus, appointment.status)
    }

    /**
     * Assert that an appointment has the expected services
     */
    fun assertAppointmentServices(appointment: Appointment, expectedServiceIds: List<ServiceId>) {
        assertEquals(expectedServiceIds.size, appointment.serviceIds.size)
        assertTrue(appointment.serviceIds.containsAll(expectedServiceIds))
    }

    // ===== MOCK HELPERS =====

    /**
     * Create a mock ValidationResult that passes validation
     */
    fun mockValidValidationResult(): ValidationResult {
        return mockk<ValidationResult> {
            every { throwIfInvalid() } returns Unit
            every { isValid } returns true
            every { errors } returns emptyList()
        }
    }

    /**
     * Create a mock ValidationResult that fails validation
     */
    fun mockInvalidValidationResult(errorMessage: String): ValidationResult {
        return mockk<ValidationResult> {
            every { throwIfInvalid() } throws IllegalArgumentException(errorMessage)
            every { isValid } returns false
            every { errors } returns listOf(errorMessage)
        }
    }

    // ===== TIME HELPERS =====

    /**
     * Create a future date for testing (avoids past date validation issues)
     */
    fun futureDate(daysFromNow: Long = 1): LocalDate {
        return LocalDate.now().plusDays(daysFromNow)
    }

    /**
     * Create a past date for testing
     */
    fun pastDate(daysAgo: Long = 1): LocalDate {
        return LocalDate.now().minusDays(daysAgo)
    }

    /**
     * Create standard business hours time slots
     */
    object BusinessHours {
        val MORNING_START = LocalTime.of(9, 0)
        val MORNING_END = LocalTime.of(12, 0)
        val AFTERNOON_START = LocalTime.of(13, 0)
        val AFTERNOON_END = LocalTime.of(17, 0)
        val EVENING_START = LocalTime.of(18, 0)
        val EVENING_END = LocalTime.of(20, 0)
    }

    /**
     * Check if two time slots overlap
     */
    fun timeSlotsOverlap(
        start1: LocalTime, end1: LocalTime,
        start2: LocalTime, end2: LocalTime
    ): Boolean {
        return start1.isBefore(end2) && start2.isBefore(end1)
    }

    // ===== DATA VALIDATION HELPERS =====

    /**
     * Validate that a client has required fields
     */
    fun validateClientData(client: Client) {
        assertNotNull(client.id)
        assertTrue(client.name.isNotBlank(), "Client name should not be blank")
        assertNotNull(client.createdAt)
        assertNotNull(client.updatedAt)
    }

    /**
     * Validate that a pet has required fields
     */
    fun validatePetData(pet: Pet) {
        assertNotNull(pet.id)
        assertNotNull(pet.clientId)
        assertTrue(pet.name.isNotBlank(), "Pet name should not be blank")
        assertNotNull(pet.createdAt)
        assertNotNull(pet.updatedAt)
    }

    /**
     * Validate that a user has required fields
     */
    fun validateUserData(user: User) {
        assertNotNull(user.id)
        assertTrue(user.firebaseUid.isNotBlank(), "Firebase UID should not be blank")
        assertTrue(user.name.isNotBlank(), "User name should not be blank")
        assertNotNull(user.role)
        assertNotNull(user.createdAt)
        assertNotNull(user.updatedAt)
    }

    /**
     * Validate that a salon has required fields
     */
    fun validateSalonData(salon: Salon) {
        assertNotNull(salon.id)
        assertTrue(salon.name.isNotBlank(), "Salon name should not be blank")
        assertNotNull(salon.createdAt)
        assertNotNull(salon.updatedAt)
    }

    /**
     * Validate that a salon service has required fields
     */
    fun validateSalonServiceData(service: SalonService) {
        assertNotNull(service.id)
        assertNotNull(service.salonId)
        assertTrue(service.name.isNotBlank(), "Service name should not be blank")
        assertNotNull(service.basePrice)
        assertTrue(service.basePrice.amount.signum() >= 0, "Service price should not be negative")
        assertNotNull(service.duration)
        assertNotNull(service.createdAt)
        assertNotNull(service.updatedAt)
    }

    // ===== TEST SCENARIO BUILDERS =====

    /**
     * Create a complete appointment scenario with all related entities
     */
    fun createCompleteAppointmentScenario(): AppointmentScenario {
        val salonId = SalonId.generate()
        val clientId = ClientId.generate()
        val petId = PetId.generate()
        val staffId = StaffId.generate()
        val userId = UserId.generate()
        val serviceId = ServiceId.generate()

        return AppointmentScenario(
            salon = TestDataBuilder.aSalon().withId(salonId).build(),
            client = TestDataBuilder.aClient().withId(clientId).build(),
            pet = TestDataBuilder.aPet().withId(petId).withClientId(clientId).build(),
            staff = TestDataBuilder.aUser().withId(userId).withRole(UserRole.STAFF).build(),
            staffMember = TestDataBuilder.aStaff()
                .withUserId(userId)
                .withSalonId(salonId)
                .withActive(true)
                .build(),
            service = TestDataBuilder.aSalonService()
                .withId(serviceId)
                .withSalonId(salonId)
                .withActive(true)
                .build(),
            appointment = TestDataBuilder.anAppointment()
                .withSalonId(salonId)
                .withClientId(clientId)
                .withPetId(petId)
                .withStaffId(staffId)
                .withServiceIds(listOf(serviceId))
                .build()
        )
    }

    /**
     * Create a scenario for testing appointment conflicts
     */
    fun createConflictingAppointmentScenario(): ConflictScenario {
        val staffId = StaffId.generate()
        val date = futureDate()
        
        val appointment1 = TestDataBuilder.anAppointment()
            .withStaffId(staffId)
            .withDate(date)
            .withTimeSlot(LocalTime.of(10, 0), LocalTime.of(11, 0))
            .withStatus(AppointmentStatus.SCHEDULED)
            .build()

        val appointment2 = TestDataBuilder.anAppointment()
            .withStaffId(staffId)
            .withDate(date)
            .withTimeSlot(LocalTime.of(10, 30), LocalTime.of(11, 30)) // Overlaps with appointment1
            .withStatus(AppointmentStatus.SCHEDULED)
            .build()

        return ConflictScenario(
            staffId = staffId,
            date = date,
            existingAppointment = appointment1,
            conflictingAppointment = appointment2
        )
    }

    // ===== ERROR MESSAGE HELPERS =====

    /**
     * Common error message patterns for testing
     */
    object ErrorMessages {
        const val ENTITY_NOT_FOUND = "not found"
        const val UNAUTHORIZED = "does not have permission"
        const val BUSINESS_RULE_VIOLATION = "business rule"
        const val VALIDATION_ERROR = "validation"
        const val SALON_MISMATCH = "does not belong to the specified salon"
        const val STAFF_INACTIVE = "Staff member is not active"
        const val SERVICE_INACTIVE = "is not active"
        const val TIME_CONFLICT = "conflicts with existing appointment"
    }

    /**
     * Assert that an exception message contains expected text
     */
    fun assertExceptionMessage(exception: Exception, expectedText: String) {
        assertNotNull(exception.message)
        assertTrue(
            exception.message!!.contains(expectedText, ignoreCase = true),
            "Expected exception message to contain '$expectedText', but was: '${exception.message}'"
        )
    }
}

/**
 * Data class representing a complete appointment test scenario
 */
data class AppointmentScenario(
    val salon: Salon,
    val client: Client,
    val pet: Pet,
    val staff: User,
    val staffMember: Staff,
    val service: SalonService,
    val appointment: Appointment
)

/**
 * Data class representing a conflict scenario for testing
 */
data class ConflictScenario(
    val staffId: StaffId,
    val date: LocalDate,
    val existingAppointment: Appointment,
    val conflictingAppointment: Appointment
)
