package ro.animaliaprogramari.animalia.domain.service

import ro.animaliaprogramari.animalia.domain.model.Email
import ro.animaliaprogramari.animalia.domain.model.*
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue

class AuthenticationServiceTest {
    private val service = AuthenticationService()

    @Test
    fun `determineUserRole should return ADMIN for admin email`() {
        val role = service.determineUserRole(Email.of("<EMAIL>"), null, null)
        assertEquals(UserRole.ADMIN, role)
    }

    @Test
    fun `validateUserForAuthentication should detect inactive user`() {
        val user = User(
            id = UserId.generate(),
            firebaseUid = "firebase",
            email = Email.of("<EMAIL>"),
            phoneNumber = null,
            name = "Name",
            role = UserRole.USER,
            currentSalonId = null,
            isActive = false
        )
        val result = service.validateUserForAuthentication(user)
        assertTrue(result is AuthenticationValidationResult.UserInactive)
    }
}
