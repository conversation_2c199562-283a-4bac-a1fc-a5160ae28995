package ro.animaliaprogramari.animalia.domain.service

import org.junit.jupiter.api.Test
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.testutil.StaffWorkingHoursTestDataBuilder
import ro.animaliaprogramari.animalia.testutil.TestDataBuilder
import java.time.*
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

class SchedulingConflictServiceTest {
    private val service = SchedulingConflictService()
    private val salonId = SalonId.of("salon-1")
    private val staffId = StaffId.of("staff-1")

    private fun defaultSalonHours(): WorkingHoursSettings =
        WorkingHoursSettings.createDefault(salonId)

    private fun defaultStaffHours(): StaffWorkingHoursSettings =
        StaffWorkingHoursTestDataBuilder.aStaffWorkingHoursSettings(staffId, salonId).build()

    @Test
    fun `detect salon closed conflict`() {
        val date = LocalDate.now().with(DayOfWeek.SUNDAY)
        val conflicts = service.detectConflicts(
            date,
            LocalTime.of(10,0),
            LocalTime.of(11,0),
            staffId,
            defaultSalonHours(),
            defaultStaffHours(),
            emptyList(),
            emptyList()
        )
        assertTrue(conflicts.any { it.type == ScheduleConflictType.SALON_CLOSED })
    }

    @Test
    fun `detect staff unavailable conflict`() {
        val date = LocalDate.now().with(DayOfWeek.MONDAY)
        val staffHours = StaffWorkingHoursTestDataBuilder
            .aStaffWorkingHoursSettings(staffId, salonId)
            .withWorkingDays(DayOfWeek.TUESDAY) // Monday off
            .build()

        val conflicts = service.detectConflicts(
            date,
            LocalTime.of(10,0),
            LocalTime.of(11,0),
            staffId,
            defaultSalonHours(),
            staffHours,
            emptyList(),
            emptyList()
        )
        assertEquals(listOf(ScheduleConflict(ScheduleConflictType.STAFF_UNAVAILABLE)), conflicts)
    }

    @Test
    fun `detect appointment overlap`() {
        val date = LocalDate.now().with(DayOfWeek.MONDAY)
        val appointment = TestDataBuilder.anAppointment(
            staffId = staffId,
            salonId = salonId,
            appointmentDate = date,
            startTime = LocalTime.of(9,30),
            endTime = LocalTime.of(10,30)
        )
        val conflicts = service.detectConflicts(
            date,
            LocalTime.of(10,0),
            LocalTime.of(11,0),
            staffId,
            defaultSalonHours(),
            defaultStaffHours(),
            listOf(appointment),
            emptyList()
        )
        assertEquals(listOf(ScheduleConflict(ScheduleConflictType.APPOINTMENT)), conflicts)
    }

    @Test
    fun `detect block time overlap`() {
        val date = LocalDate.now().with(DayOfWeek.MONDAY)
        val block = TestDataBuilder.aBlockTime(
            salonId = salonId,
            startTime = date.atTime(9,45).atZone(ZoneId.systemDefault()),
            endTime = date.atTime(10,15).atZone(ZoneId.systemDefault()),
            staffIds = setOf(staffId)
        )
        val conflicts = service.detectConflicts(
            date,
            LocalTime.of(10,0),
            LocalTime.of(11,0),
            staffId,
            defaultSalonHours(),
            defaultStaffHours(),
            emptyList(),
            listOf(block)
        )
        assertEquals(listOf(ScheduleConflict(ScheduleConflictType.BLOCK_TIME)), conflicts)
    }

    @Test
    fun `slot available when no conflicts`() {
        val date = LocalDate.now().with(DayOfWeek.MONDAY)
        val available = service.isSlotAvailable(
            date,
            LocalTime.of(13,30),
            LocalTime.of(14,30),
            staffId,
            defaultSalonHours(),
            defaultStaffHours(),
            emptyList(),
            emptyList()
        )
        assertTrue(available)
    }

    @Test
    fun `slot unavailable when conflicts exist`() {
        val date = LocalDate.now().with(DayOfWeek.MONDAY)
        val appointment = TestDataBuilder.anAppointment(
            staffId = staffId,
            salonId = salonId,
            appointmentDate = date,
            startTime = LocalTime.of(13,0),
            endTime = LocalTime.of(14,0)
        )
        val available = service.isSlotAvailable(
            date,
            LocalTime.of(13,0),
            LocalTime.of(14,0),
            staffId,
            defaultSalonHours(),
            defaultStaffHours(),
            listOf(appointment),
            emptyList()
        )
        assertFalse(available)
    }
}
