package ro.animaliaprogramari.animalia.domain.service

import org.junit.jupiter.api.Test
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import ro.animaliaprogramari.animalia.domain.model.Money
import ro.animaliaprogramari.animalia.domain.model.Duration
import kotlin.test.assertFalse
import kotlin.test.assertTrue

/**
 * Unit tests for ServicePricingValidationService
 */
@DisplayName("ServicePricingValidationService")
class ServicePricingValidationServiceTest {

    private val validationService = ServicePricingValidationService()

    @Nested
    @DisplayName("Fixed Pricing Validation")
    inner class FixedPricingValidation {

        @Test
        fun `should validate fixed pricing successfully`() {
            // Given
            val basePrice = Money.of(50.0)
            val baseDuration = Duration.ofMinutes(60)

            // When
            val result = validationService.validatePricingConfiguration(
                basePrice = basePrice,
                baseDuration = baseDuration
            )

            // Then
            assertTrue(result.isValid)
        }

        @Test
        fun `should validate fixed pricing with min-max successfully`() {
            // Given
            val basePrice = Money.of(50.0)
            val baseDuration = Duration.ofMinutes(60)
            val minPrice = Money.of(40.0)
            val maxPrice = Money.of(60.0)

            // When
            val result = validationService.validatePricingConfiguration(
                basePrice = basePrice,
                baseDuration = baseDuration,
                minPrice = minPrice,
                maxPrice = maxPrice
            )

            // Then
            assertTrue(result.isValid)
        }

        @Test
        fun `should fail when min price is greater than max price`() {
            // Given
            val basePrice = Money.of(50.0)
            val baseDuration = Duration.ofMinutes(60)
            val minPrice = Money.of(60.0)
            val maxPrice = Money.of(40.0)

            // When
            val result = validationService.validatePricingConfiguration(
                basePrice = basePrice,
                baseDuration = baseDuration,
                minPrice = minPrice,
                maxPrice = maxPrice
            )

            // Then
            assertFalse(result.isValid)
            assertTrue(result.errors.any { it.contains("Min price cannot be greater than max price") })
        }
    }

    @Nested
    @DisplayName("Variable Pricing Validation")
    inner class VariablePricingValidation {

        @Test
        fun `should validate variable pricing successfully`() {
            // Given
            val basePrice = Money.of(50.0)
            val baseDuration = Duration.ofMinutes(60)
            val sizePrices = mapOf(
                "S" to Money.of(30.0),
                "M" to Money.of(50.0),
                "L" to Money.of(70.0)
            )
            val sizeDurations = mapOf(
                "S" to Duration.ofMinutes(45),
                "M" to Duration.ofMinutes(60),
                "L" to Duration.ofMinutes(90)
            )

            // When
            val result = validationService.validatePricingConfiguration(
                basePrice = basePrice,
                baseDuration = baseDuration,
                sizePrices = sizePrices,
                sizeDurations = sizeDurations
            )

            // Then
            assertTrue(result.isValid)
        }

        @Test
        fun `should fail when size prices and durations have different keys`() {
            // Given
            val basePrice = Money.of(50.0)
            val baseDuration = Duration.ofMinutes(60)
            val sizePrices = mapOf(
                "S" to Money.of(30.0),
                "M" to Money.of(50.0)
            )
            val sizeDurations = mapOf(
                "S" to Duration.ofMinutes(45),
                "L" to Duration.ofMinutes(90)
            )

            // When
            val result = validationService.validatePricingConfiguration(
                basePrice = basePrice,
                baseDuration = baseDuration,
                sizePrices = sizePrices,
                sizeDurations = sizeDurations
            )

            // Then
            assertFalse(result.isValid)
            assertTrue(result.errors.any { it.contains("Size prices and durations must have the same keys") })
        }
    }

    @Nested
    @DisplayName("Size-based Min-Max Pricing Validation")
    inner class SizeBasedMinMaxPricingValidation {

        @Test
        fun `should validate size-based min-max pricing successfully`() {
            // Given
            val basePrice = Money.of(50.0)
            val baseDuration = Duration.ofMinutes(60)
            val sizeMinPrices = mapOf(
                "S" to Money.of(25.0),
                "M" to Money.of(45.0),
                "L" to Money.of(65.0)
            )
            val sizeMaxPrices = mapOf(
                "S" to Money.of(35.0),
                "M" to Money.of(55.0),
                "L" to Money.of(75.0)
            )

            // When
            val result = validationService.validatePricingConfiguration(
                basePrice = basePrice,
                baseDuration = baseDuration,
                sizeMinPrices = sizeMinPrices,
                sizeMaxPrices = sizeMaxPrices
            )

            // Then
            assertTrue(result.isValid)
        }

        @Test
        fun `should fail when size min price is greater than max price for a size`() {
            // Given
            val basePrice = Money.of(50.0)
            val baseDuration = Duration.ofMinutes(60)
            val sizeMinPrices = mapOf(
                "S" to Money.of(35.0),
                "M" to Money.of(45.0),
                "L" to Money.of(75.0) // Greater than max
            )
            val sizeMaxPrices = mapOf(
                "S" to Money.of(35.0),
                "M" to Money.of(55.0),
                "L" to Money.of(65.0) // Less than min
            )

            // When
            val result = validationService.validatePricingConfiguration(
                basePrice = basePrice,
                baseDuration = baseDuration,
                sizeMinPrices = sizeMinPrices,
                sizeMaxPrices = sizeMaxPrices
            )

            // Then
            assertFalse(result.isValid)
            assertTrue(result.errors.any { it.contains("Min price for size L cannot be greater than max price") })
        }
    }

    @Nested
    @DisplayName("Pet Size Validation")
    inner class PetSizeValidation {

        @Test
        fun `should validate supported pet sizes successfully`() {
            // Given
            val sizes = setOf("S", "M", "L")

            // When
            val result = validationService.validatePetSizes(sizes)

            // Then
            assertTrue(result.isValid)
        }

        @Test
        fun `should fail for unsupported pet sizes`() {
            // Given
            val sizes = setOf("S", "M", "XXL")

            // When
            val result = validationService.validatePetSizes(sizes)

            // Then
            assertFalse(result.isValid)
            assertTrue(result.errors.any { it.contains("Unsupported pet size: XXL") })
        }
    }
}
