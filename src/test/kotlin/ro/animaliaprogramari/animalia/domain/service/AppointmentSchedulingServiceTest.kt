package ro.animaliaprogramari.animalia.domain.service

import ro.animaliaprogramari.animalia.domain.exception.StaffUnavailableException
import ro.animaliaprogramari.animalia.domain.exception.TimeSlotUnavailableException
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.testutil.TestDataBuilder
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.LocalTime
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertFailsWith

class AppointmentSchedulingServiceTest {
    private val service = AppointmentSchedulingService()


    @Test
    fun `validateTimeSlotAvailability should detect overlap`() {
        val staff = TestDataBuilder.aStaff()
        val existing = listOf(
            TestDataBuilder.anAppointment(
                staffId = staff.id,
                appointmentDate = LocalDate.now().plusDays(1),
                startTime = LocalTime.of(9,0),
                endTime = LocalTime.of(10,0)
            )
        )
        assertFailsWith<TimeSlotUnavailableException> {
            service.validateTimeSlotAvailability(
                staff.id,
                LocalDate.now().plusDays(1),
                LocalTime.of(9,30),
                LocalTime.of(10,30),
                existing
            )
        }
    }

    @Test
    fun `calculateRecommendedDuration adds buffer`() {
        val s1 = SalonService(ServiceId.of("1"), SalonId.of("salon-1"), "Wash", null, Money.of(20.0), Duration.ofMinutes(30), ServiceCategory.GROOMING)
        val result = service.calculateRecommendedDuration(listOf(s1))
        assertEquals(Duration.ofMinutes(45), result)
    }
}
