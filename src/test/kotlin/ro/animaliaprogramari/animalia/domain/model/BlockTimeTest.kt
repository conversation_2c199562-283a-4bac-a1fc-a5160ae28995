package ro.animaliaprogramari.animalia.domain.model

import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import ro.animaliaprogramari.animalia.testutil.TestDataBuilder
import ro.animaliaprogramari.animalia.testutil.TestDataBuilder.blockTime
import java.time.ZonedDateTime

/**
 * Comprehensive unit tests for BlockTime domain model
 * Tests business rules, validation, and behavior
 */
@DisplayName("BlockTime Domain Model")
class BlockTimeTest {

    @Nested
    @DisplayName("Creation and Validation")
    inner class CreationAndValidation {

        @Test
        fun `should create valid block time with required fields`() {
            // Given
            val startTime = ZonedDateTime.now().plusHours(1)
            val endTime = startTime.plusHours(2)
            val staffIds = setOf(StaffId.of("staff-1"), StaffId.of("staff-2"))

            // When
            val blockTime = TestDataBuilder.aBlockTime(
                startTime = startTime,
                endTime = endTime,
                reason = BlockReason.PAUZA,
                staffIds = staffIds
            )

            // Then
            assertEquals(startTime, blockTime.startTime)
            assertEquals(endTime, blockTime.endTime)
            assertEquals(BlockReason.PAUZA, blockTime.reason)
            assertEquals(staffIds, blockTime.staffIds)
            assertEquals(120, blockTime.getDurationMinutes())
            assertTrue(blockTime.isActive())
        }

        @Test
        fun `should throw exception when end time is before start time`() {
            // Given
            val startTime = ZonedDateTime.now().plusHours(2)
            val endTime = startTime.minusHours(1) // Before start time

            // When & Then
            val exception = assertThrows<IllegalArgumentException> {
                TestDataBuilder.aBlockTime(
                    startTime = startTime,
                    endTime = endTime
                )
            }
            assertEquals("End time must be after start time", exception.message)
        }

        @Test
        fun `should throw exception when staff IDs are empty`() {
            // When & Then
            val exception = assertThrows<IllegalArgumentException> {
                TestDataBuilder.aBlockTime(staffIds = emptySet())
            }
            assertEquals("At least one staff member must be specified", exception.message)
        }

        @Test
        fun `should throw exception when duration is less than 15 minutes`() {
            // Given
            val startTime = ZonedDateTime.now().plusHours(1)
            val endTime = startTime.plusMinutes(10) // Only 10 minutes

            // When & Then
            val exception = assertThrows<IllegalArgumentException> {
                TestDataBuilder.aBlockTime(
                    startTime = startTime,
                    endTime = endTime
                )
            }
            assertEquals("Block duration must be at least 15 minutes", exception.message)
        }

        @Test
        fun `should throw exception when duration exceeds 12 hours`() {
            // Given
            val startTime = ZonedDateTime.now().plusHours(1)
            val endTime = startTime.plusHours(13) // 13 hours

            // When & Then
            val exception = assertThrows<IllegalArgumentException> {
                TestDataBuilder.aBlockTime(
                    startTime = startTime,
                    endTime = endTime
                )
            }
            assertEquals("Block duration cannot exceed 12 hours", exception.message)
        }

        @Test
        fun `should require custom reason when reason is ALTELE`() {
            // When & Then
            val exception = assertThrows<IllegalArgumentException> {
                TestDataBuilder.aBlockTime(
                    reason = BlockReason.ALTELE,
                    customReason = null
                )
            }
            assertEquals("Custom reason is required when reason is 'Altele'", exception.message)
        }

        @Test
        fun `should allow custom reason when reason is ALTELE`() {
            // When
            val blockTime = TestDataBuilder.aBlockTime(
                reason = BlockReason.ALTELE,
                customReason = "Custom reason"
            )

            // Then
            assertEquals(BlockReason.ALTELE, blockTime.reason)
            assertEquals("Custom reason", blockTime.customReason)
            assertEquals("Custom reason", blockTime.getDisplayReason())
        }

        @Test
        fun `should require recurrence pattern when isRecurring is true`() {
            // When & Then
            val exception = assertThrows<IllegalArgumentException> {
                TestDataBuilder.aBlockTime(
                    isRecurring = true,
                    recurrencePattern = null
                )
            }
            assertEquals("Recurrence pattern is required for recurring blocks", exception.message)
        }
    }

    @Nested
    @DisplayName("Business Behavior")
    inner class BusinessBehavior {

        @Test
        fun `should check if block affects specific staff`() {
            // Given
            val staffIds = setOf(StaffId.of("staff-1"), StaffId.of("staff-2"))
            val blockTime = TestDataBuilder.aBlockTime(staffIds = staffIds)

            // When & Then
            assertTrue(blockTime.affectsStaff(StaffId.of("staff-1")))
            assertTrue(blockTime.affectsStaff(StaffId.of("staff-2")))
            assertFalse(blockTime.affectsStaff(StaffId.of("staff-3")))
        }

        @Test
        fun `should check overlap with time period`() {
            // Given
            val startTime = ZonedDateTime.now().plusHours(1)
            val endTime = startTime.plusHours(2)
            val blockTime = TestDataBuilder.aBlockTime(startTime = startTime, endTime = endTime)

            // When & Then - Overlapping periods
            assertTrue(blockTime.overlapsWith(startTime.minusMinutes(30), startTime.plusMinutes(30)))
            assertTrue(blockTime.overlapsWith(endTime.minusMinutes(30), endTime.plusMinutes(30)))
            assertTrue(blockTime.overlapsWith(startTime.plusMinutes(30), endTime.minusMinutes(30)))
            assertTrue(blockTime.overlapsWith(startTime.minusMinutes(30), endTime.plusMinutes(30)))

            // When & Then - Non-overlapping periods
            assertFalse(blockTime.overlapsWith(startTime.minusHours(2), startTime.minusHours(1)))
            assertFalse(blockTime.overlapsWith(endTime.plusHours(1), endTime.plusHours(2)))
        }

        @Test
        fun `should calculate duration in minutes correctly`() {
            // Given
            val startTime = ZonedDateTime.now().plusHours(1)
            val endTime = startTime.plusMinutes(90) // 1.5 hours
            val blockTime = TestDataBuilder.aBlockTime(startTime = startTime, endTime = endTime)

            // When
            val duration = blockTime.getDurationMinutes()

            // Then
            assertEquals(90, duration)
        }

        @Test
        fun `should check if block is active`() {
            // Given
            val activeBlock = TestDataBuilder.aBlockTime(status = BlockTimeStatus.ACTIVE)
            val cancelledBlock = TestDataBuilder.aBlockTime(status = BlockTimeStatus.CANCELLED)
            val expiredBlock = TestDataBuilder.aBlockTime(status = BlockTimeStatus.EXPIRED)

            // When & Then
            assertTrue(activeBlock.isActive())
            assertFalse(cancelledBlock.isActive())
            assertFalse(expiredBlock.isActive())
        }

        @Test
        fun `should check if block is in past`() {
            // Given
            val pastBlock = blockTime().inPast().build()
            val futureBlock = blockTime().inFuture().build()

            // When & Then
            assertTrue(pastBlock.isInPast())
            assertFalse(futureBlock.isInPast())
        }

        @Test
        fun `should get display reason for standard reasons`() {
            // Given
            val blockTime = TestDataBuilder.aBlockTime(reason = BlockReason.PAUZA)

            // When
            val displayReason = blockTime.getDisplayReason()

            // Then
            assertEquals("Pauză", displayReason)
        }

        @Test
        fun `should get display reason for custom reason`() {
            // Given
            val customReason = "Întâlnire cu furnizorul"
            val blockTime = TestDataBuilder.aBlockTime(
                reason = BlockReason.ALTELE,
                customReason = customReason
            )

            // When
            val displayReason = blockTime.getDisplayReason()

            // Then
            assertEquals(customReason, displayReason)
        }
    }

    @Nested
    @DisplayName("State Changes")
    inner class StateChanges {

        @Test
        fun `should cancel block time with reason`() {
            // Given
            val blockTime = TestDataBuilder.aBlockTime()
            val cancelledBy = UserId.of("user-2")
            val cancellationReason = "Test cancellation"

            // When
            val cancelledBlock = blockTime.cancel(cancelledBy, cancellationReason)

            // Then
            assertEquals(BlockTimeStatus.CANCELLED, cancelledBlock.status)
            assertEquals(cancelledBy, cancelledBlock.updatedBy)
            assertTrue(cancelledBlock.notes!!.contains("Cancelled: $cancellationReason"))
        }

        @Test
        fun `should cancel block time without reason`() {
            // Given
            val blockTime = TestDataBuilder.aBlockTime(notes = "Original notes")
            val cancelledBy = UserId.of("user-2")

            // When
            val cancelledBlock = blockTime.cancel(cancelledBy)

            // Then
            assertEquals(BlockTimeStatus.CANCELLED, cancelledBlock.status)
            assertEquals(cancelledBy, cancelledBlock.updatedBy)
            assertEquals("Original notes", cancelledBlock.notes)
        }

        @Test
        fun `should update block time fields`() {
            // Given
            val blockTime = TestDataBuilder.aBlockTime()
            val newStartTime = blockTime.startTime.plusHours(1)
            val newEndTime = blockTime.endTime.plusHours(1)
            val updatedBy = UserId.of("user-2")

            // When
            val updatedBlock = blockTime.update(
                startTime = newStartTime,
                endTime = newEndTime,
                reason = BlockReason.INTALNIRE,
                customReason = "Updated reason",
                staffIds = setOf(StaffId.of("new-staff")),
                notes = "Updated notes",
                updatedBy = updatedBy
            )

            // Then
            assertEquals(newStartTime, updatedBlock.startTime)
            assertEquals(newEndTime, updatedBlock.endTime)
            assertEquals(BlockReason.INTALNIRE, updatedBlock.reason)
            assertEquals("Updated reason", updatedBlock.customReason)
            assertEquals(setOf(StaffId.of("new-staff")), updatedBlock.staffIds)
            assertEquals("Updated notes", updatedBlock.notes)
            assertEquals(updatedBy, updatedBlock.updatedBy)
        }

        @Test
        fun `should update only specified fields`() {
            // Given
            val originalReason = BlockReason.PAUZA
            val originalStaffIds = setOf(StaffId.of("staff-1"))
            val blockTime = TestDataBuilder.aBlockTime(
                reason = originalReason,
                staffIds = originalStaffIds
            )
            val updatedBy = UserId.of("user-2")

            // When - Only update notes
            val updatedBlock = blockTime.update(
                notes = "Only notes updated",
                updatedBy = updatedBy
            )

            // Then - Other fields should remain unchanged
            assertEquals(originalReason, updatedBlock.reason)
            assertEquals(originalStaffIds, updatedBlock.staffIds)
            assertEquals("Only notes updated", updatedBlock.notes)
            assertEquals(updatedBy, updatedBlock.updatedBy)
        }
    }
}
