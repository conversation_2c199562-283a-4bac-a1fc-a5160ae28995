import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue
import ro.animaliaprogramari.animalia.domain.model.notification.*
import ro.animaliaprogramari.animalia.domain.model.*

class NotificationTest {
    @Test
    fun `markAsSent and delivered`() {
        var n = Notification.createSms(PhoneNumber.of("0711111111"), "msg", null, SalonId.of("s"))
        n = n.markAsSent()
        assertEquals(NotificationStatus.SENT, n.status)
        n = n.markAsDelivered()
        assertEquals(NotificationStatus.DELIVERED, n.status)
    }

    @Test
    fun `retry logic`() {
        var n = Notification.createSms(PhoneNumber.of("0711111111"), "msg", null, SalonId.of("s"))
        n = n.markAsFailed("err")
        assertTrue(n.canRetry())
        n = n.incrementRetry()
        assertEquals(1, n.retryCount)
        n = n.copy(retryCount = 3, status = NotificationStatus.FAILED)
        assertFalse(n.canRetry())
    }
}
