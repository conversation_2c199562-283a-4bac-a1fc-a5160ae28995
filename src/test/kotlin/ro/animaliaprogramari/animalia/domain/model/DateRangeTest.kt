import ro.animaliaprogramari.animalia.domain.model.DateRange
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue
import java.time.LocalDate

class DateRangeTest {
    @Test
    fun `contains and daysBetween`() {
        val start = LocalDate.of(2024,1,1)
        val end = LocalDate.of(2024,1,5)
        val range = DateRange.of(start, end)
        assertEquals(5, range.daysBetween())
        assertTrue(range.contains(LocalDate.of(2024,1,3)))
        assertFalse(range.contains(LocalDate.of(2023,12,31)))
    }
}
