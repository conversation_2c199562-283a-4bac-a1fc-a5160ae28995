import ro.animaliaprogramari.animalia.domain.model.*
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertFailsWith
import kotlin.test.assertTrue
import java.time.LocalDate
import java.time.LocalTime

class AppointmentDomainTest {
    private fun sampleService(id: String, duration: Int, price: Double) = SalonService(
        ServiceId.of(id), SalonId.of("salon"), "srv", null, Money.of(price), Duration.ofMinutes(duration), ServiceCategory.GROOMING
    )

    @Test
    fun `create should compute totals`() {
        val s1 = sampleService("1", 30, 20.0)
        val s2 = sampleService("2", 45, 30.0)
        val appt = Appointment.create(
            SalonId.of("salon"), ClientId.of("client"), PetId.of("pet"), StaffId.of("staff"),
            LocalDate.now().plusDays(1), LocalTime.of(9,0), LocalTime.of(10,30), listOf(s1, s2)
        )
        assertEquals(Money.of(50.0), appt.totalPrice)
        assertEquals(Duration.ofMinutes(75), appt.totalDuration)
        assertEquals(AppointmentStatus.SCHEDULED, appt.status)
    }

    @Test
    fun `cancel should update status and notes`() {
        var appt = Appointment.create(
            SalonId.of("salon"), ClientId.of("client"), PetId.of("pet"), StaffId.of("staff"),
            LocalDate.now().plusDays(1), LocalTime.of(9,0), LocalTime.of(10,0), listOf(sampleService("1",30,20.0))
        )
        appt = appt.cancel("n")
        assertEquals(AppointmentStatus.CANCELLED, appt.status)
        assertTrue(appt.notes!!.contains("Cancelled"))
    }

    @Test
    fun `addService should increase totals`() {
        val s1 = sampleService("1", 30, 20.0)
        val s2 = sampleService("2", 15, 10.0)
        var appt = Appointment.create(
            SalonId.of("salon"), ClientId.of("client"), PetId.of("pet"), StaffId.of("staff"),
            LocalDate.now().plusDays(1), LocalTime.of(9,0), LocalTime.of(9,30), listOf(s1)
        )
        appt = appt.addService(s2.id, s2.basePrice, s2.duration)
        assertEquals(2, appt.serviceIds.size)
        assertEquals(Money.of(30.0), appt.totalPrice)
        assertEquals(Duration.ofMinutes(45), appt.totalDuration)
    }

    @Test
    fun `reschedule with invalid times should throw`() {
        val s1 = sampleService("1", 30, 20.0)
        val appt = Appointment.create(
            SalonId.of("salon"), ClientId.of("client"), PetId.of("pet"), StaffId.of("staff"),
            LocalDate.now().plusDays(1), LocalTime.of(9,0), LocalTime.of(10,0), listOf(s1)
        )
        assertFailsWith<IllegalArgumentException> {
            appt.reschedule(LocalDate.now().plusDays(2), LocalTime.of(10,0), LocalTime.of(9,0))
        }
    }

    @Test
    fun `overlaps should detect conflict`() {
        val s1 = sampleService("1", 30, 20.0)
        val appt = Appointment.create(
            SalonId.of("salon"), ClientId.of("client"), PetId.of("pet"), StaffId.of("staff"),
            LocalDate.now().plusDays(1), LocalTime.of(9,0), LocalTime.of(10,0), listOf(s1)
        )
        val slot = TimeSlot.of(LocalTime.of(9,30), LocalTime.of(10,30))
        assertTrue(appt.overlaps(slot.startTime, slot.endTime))
    }
}
