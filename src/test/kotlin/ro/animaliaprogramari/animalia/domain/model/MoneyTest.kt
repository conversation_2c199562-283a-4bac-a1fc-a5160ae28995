package ro.animaliaprogramari.animalia.domain.model

import java.math.BigDecimal
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertFailsWith

class MoneyTest {
    @Test
    fun `should perform arithmetic operations`() {
        val m1 = Money.of(10.0)
        val m2 = Money.of(2.5)
        assertEquals(Money.of(12.5), m1.add(m2))
        assertEquals(Money.of(7.5), m1.subtract(m2))
    }

    @Test
    fun `subtracting more than available should throw`() {
        val m1 = Money.of(5.0)
        val m2 = Money.of(10.0)
        assertFailsWith<IllegalArgumentException> { m1.subtract(m2) }
    }
}
