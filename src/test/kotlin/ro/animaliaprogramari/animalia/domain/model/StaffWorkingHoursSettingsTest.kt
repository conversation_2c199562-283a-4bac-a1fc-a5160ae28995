package ro.animaliaprogramari.animalia.domain.model

import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.LocalTime

class StaffWorkingHoursSettingsTest {

    private val staffId = StaffId.of("staff-123")
    private val salonId = SalonId.of("salon-456")

    @Test
    fun `should create default working hours settings`() {
        val settings = StaffWorkingHoursSettings.createDefault(staffId, salonId)

        assertEquals(staffId, settings.staffId)
        assertEquals(salonId, settings.salonId)
        assertEquals(7, settings.weeklySchedule.size)
        assertTrue(settings.holidays.isNotEmpty())
        assertTrue(settings.customClosures.isEmpty())
    }

    @Test
    fun `should require all 7 days in weekly schedule`() {
        val incompleteSchedule = mapOf(
            DayOfWeek.MONDAY to DaySchedule.workingDay(LocalTime.of(9, 0), LocalTime.of(17, 0))
        )

        assertThrows<IllegalArgumentException> {
            StaffWorkingHoursSettings(
                staffId = staffId,
                salonId = salonId,
                weeklySchedule = incompleteSchedule,
                holidays = emptyList(),
                customClosures = emptyList()
            )
        }
    }

    @Test
    fun `should check availability on specific date`() {
        val settings = StaffWorkingHoursSettings.createDefault(staffId, salonId)
        // Find a Monday in the current year that's not a holiday
        val currentYear = LocalDate.now().year
        val mondayDate = LocalDate.of(currentYear, 6, 3) // A Monday in June (usually not a holiday)

        assertTrue(settings.isAvailableOn(mondayDate))
    }

    @Test
    fun `should not be available on custom closure date`() {
        val currentYear = LocalDate.now().year
        val closureDate = LocalDate.of(currentYear, 6, 15)
        val customClosure = StaffCustomClosure.create(salonId, "Concediu medical", closureDate)

        val settings = StaffWorkingHoursSettings.createDefault(staffId, salonId)
            .updateCustomClosures(listOf(customClosure))

        assertFalse(settings.isAvailableOn(closureDate))
    }

    @Test
    fun `should not be available on non-working holiday`() {
        val currentYear = LocalDate.now().year
        val holidayDate = LocalDate.of(currentYear, 1, 1) // New Year of current year
        val settings = StaffWorkingHoursSettings.createDefault(staffId, salonId)

        assertFalse(settings.isAvailableOn(holidayDate))
    }

    @Test
    fun `should be available on working holiday`() {
        val currentYear = LocalDate.now().year
        val holidayDate = LocalDate.of(currentYear, 1, 1)
        val workingHoliday = StaffHoliday.create(salonId, "Working New Year", holidayDate, isWorkingDay = true)

        val settings = StaffWorkingHoursSettings.createDefault(staffId, salonId)
            .updateHolidays(listOf(workingHoliday))

        assertTrue(settings.isAvailableOn(holidayDate))
    }

    @Test
    fun `should get working hours for available date`() {
        val settings = StaffWorkingHoursSettings.createDefault(staffId, salonId)
        val currentYear = LocalDate.now().year
        val mondayDate = LocalDate.of(currentYear, 6, 3) // A Monday in June

        val workingHours = settings.getWorkingHoursFor(mondayDate)

        assertNotNull(workingHours)
        assertTrue(workingHours!!.isWorkingDay)
        assertEquals(LocalTime.of(9, 0), workingHours.startTime)
        assertEquals(LocalTime.of(17, 0), workingHours.endTime)
    }

    @Test
    fun `should return null working hours for unavailable date`() {
        val settings = StaffWorkingHoursSettings.createDefault(staffId, salonId)
        // Use a known Sunday date - January 5, 2025 is a Sunday
        val sundayDate = LocalDate.of(2025, 1, 5)

        val workingHours = settings.getWorkingHoursFor(sundayDate)

        assertNull(workingHours)
    }

    @Test
    fun `should update weekly schedule`() {
        val settings = StaffWorkingHoursSettings.createDefault(staffId, salonId)
        val newSchedule = mapOf(
            DayOfWeek.MONDAY to DaySchedule.workingDay(LocalTime.of(8, 0), LocalTime.of(16, 0)),
            DayOfWeek.TUESDAY to DaySchedule.workingDay(LocalTime.of(8, 0), LocalTime.of(16, 0)),
            DayOfWeek.WEDNESDAY to DaySchedule.workingDay(LocalTime.of(8, 0), LocalTime.of(16, 0)),
            DayOfWeek.THURSDAY to DaySchedule.workingDay(LocalTime.of(8, 0), LocalTime.of(16, 0)),
            DayOfWeek.FRIDAY to DaySchedule.workingDay(LocalTime.of(8, 0), LocalTime.of(16, 0)),
            DayOfWeek.SATURDAY to DaySchedule.dayOff(),
            DayOfWeek.SUNDAY to DaySchedule.dayOff()
        )

        val updatedSettings = settings.updateWeeklySchedule(newSchedule)

        assertEquals(LocalTime.of(8, 0), updatedSettings.weeklySchedule[DayOfWeek.MONDAY]?.startTime)
        assertEquals(LocalTime.of(16, 0), updatedSettings.weeklySchedule[DayOfWeek.MONDAY]?.endTime)
    }

    @Test
    fun `should update holidays`() {
        val settings = StaffWorkingHoursSettings.createDefault(staffId, salonId)
        val currentYear = LocalDate.now().year
        val newHoliday = StaffHoliday.create(salonId, "Custom Holiday", LocalDate.of(currentYear, 7, 4))
        val newHolidays = listOf(newHoliday)

        val updatedSettings = settings.updateHolidays(newHolidays)

        assertEquals(1, updatedSettings.holidays.size)
        assertEquals("Custom Holiday", updatedSettings.holidays[0].name)
    }

    @Test
    fun `should update custom closures`() {
        val settings = StaffWorkingHoursSettings.createDefault(staffId, salonId)
        val currentYear = LocalDate.now().year
        val newClosure = StaffCustomClosure.create(salonId, "Personal leave", LocalDate.of(currentYear, 8, 15))
        val newClosures = listOf(newClosure)

        val updatedSettings = settings.updateCustomClosures(newClosures)

        assertEquals(1, updatedSettings.customClosures.size)
        assertEquals("Personal leave", updatedSettings.customClosures[0].reason)
    }

    @Test
    fun `should create default Romanian holidays for current and next year`() {
        val settings = StaffWorkingHoursSettings.createDefault(staffId, salonId)
        val currentYear = LocalDate.now().year

        val currentYearHolidays = settings.holidays.filter { it.date.year == currentYear }
        val nextYearHolidays = settings.holidays.filter { it.date.year == currentYear + 1 }

        assertTrue(currentYearHolidays.isNotEmpty())
        assertTrue(nextYearHolidays.isNotEmpty())
        
        // Check for specific Romanian holidays
        assertTrue(settings.holidays.any { it.name == "Anul Nou" })
        assertTrue(settings.holidays.any { it.name == "Crăciunul" })
        assertTrue(settings.holidays.any { it.name == "Ziua Națională" })
    }
}
