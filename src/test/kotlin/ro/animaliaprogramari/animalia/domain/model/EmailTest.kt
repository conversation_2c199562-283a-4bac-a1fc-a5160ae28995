import ro.animaliaprogramari.animalia.domain.model.Email
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertNull

class EmailTest {
    @Test
    fun `placeholder uses phone`() {
        val email = Email.placeholder("+40712345678")
        assertEquals("<EMAIL>", email.value)
    }

    @Test
    fun `ofNullable returns null for invalid`() {
        assertNull(Email.ofNullable("not-an-email"))
        assertNull(Email.ofNullable(""))
    }
}
