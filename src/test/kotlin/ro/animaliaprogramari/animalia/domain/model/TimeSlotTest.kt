import org.junit.jupiter.api.assertThrows
import ro.animaliaprogramari.animalia.domain.model.TimeSlot
import kotlin.test.Test
import kotlin.test.assertFalse
import kotlin.test.assertTrue
import java.time.LocalTime

class TimeSlotTest {
    @Test
    fun `overlap detection`() {
        val t1 = TimeSlot.of(LocalTime.of(9,0), LocalTime.of(10,0))
        val t2 = TimeSlot.of(LocalTime.of(9,30), LocalTime.of(10,30))
        assertTrue(t1.overlaps(t2))
    }

}
