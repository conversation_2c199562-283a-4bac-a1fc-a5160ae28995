import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.api.assertThrows
import ro.animaliaprogramari.animalia.domain.model.JwtToken
import kotlin.test.Test
import kotlin.test.assertFalse
import kotlin.test.assertTrue
import java.time.LocalDateTime

class JwtTokenTest {
    @Test
    fun `token validity`() {
        val token = JwtToken.createWithDuration("tok", 5)
        assertTrue(token.isValid())
        Thread.sleep(1000)
        assertTrue(token.getRemainingValiditySeconds() > 0)
    }

    @Test
    fun `expired token`() {
        val past = LocalDateTime.now().minusSeconds(1)
        assertThrows<IllegalArgumentException> {
            JwtToken.create("tok", past)
        }
    }
}
