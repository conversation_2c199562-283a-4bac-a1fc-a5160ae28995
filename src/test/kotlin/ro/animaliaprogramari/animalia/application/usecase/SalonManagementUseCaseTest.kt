package ro.animaliaprogramari.animalia.application.usecase

import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.*
import ro.animaliaprogramari.animalia.application.command.*
import ro.animaliaprogramari.animalia.application.port.outbound.ClientRepository
import ro.animaliaprogramari.animalia.application.port.outbound.SalonRepository
import ro.animaliaprogramari.animalia.application.port.outbound.StaffRepository
import ro.animaliaprogramari.animalia.application.port.outbound.UserRepository
import ro.animaliaprogramari.animalia.application.port.outbound.WorkingHoursRepository
import ro.animaliaprogramari.animalia.domain.exception.BusinessRuleViolationException
import ro.animaliaprogramari.animalia.domain.exception.EntityNotFoundException
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.test.TestDataBuilder
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

/**
 * Unit tests for SalonManagementUseCaseImpl
 * Tests all salon management operations with comprehensive coverage
 */
@DisplayName("SalonManagementUseCase")
class SalonManagementUseCaseTest {

    // Mocked dependencies
    private val salonRepository = mockk<SalonRepository>()
    private val userRepository = mockk<UserRepository>()
    private val staffRepository = mockk<StaffRepository>()
    private val clientRepository = mockk<ClientRepository>()
    private val workingHoursRepository = mockk<WorkingHoursRepository>()

    // System under test
    private lateinit var useCase: SalonManagementUseCaseImpl

    @BeforeEach
    fun setUp() {
        clearAllMocks()
        useCase = SalonManagementUseCaseImpl(
            salonRepository = salonRepository,
            userRepository = userRepository,
            staffRepository = staffRepository,
            clientRepository = clientRepository,
            workingHoursRepository = workingHoursRepository
        )
    }

    @Nested
    @DisplayName("Create Salon")
    inner class CreateSalonTests {

        @Test
        fun `should create salon successfully when creator exists`() {
            // Given
            val creatorUserId = UserId.generate()
            val command = CreateSalonCommand(
                name = "Pet Paradise",
                description = "Best pet salon in town",
                address = "123 Pet Street",
                city = "Bucharest",
                phone = PhoneNumber.of("+40212345678"),
                email = Email.of("<EMAIL>"),
                creatorUserId = creatorUserId
            )

            val creator = TestDataBuilder.aUser().withId(creatorUserId).build()
            val expectedSalon = TestDataBuilder.aSalon()
                .withName("Pet Paradise")
                .withAddress("123 Pet Street")
                .withCity("Bucharest")
                .withPhone(PhoneNumber.of("+40212345678"))
                .withEmail(Email.of("<EMAIL>"))
                .withOwnerId(creatorUserId)
                .withActive(true)
                .build()

            val expectedStaff = TestDataBuilder.aStaff()
                .withUserId(creatorUserId)
                .withSalonId(expectedSalon.id)
                .withRole(StaffRole.CHIEF_GROOMER)
                .withPermissions(StaffPermissions.fullAccess())
                .build()

            every { userRepository.findById(creatorUserId) } returns creator
            every { salonRepository.save(any()) } returns expectedSalon
            every { workingHoursRepository.save(any()) } returns mockk()
            every { staffRepository.save(any()) } returns expectedStaff

            // When
            val result = useCase.createSalon(command)

            // Then
            assertNotNull(result)
            assertEquals("Pet Paradise", result.name)
            assertEquals("123 Pet Street", result.address)
            assertEquals("Bucharest", result.city)
            assertEquals(PhoneNumber.of("+40212345678"), result.phone)
            assertEquals(Email.of("<EMAIL>"), result.email)
            assertEquals(creatorUserId, result.ownerId)
            assertTrue(result.isActive)
            assertTrue(result.clientIds.isEmpty())

            // Verify interactions
            verify(exactly = 1) { userRepository.findById(creatorUserId) }
            verify(exactly = 1) { salonRepository.save(any()) }
            verify(exactly = 1) { workingHoursRepository.save(any()) }
            verify(exactly = 1) { staffRepository.save(any()) }
        }

        @Test
        fun `should create salon with minimal information when optional fields are null`() {
            // Given
            val creatorUserId = UserId.generate()
            val command = CreateSalonCommand(
                name = "Simple Salon",
                description = null,
                address = null,
                city = null,
                phone = null,
                email = null,
                creatorUserId = creatorUserId
            )

            val creator = TestDataBuilder.aUser().withId(creatorUserId).build()
            val expectedSalon = TestDataBuilder.aSalon()
                .withName("Simple Salon")
                .withAddress(null)
                .withCity(null)
                .withPhone(null)
                .withEmail(null)
                .withOwnerId(creatorUserId)
                .build()

            val expectedStaff = TestDataBuilder.aStaff()
                .withUserId(creatorUserId)
                .withSalonId(expectedSalon.id)
                .withRole(StaffRole.CHIEF_GROOMER)
                .build()

            every { userRepository.findById(creatorUserId) } returns creator
            every { salonRepository.save(any()) } returns expectedSalon
            every { workingHoursRepository.save(any()) } returns mockk()
            every { staffRepository.save(any()) } returns expectedStaff

            // When
            val result = useCase.createSalon(command)

            // Then
            assertNotNull(result)
            assertEquals("Simple Salon", result.name)
            assertNull(result.address)
            assertNull(result.city)
            assertNull(result.phone)
            assertNull(result.email)
            assertEquals(creatorUserId, result.ownerId)

            verify(exactly = 1) { userRepository.findById(creatorUserId) }
            verify(exactly = 1) { salonRepository.save(any()) }
            verify(exactly = 1) { workingHoursRepository.save(any()) }
            verify(exactly = 1) { staffRepository.save(any()) }
        }

        @Test
        fun `should throw EntityNotFoundException when creator not found`() {
            // Given
            val creatorUserId = UserId.generate()
            val command = CreateSalonCommand(
                name = "Test Salon",
                address = null,
                phone = null,
                email = null,
                creatorUserId = creatorUserId
            )

            every { userRepository.findById(creatorUserId) } returns null

            // When & Then
            val exception = assertThrows<EntityNotFoundException> {
                useCase.createSalon(command)
            }

            assertTrue(exception.message!!.contains("Creator user not found"))
            assertTrue(exception.message!!.contains(creatorUserId.value))

            verify(exactly = 1) { userRepository.findById(creatorUserId) }
            verify(exactly = 0) { salonRepository.save(any()) }
            verify(exactly = 0) { workingHoursRepository.save(any()) }
            verify(exactly = 0) { staffRepository.save(any()) }
        }

        @Test
        fun `should create chief groomer staff association for creator`() {
            // Given
            val creatorUserId = UserId.generate()
            val command = CreateSalonCommand(
                name = "Test Salon",
                address = null,
                phone = null,
                email = null,
                creatorUserId = creatorUserId
            )

            val creator = TestDataBuilder.aUser().withId(creatorUserId).build()
            val savedSalon = TestDataBuilder.aSalon().withOwnerId(creatorUserId).build()

            every { userRepository.findById(creatorUserId) } returns creator
            every { salonRepository.save(any()) } returns savedSalon
            every { workingHoursRepository.save(any()) } returns mockk()
            every { staffRepository.save(any()) } returns mockk()

            // When
            useCase.createSalon(command)

            // Then
            verify(exactly = 1) {
                staffRepository.save(match<Staff> { staff ->
                    staff.userId == creatorUserId &&
                    staff.salonId == savedSalon.id &&
                    staff.role == StaffRole.CHIEF_GROOMER &&
                    staff.permissions == StaffPermissions.fullAccess() &&
                    staff.isActive
                })
            }
        }

        @Test
        fun `should create default working hours for new salon`() {
            // Given
            val creatorUserId = UserId.generate()
            val command = CreateSalonCommand(
                name = "Test Salon",
                address = null,
                phone = null,
                email = null,
                creatorUserId = creatorUserId
            )

            val creator = TestDataBuilder.aUser().withId(creatorUserId).build()
            val savedSalon = TestDataBuilder.aSalon().withOwnerId(creatorUserId).build()

            every { userRepository.findById(creatorUserId) } returns creator
            every { salonRepository.save(any()) } returns savedSalon
            every { workingHoursRepository.save(any()) } returns mockk()
            every { staffRepository.save(any()) } returns mockk()

            // When
            useCase.createSalon(command)

            // Then
            verify(exactly = 1) {
                workingHoursRepository.save(match<WorkingHoursSettings> { workingHours ->
                    workingHours.salonId == savedSalon.id &&
                    workingHours.weeklySchedule.size == 7 &&
                    workingHours.holidays.isEmpty() &&
                    workingHours.customClosures.isEmpty()
                })
            }
        }
    }

    @Nested
    @DisplayName("Update Salon")
    inner class UpdateSalonTests {

        @Test
        fun `should update salon successfully when user has permission`() {
            // Given
            val salonId = SalonId.generate()
            val updaterUserId = UserId.generate()
            val command = UpdateSalonCommand(
                salonId = salonId,
                name = "Updated Salon Name",
                address = "Updated Address",
                phone = PhoneNumber.of("+40212345679"),
                email = Email.of("<EMAIL>"),
                updaterUserId = updaterUserId
            )

            val existingSalon = TestDataBuilder.aSalon()
                .withId(salonId)
                .withName("Old Name")
                .withAddress("Old Address")
                .build()

            val updaterStaff = TestDataBuilder.aStaff()
                .withUserId(updaterUserId)
                .withSalonId(salonId)
                .withRole(StaffRole.CHIEF_GROOMER)
                .withActive(true)
                .build()

            val updatedSalon = existingSalon.copy(
                name = "Updated Salon Name",
                address = "Updated Address",
                phone = PhoneNumber.of("+40212345679"),
                email = Email.of("<EMAIL>")
            )

            every { salonRepository.findById(salonId) } returns existingSalon
            every { staffRepository.findByUserIdAndSalonId(updaterUserId, salonId) } returns updaterStaff
            every { salonRepository.save(any()) } returns updatedSalon

            // When
            val result = useCase.updateSalon(command)

            // Then
            assertNotNull(result)
            assertEquals("Updated Salon Name", result.name)
            assertEquals("Updated Address", result.address)
            assertEquals(PhoneNumber.of("+40212345679"), result.phone)
            assertEquals(Email.of("<EMAIL>"), result.email)

            verify(exactly = 1) { salonRepository.findById(salonId) }
            verify(exactly = 1) { staffRepository.findByUserIdAndSalonId(updaterUserId, salonId) }
            verify(exactly = 1) { salonRepository.save(any()) }
        }

        @Test
        fun `should throw EntityNotFoundException when salon not found`() {
            // Given
            val salonId = SalonId.generate()
            val command = UpdateSalonCommand(
                salonId = salonId,
                name = "Updated Name",
                address = null,
                phone = null,
                email = null,
                updaterUserId = UserId.generate()
            )

            every { salonRepository.findById(salonId) } returns null

            // When & Then
            val exception = assertThrows<EntityNotFoundException> {
                useCase.updateSalon(command)
            }

            assertTrue(exception.message!!.contains("Salon not found"))
            assertTrue(exception.message!!.contains(salonId.value))

            verify(exactly = 1) { salonRepository.findById(salonId) }
            verify(exactly = 0) { salonRepository.save(any()) }
        }

        @Test
        fun `should throw BusinessRuleViolationException when user not associated with salon`() {
            // Given
            val salonId = SalonId.generate()
            val updaterUserId = UserId.generate()
            val command = UpdateSalonCommand(
                salonId = salonId,
                name = "Updated Name",
                address = null,
                phone = null,
                email = null,
                updaterUserId = updaterUserId
            )

            val existingSalon = TestDataBuilder.aSalon().withId(salonId).build()

            every { salonRepository.findById(salonId) } returns existingSalon
            every { staffRepository.findByUserIdAndSalonId(updaterUserId, salonId) } returns null

            // When & Then
            val exception = assertThrows<BusinessRuleViolationException> {
                useCase.updateSalon(command)
            }

            assertTrue(exception.message!!.contains("User does not have permission"))

            verify(exactly = 1) { staffRepository.findByUserIdAndSalonId(updaterUserId, salonId) }
            verify(exactly = 0) { salonRepository.save(any()) }
        }
        @Test
        fun `should throw BusinessRuleViolationException when user is not chief groomer`() {
            // Given
            val salonId = SalonId.generate()
            val updaterUserId = UserId.generate()
            val command = UpdateSalonCommand(
                salonId = salonId,
                name = "Updated Name",
                address = null,
                phone = null,
                email = null,
                updaterUserId = updaterUserId
            )

            val existingSalon = TestDataBuilder.aSalon().withId(salonId).build()
            val updaterUser = TestDataBuilder.aUser().withId(updaterUserId).build()
            val updaterStaff = TestDataBuilder.aStaff()
                .withUserId(updaterUserId)
                .withSalonId(salonId)
                .withRole(StaffRole.GROOMER) // Not chief groomer
                .withActive(true)
                .build()

            every { salonRepository.findById(salonId) } returns existingSalon
            every { userRepository.findById(updaterUserId) } returns updaterUser
            every { staffRepository.findByUserIdAndSalonId(updaterUserId, salonId) } returns updaterStaff

            // When & Then
            val exception = assertThrows<BusinessRuleViolationException> {
                useCase.updateSalon(command)
            }

            assertTrue(exception.message!!.contains("User does not have permission"))

            verify(exactly = 1) { staffRepository.findByUserIdAndSalonId(updaterUserId, salonId) }
            verify(exactly = 0) { salonRepository.save(any()) }
        }

        @Test
        fun `should update only specified fields and keep others unchanged`() {
            // Given
            val salonId = SalonId.generate()
            val updaterUserId = UserId.generate()
            val command = UpdateSalonCommand(
                salonId = salonId,
                name = "New Name",
                address = null, // Not updating address
                phone = null,   // Not updating phone
                email = null,   // Not updating email
                updaterUserId = updaterUserId
            )

            val existingSalon = TestDataBuilder.aSalon()
                .withId(salonId)
                .withName("Old Name")
                .withAddress("Old Address")
                .withPhone(PhoneNumber.of("+40212345678"))
                .withEmail(Email.of("<EMAIL>"))
                .build()

            val updaterUser = TestDataBuilder.aUser().withId(updaterUserId).build()
            val updaterStaff = TestDataBuilder.aStaff()
                .withUserId(updaterUserId)
                .withSalonId(salonId)
                .withRole(StaffRole.CHIEF_GROOMER)
                .withActive(true)
                .build()

            val updatedSalon = existingSalon.copy(name = "New Name")

            every { salonRepository.findById(salonId) } returns existingSalon
            every { userRepository.findById(updaterUserId) } returns updaterUser
            every { staffRepository.findByUserIdAndSalonId(updaterUserId, salonId) } returns updaterStaff
            every { salonRepository.save(any()) } returns updatedSalon

            // When
            val result = useCase.updateSalon(command)

            // Then
            assertEquals("New Name", result.name)
            // Other fields should remain unchanged
            assertEquals("Old Address", result.address)
            assertEquals(PhoneNumber.of("+40212345678"), result.phone)
            assertEquals(Email.of("<EMAIL>"), result.email)
        }
    }

    @Nested
    @DisplayName("Activate Salon")
    inner class ActivateSalonTests {

        @Test
        fun `should activate salon successfully`() {
            // Given
            val salonId = SalonId.generate()
            val activatorUserId = UserId.generate()
            val command = ActivateSalonCommand(
                salonId = salonId,
                activatorUserId = activatorUserId
            )

            val inactiveSalon = TestDataBuilder.aSalon()
                .withId(salonId)
                .withActive(false)
                .build()

            val activatedSalon = inactiveSalon.copy(isActive = true)

            every { salonRepository.findById(salonId) } returns inactiveSalon
            every { salonRepository.save(any()) } returns activatedSalon

            // When
            val result = useCase.activateSalon(command)

            // Then
            assertNotNull(result)
            assertTrue(result.isActive)

            verify(exactly = 1) { salonRepository.findById(salonId) }
            verify(exactly = 1) { salonRepository.save(any()) }
        }

        @Test
        fun `should throw EntityNotFoundException when salon not found`() {
            // Given
            val salonId = SalonId.generate()
            val command = ActivateSalonCommand(
                salonId = salonId,
                activatorUserId = UserId.generate()
            )

            every { salonRepository.findById(salonId) } returns null

            // When & Then
            val exception = assertThrows<EntityNotFoundException> {
                useCase.activateSalon(command)
            }

            assertTrue(exception.message!!.contains("Salon not found"))
            assertTrue(exception.message!!.contains(salonId.value))

            verify(exactly = 1) { salonRepository.findById(salonId) }
            verify(exactly = 0) { salonRepository.save(any()) }
        }
    }

    @Nested
    @DisplayName("Deactivate Salon")
    inner class DeactivateSalonTests {

        @Test
        fun `should deactivate salon successfully`() {
            // Given
            val salonId = SalonId.generate()
            val deactivatorUserId = UserId.generate()
            val command = DeactivateSalonCommand(
                salonId = salonId,
                deactivatorUserId = deactivatorUserId
            )

            val activeSalon = TestDataBuilder.aSalon()
                .withId(salonId)
                .withActive(true)
                .build()

            val deactivatedSalon = activeSalon.copy(isActive = false)

            every { salonRepository.findById(salonId) } returns activeSalon
            every { salonRepository.save(any()) } returns deactivatedSalon

            // When
            val result = useCase.deactivateSalon(command)

            // Then
            assertNotNull(result)
            assertFalse(result.isActive)

            verify(exactly = 1) { salonRepository.findById(salonId) }
            verify(exactly = 1) { salonRepository.save(any()) }
        }

        @Test
        fun `should throw EntityNotFoundException when salon not found`() {
            // Given
            val salonId = SalonId.generate()
            val command = DeactivateSalonCommand(
                salonId = salonId,
                deactivatorUserId = UserId.generate()
            )

            every { salonRepository.findById(salonId) } returns null

            // When & Then
            val exception = assertThrows<EntityNotFoundException> {
                useCase.deactivateSalon(command)
            }

            assertTrue(exception.message!!.contains("Salon not found"))
            assertTrue(exception.message!!.contains(salonId.value))

            verify(exactly = 1) { salonRepository.findById(salonId) }
            verify(exactly = 0) { salonRepository.save(any()) }
        }
    }

    @Nested
    @DisplayName("Add Client to Salon")
    inner class AddClientToSalonTests {

        @Test
        fun `should add client to salon successfully when user has permission`() {
            // Given
            val salonId = SalonId.generate()
            val clientId = ClientId.generate()
            val groomerUserId = UserId.generate()
            val command = AddClientToSalonCommand(
                salonId = salonId,
                clientId = clientId,
                groomerUserId = groomerUserId
            )

            val salon = TestDataBuilder.aSalon()
                .withId(salonId)
                .withClientIds(emptySet())
                .build()

            val client = TestDataBuilder.aClient().withId(clientId).build()
            val groomerStaff = TestDataBuilder.aStaff()
                .withUserId(groomerUserId)
                .withSalonId(salonId)
                .withRole(StaffRole.GROOMER)
                .withActive(true)
                .build()

            val updatedSalon = salon.copy(clientIds = setOf(clientId))

            every { salonRepository.findById(salonId) } returns salon
            every { clientRepository.findById(clientId) } returns client
            every { staffRepository.findByUserIdAndSalonId(groomerUserId, salonId) } returns groomerStaff
            every { salonRepository.save(any()) } returns updatedSalon

            // When
            val result = useCase.addClientToSalon(command)

            // Then
            assertNotNull(result)
            assertTrue(result.clientIds.contains(clientId))

            verify(exactly = 1) { salonRepository.findById(salonId) }
            verify(exactly = 1) { clientRepository.findById(clientId) }
            verify(exactly = 1) { staffRepository.findByUserIdAndSalonId(groomerUserId, salonId) }
            verify(exactly = 1) { salonRepository.save(any()) }
        }

        @Test
        fun `should throw EntityNotFoundException when salon not found`() {
            // Given
            val command = AddClientToSalonCommand(
                salonId = SalonId.generate(),
                clientId = ClientId.generate(),
                groomerUserId = UserId.generate()
            )

            every { salonRepository.findById(command.salonId) } returns null

            // When & Then
            val exception = assertThrows<EntityNotFoundException> {
                useCase.addClientToSalon(command)
            }

            assertTrue(exception.message!!.contains("Salon not found"))

            verify(exactly = 1) { salonRepository.findById(command.salonId) }
            verify(exactly = 0) { salonRepository.save(any()) }
        }

        @Test
        fun `should throw EntityNotFoundException when client not found`() {
            // Given
            val command = AddClientToSalonCommand(
                salonId = SalonId.generate(),
                clientId = ClientId.generate(),
                groomerUserId = UserId.generate()
            )

            val salon = TestDataBuilder.aSalon().withId(command.salonId).build()

            every { salonRepository.findById(command.salonId) } returns salon
            every { clientRepository.findById(command.clientId) } returns null

            // When & Then
            val exception = assertThrows<EntityNotFoundException> {
                useCase.addClientToSalon(command)
            }

            assertTrue(exception.message!!.contains("Client not found"))

            verify(exactly = 1) { clientRepository.findById(command.clientId) }
            verify(exactly = 0) { salonRepository.save(any()) }
        }

        @Test
        fun `should throw BusinessRuleViolationException when groomer has no permission`() {
            // Given
            val salonId = SalonId.generate()
            val clientId = ClientId.generate()
            val groomerUserId = UserId.generate()
            val command = AddClientToSalonCommand(
                salonId = salonId,
                clientId = clientId,
                groomerUserId = groomerUserId
            )

            val salon = TestDataBuilder.aSalon().withId(salonId).build()
            val client = TestDataBuilder.aClient().withId(clientId).build()
            val groomerUser = TestDataBuilder.aUser().withId(groomerUserId).build()
            val groomerStaff = TestDataBuilder.aStaff()
                .withUserId(groomerUserId)
                .withSalonId(salonId)
                .withPermissions(StaffPermissions(
                    clientDataAccess = ClientDataAccess.NONE,
                    canManageAppointments = false,
                    canManageServices = false,
                    canViewReports = false,
                    canManageSchedule = false
                ))
                .build()

            every { salonRepository.findById(salonId) } returns salon
            every { clientRepository.findById(clientId) } returns client
            every { userRepository.findById(groomerUserId) } returns groomerUser
            every { staffRepository.findByUserIdAndSalonId(groomerUserId, salonId) } returns groomerStaff

            // When & Then
            val exception = assertThrows<BusinessRuleViolationException> {
                useCase.addClientToSalon(command)
            }

            assertTrue(exception.message!!.contains("Groomer does not have permission"))

            verify(exactly = 0) { salonRepository.save(any()) }
        }
    }
}
