package ro.animaliaprogramari.animalia.application.usecase

import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.*
import ro.animaliaprogramari.animalia.application.command.UpdateNotificationSettingsCommand
import ro.animaliaprogramari.animalia.application.port.outbound.NotificationSettingsRepository
import ro.animaliaprogramari.animalia.application.port.outbound.SalonRepository
import ro.animaliaprogramari.animalia.application.query.GetNotificationSettingsQuery
import ro.animaliaprogramari.animalia.domain.exception.EntityNotFoundException
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.test.TestDataBuilder
import java.time.LocalTime
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

/**
 * Unit tests for NotificationSettingsManagementUseCaseImpl
 * Tests notification preference management with comprehensive coverage
 */
@DisplayName("NotificationSettingsManagementUseCase")
class NotificationSettingsManagementUseCaseTest {

    // Mocked dependencies
    private val notificationSettingsRepository = mockk<NotificationSettingsRepository>()
    private val salonRepository = mockk<SalonRepository>()

    // System under test
    private lateinit var useCase: NotificationSettingsManagementUseCaseImpl

    @BeforeEach
    fun setUp() {
        clearAllMocks()
        useCase = NotificationSettingsManagementUseCaseImpl(
            notificationSettingsRepository = notificationSettingsRepository,
            salonRepository = salonRepository
        )
    }

    @Nested
    @DisplayName("Get Notification Settings")
    inner class GetNotificationSettingsTests {

        @Test
        fun `should return existing notification settings when found`() {
            // Given
            val salonId = SalonId.generate()
            val query = GetNotificationSettingsQuery(salonId = salonId)

            val salon = TestDataBuilder.aSalon().withId(salonId).build()
            val existingSettings = NotificationSettings(
                salonId = salonId,
                pushNotificationsEnabled = true,
                soundPreference = SoundPreference.CUSTOM1,
                vibrationEnabled = false,
                doNotDisturb = DoNotDisturbSettings(
                    enabled = true,
                    startTime = LocalTime.of(22, 0),
                    endTime = LocalTime.of(8, 0),
                    allowCritical = true
                ),
                notificationRules = NotificationRules(
                    newAppointments = true,
                    appointmentCancellations = true,
                    paymentConfirmations = true,
                    teamMemberUpdates = false,
                    systemMaintenanceAlerts = true
                )
            )

            every { salonRepository.findById(salonId) } returns salon
            every { notificationSettingsRepository.findBySalonId(salonId) } returns existingSettings

            // When
            val result = useCase.getNotificationSettings(query)

            // Then
            assertNotNull(result)
            assertEquals(salonId, result.salonId)
            assertEquals(SoundPreference.CUSTOM1, result.soundPreference)
            assertFalse(result.vibrationEnabled)
            assertTrue(result.doNotDisturb.enabled)
            assertFalse(result.notificationRules.teamMemberUpdates)

            verify(exactly = 1) { salonRepository.findById(salonId) }
            verify(exactly = 1) { notificationSettingsRepository.findBySalonId(salonId) }
        }

        @Test
        fun `should create and return default settings when none exist`() {
            // Given
            val salonId = SalonId.generate()
            val query = GetNotificationSettingsQuery(salonId = salonId)

            val salon = TestDataBuilder.aSalon().withId(salonId).build()
            val defaultSettings = NotificationSettings.createDefault(salonId)

            every { salonRepository.findById(salonId) } returns salon
            every { notificationSettingsRepository.findBySalonId(salonId) } returns null
            every { notificationSettingsRepository.save(any()) } returns defaultSettings

            // When
            val result = useCase.getNotificationSettings(query)

            // Then
            assertNotNull(result)
            assertEquals(salonId, result.salonId)
            assertTrue(result.pushNotificationsEnabled) // Default value
            assertEquals(SoundPreference.DEFAULT, result.soundPreference) // Default value
            assertTrue(result.vibrationEnabled) // Default value

            verify(exactly = 1) { salonRepository.findById(salonId) }
            verify(exactly = 1) { notificationSettingsRepository.findBySalonId(salonId) }
            verify(exactly = 1) { notificationSettingsRepository.save(any()) }
        }

        @Test
        fun `should throw EntityNotFoundException when salon not found`() {
            // Given
            val salonId = SalonId.generate()
            val query = GetNotificationSettingsQuery(salonId = salonId)

            every { salonRepository.findById(salonId) } returns null

            // When & Then
            val exception = assertThrows<EntityNotFoundException> {
                useCase.getNotificationSettings(query)
            }

            assertTrue(exception.message!!.contains("Salon not found"))
            verify(exactly = 1) { salonRepository.findById(salonId) }
            verify(exactly = 0) { notificationSettingsRepository.findBySalonId(any()) }
        }
    }

    @Nested
    @DisplayName("Update Notification Settings")
    inner class UpdateNotificationSettingsTests {

        @Test
        fun `should update existing notification settings successfully`() {
            // Given
            val salonId = SalonId.generate()
            val command = UpdateNotificationSettingsCommand(
                salonId = salonId,
                pushNotificationsEnabled = false,
                soundPreference = SoundPreference.SILENT,
                vibrationEnabled = true,
                doNotDisturb = DoNotDisturbSettings(
                    enabled = true,
                    startTime = LocalTime.of(23, 0),
                    endTime = LocalTime.of(7, 0),
                    allowCritical = false
                ),
                notificationRules = NotificationRules(
                    newAppointments = true,
                    appointmentCancellations = false,
                    paymentConfirmations = true,
                    teamMemberUpdates = true,
                    systemMaintenanceAlerts = false
                )
            )

            val salon = TestDataBuilder.aSalon().withId(salonId).build()
            val existingSettings = NotificationSettings.createDefault(salonId)
            val updatedSettings = existingSettings.updateSettings(
                pushNotificationsEnabled = command.pushNotificationsEnabled,
                soundPreference = command.soundPreference,
                vibrationEnabled = command.vibrationEnabled,
                doNotDisturb = command.doNotDisturb,
                notificationRules = command.notificationRules
            )

            every { salonRepository.findById(salonId) } returns salon
            every { notificationSettingsRepository.findBySalonId(salonId) } returns existingSettings
            every { notificationSettingsRepository.save(any()) } returns updatedSettings

            // When
            val result = useCase.updateNotificationSettings(command)

            // Then
            assertNotNull(result)
            assertEquals(salonId, result.salonId)
            assertFalse(result.pushNotificationsEnabled)
            assertEquals(SoundPreference.SILENT, result.soundPreference)
            assertTrue(result.vibrationEnabled)
            assertTrue(result.doNotDisturb.enabled)
            assertEquals(LocalTime.of(23, 0), result.doNotDisturb.startTime)
            assertFalse(result.doNotDisturb.allowCritical)
            assertFalse(result.notificationRules.appointmentCancellations)
            assertTrue(result.notificationRules.teamMemberUpdates)
            assertFalse(result.notificationRules.systemMaintenanceAlerts)

            verify(exactly = 1) { salonRepository.findById(salonId) }
            verify(exactly = 1) { notificationSettingsRepository.findBySalonId(salonId) }
            verify(exactly = 1) { notificationSettingsRepository.save(any()) }
        }

        @Test
        fun `should create new settings when none exist and update them`() {
            // Given
            val salonId = SalonId.generate()
            val command = UpdateNotificationSettingsCommand(
                salonId = salonId,
                pushNotificationsEnabled = false,
                soundPreference = SoundPreference.CUSTOM2,
                vibrationEnabled = false,
                doNotDisturb = DoNotDisturbSettings(),
                notificationRules = NotificationRules(
                    newAppointments = false,
                    appointmentCancellations = false,
                    paymentConfirmations = false,
                    teamMemberUpdates = false,
                    systemMaintenanceAlerts = false
                )
            )

            val salon = TestDataBuilder.aSalon().withId(salonId).build()
            val defaultSettings = NotificationSettings.createDefault(salonId)
            val updatedSettings = defaultSettings.updateSettings(
                pushNotificationsEnabled = command.pushNotificationsEnabled,
                soundPreference = command.soundPreference,
                vibrationEnabled = command.vibrationEnabled,
                doNotDisturb = command.doNotDisturb,
                notificationRules = command.notificationRules
            )

            every { salonRepository.findById(salonId) } returns salon
            every { notificationSettingsRepository.findBySalonId(salonId) } returns null
            every { notificationSettingsRepository.save(any()) } returns updatedSettings

            // When
            val result = useCase.updateNotificationSettings(command)

            // Then
            assertNotNull(result)
            assertEquals(salonId, result.salonId)
            assertFalse(result.pushNotificationsEnabled)
            assertEquals(SoundPreference.CUSTOM2, result.soundPreference)
            assertFalse(result.vibrationEnabled)

            verify(exactly = 1) { salonRepository.findById(salonId) }
            verify(exactly = 1) { notificationSettingsRepository.findBySalonId(salonId) }
            verify(exactly = 1) { notificationSettingsRepository.save(any()) }
        }

        @Test
        fun `should throw EntityNotFoundException when salon not found`() {
            // Given
            val salonId = SalonId.generate()
            val command = UpdateNotificationSettingsCommand(
                salonId = salonId,
                pushNotificationsEnabled = true,
                soundPreference = SoundPreference.DEFAULT,
                vibrationEnabled = true,
                doNotDisturb = DoNotDisturbSettings(),
                notificationRules = NotificationRules()
            )

            every { salonRepository.findById(salonId) } returns null

            // When & Then
            val exception = assertThrows<EntityNotFoundException> {
                useCase.updateNotificationSettings(command)
            }

            assertTrue(exception.message!!.contains("Salon not found"))
            verify(exactly = 1) { salonRepository.findById(salonId) }
            verify(exactly = 0) { notificationSettingsRepository.findBySalonId(any()) }
            verify(exactly = 0) { notificationSettingsRepository.save(any()) }
        }

        @Test
        fun `should handle all notification types correctly`() {
            // Given
            val salonId = SalonId.generate()
            val command = UpdateNotificationSettingsCommand(
                salonId = salonId,
                pushNotificationsEnabled = true,
                soundPreference = SoundPreference.CUSTOM3,
                vibrationEnabled = true,
                doNotDisturb = DoNotDisturbSettings(
                    enabled = false,
                    startTime = LocalTime.of(0, 0),
                    endTime = LocalTime.of(0, 1),
                    allowCritical = true
                ),
                notificationRules = NotificationRules(
                    newAppointments = true,
                    appointmentCancellations = true,
                    paymentConfirmations = true,
                    teamMemberUpdates = true,
                    systemMaintenanceAlerts = true
                )
            )

            val salon = TestDataBuilder.aSalon().withId(salonId).build()
            val existingSettings = NotificationSettings.createDefault(salonId)
            val updatedSettings = existingSettings.updateSettings(
                pushNotificationsEnabled = command.pushNotificationsEnabled,
                soundPreference = command.soundPreference,
                vibrationEnabled = command.vibrationEnabled,
                doNotDisturb = command.doNotDisturb,
                notificationRules = command.notificationRules
            )

            every { salonRepository.findById(salonId) } returns salon
            every { notificationSettingsRepository.findBySalonId(salonId) } returns existingSettings
            every { notificationSettingsRepository.save(any()) } returns updatedSettings

            // When
            val result = useCase.updateNotificationSettings(command)

            // Then
            assertTrue(result.pushNotificationsEnabled)
            assertEquals(SoundPreference.CUSTOM3, result.soundPreference)
            assertTrue(result.vibrationEnabled)
            assertFalse(result.doNotDisturb.enabled)
            assertTrue(result.notificationRules.newAppointments)
            assertTrue(result.notificationRules.appointmentCancellations)
            assertTrue(result.notificationRules.paymentConfirmations)
            assertTrue(result.notificationRules.teamMemberUpdates)
            assertTrue(result.notificationRules.systemMaintenanceAlerts)
        }
    }
}
