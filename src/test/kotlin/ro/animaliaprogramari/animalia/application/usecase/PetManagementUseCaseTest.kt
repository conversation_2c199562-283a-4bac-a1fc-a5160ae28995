package ro.animaliaprogramari.animalia.application.usecase

import io.mockk.*
import org.junit.jupiter.api.*
import ro.animaliaprogramari.animalia.application.command.ActivatePetCommand
import ro.animaliaprogramari.animalia.application.command.AddPetCommand
import ro.animaliaprogramari.animalia.application.command.DeactivatePetCommand
import ro.animaliaprogramari.animalia.application.command.UpdatePetCommand
import ro.animaliaprogramari.animalia.application.port.outbound.ClientRepository
import ro.animaliaprogramari.animalia.application.port.outbound.DomainEventPublisher
import ro.animaliaprogramari.animalia.application.port.outbound.PetRepository
import ro.animaliaprogramari.animalia.application.query.GetAllPetsQuery
import ro.animaliaprogramari.animalia.application.query.GetPetByIdQuery
import ro.animaliaprogramari.animalia.application.query.GetPetsByClientQuery
import ro.animaliaprogramari.animalia.application.query.SearchPetsQuery
import ro.animaliaprogramari.animalia.domain.event.PetAddedEvent
import ro.animaliaprogramari.animalia.domain.exception.EntityNotFoundException
import ro.animaliaprogramari.animalia.domain.model.ClientId
import ro.animaliaprogramari.animalia.domain.model.Gender
import ro.animaliaprogramari.animalia.domain.model.PetId
import ro.animaliaprogramari.animalia.test.TestDataBuilder
import java.math.BigDecimal
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

/**
 * Unit tests for PetManagementUseCaseImpl
 * Tests all pet lifecycle operations with comprehensive coverage
 */
@DisplayName("PetManagementUseCase")
class PetManagementUseCaseTest {

    // Mocked dependencies
    private val petRepository = mockk<PetRepository>()
    private val clientRepository = mockk<ClientRepository>()
    private val domainEventPublisher = mockk<DomainEventPublisher>()

    // System under test
    private lateinit var useCase: PetManagementUseCaseImpl

    @BeforeEach
    fun setUp() {
        clearAllMocks()
        useCase = PetManagementUseCaseImpl(
            petRepository = petRepository,
            clientRepository = clientRepository,
            domainEventPublisher = domainEventPublisher
        )
    }

    @Nested
    @DisplayName("Add Pet")
    inner class AddPetTests {

        @Test
        fun `should add pet successfully when client exists`() {
            // Given
            val clientId = ClientId.generate()
            val command = AddPetCommand(
                clientId = clientId,
                name = "Buddy",
                breed = "Golden Retriever",
                age = 3,
                weight = BigDecimal("25.5"),
                color = "Golden",
                gender = Gender.MALE,
                notes = "Friendly dog",
                medicalConditions = "None"
            )

            val client = TestDataBuilder.aClient().withId(clientId).build()
            val expectedPet = TestDataBuilder.aPet()
                .withClientId(clientId)
                .withName("Buddy")
                .withBreed("Golden Retriever")
                .withAge(3)
                .withWeight(BigDecimal("25.5"))
                .withColor("Golden")
                .withGender(Gender.MALE)
                .withNotes("Friendly dog")
                .withMedicalConditions("None")
                .build()

            every { clientRepository.findById(clientId) } returns client
            every { petRepository.save(any()) } returns expectedPet
            every { domainEventPublisher.publish(any()) } just Runs

            // When
            val result = useCase.addPet(command)

            // Then
            assertNotNull(result)
            assertEquals(clientId, result.clientId)
            assertEquals("Buddy", result.name)
            assertEquals("Golden Retriever", result.breed)
            assertEquals(3, result.age)
            assertEquals(BigDecimal("25.5"), result.weight)
            assertEquals("Golden", result.color)
            assertEquals(Gender.MALE, result.gender)
            assertEquals("Friendly dog", result.notes)
            assertEquals("None", result.medicalConditions)
            assertTrue(result.isActive)

            // Verify interactions
            verify(exactly = 1) { clientRepository.findById(clientId) }
            verify(exactly = 1) { petRepository.save(any()) }
            verify(exactly = 1) { domainEventPublisher.publish(any<PetAddedEvent>()) }
        }

        @Test
        fun `should add pet with minimal information when optional fields are null`() {
            // Given
            val clientId = ClientId.generate()
            val command = AddPetCommand(
                clientId = clientId,
                name = "Max",
                breed = null,
                age = null,
                weight = null,
                color = null,
                gender = null,
                notes = null,
                medicalConditions = null
            )

            val client = TestDataBuilder.aClient().withId(clientId).build()
            val expectedPet = TestDataBuilder.aPet()
                .withClientId(clientId)
                .withName("Max")
                .withBreed(null)
                .withAge(null)
                .withWeight(null)
                .withColor(null)
                .withGender(null)
                .withNotes(null)
                .withMedicalConditions(null)
                .build()

            every { clientRepository.findById(clientId) } returns client
            every { petRepository.save(any()) } returns expectedPet
            every { domainEventPublisher.publish(any()) } just Runs

            // When
            val result = useCase.addPet(command)

            // Then
            assertNotNull(result)
            assertEquals(clientId, result.clientId)
            assertEquals("Max", result.name)
            assertNull(result.breed)
            assertNull(result.age)
            assertNull(result.weight)
            assertNull(result.color)
            assertNull(result.gender)
            assertNull(result.notes)
            assertNull(result.medicalConditions)

            verify(exactly = 1) { clientRepository.findById(clientId) }
            verify(exactly = 1) { petRepository.save(any()) }
            verify(exactly = 1) { domainEventPublisher.publish(any<PetAddedEvent>()) }
        }

        @Test
        fun `should throw EntityNotFoundException when client not found`() {
            // Given
            val clientId = ClientId.generate()
            val command = AddPetCommand(
                clientId = clientId,
                name = "Buddy"
            )

            every { clientRepository.findById(clientId) } returns null

            // When & Then
            val exception = assertThrows<EntityNotFoundException> {
                useCase.addPet(command)
            }

            assertTrue(exception.message!!.contains("Client"))
            assertTrue(exception.message!!.contains(clientId.value))

            verify(exactly = 1) { clientRepository.findById(clientId) }
            verify(exactly = 0) { petRepository.save(any()) }
            verify(exactly = 0) { domainEventPublisher.publish(any()) }
        }

        @Test
        fun `should publish domain event with correct pet information`() {
            // Given
            val clientId = ClientId.generate()
            val command = AddPetCommand(
                clientId = clientId,
                name = "Luna",
                breed = "Labrador"
            )

            val client = TestDataBuilder.aClient().withId(clientId).build()
            val savedPet = TestDataBuilder.aPet()
                .withClientId(clientId)
                .withName("Luna")
                .withBreed("Labrador")
                .build()

            every { clientRepository.findById(clientId) } returns client
            every { petRepository.save(any()) } returns savedPet
            every { domainEventPublisher.publish(any()) } just Runs

            // When
            useCase.addPet(command)

            // Then
            verify(exactly = 1) { 
                domainEventPublisher.publish(match<PetAddedEvent> { event ->
                    event.petId == savedPet.id &&
                    event.clientId == clientId &&
                    event.petName == "Luna" &&
                    event.breed == "Labrador"
                })
            }
        }
    }

    @Nested
    @DisplayName("Update Pet")
    inner class UpdatePetTests {

        @Test
        fun `should update pet successfully when pet exists`() {
            // Given
            val petId = PetId.generate()
            val command = UpdatePetCommand(
                petId = petId,
                name = "Updated Buddy",
                breed = "Golden Retriever Mix",
                age = 4,
                weight = BigDecimal("27.0"),
                color = "Light Golden",
                gender = Gender.MALE,
                notes = "Updated notes",
                medicalConditions = "Hip dysplasia"
            )

            val existingPet = TestDataBuilder.aPet()
                .withId(petId)
                .withName("Buddy")
                .withBreed("Golden Retriever")
                .withAge(3)
                .build()

            val updatedPet = existingPet.update(
                name = command.name,
                breed = command.breed,
                age = command.age,
                weight = command.weight,
                color = command.color,
                gender = command.gender,
                notes = command.notes,
                medicalConditions = command.medicalConditions
            )

            every { petRepository.findById(petId) } returns existingPet
            every { petRepository.save(any()) } returns updatedPet

            // When
            val result = useCase.updatePet(command)

            // Then
            assertNotNull(result)
            assertEquals("Updated Buddy", result.name)
            assertEquals("Golden Retriever Mix", result.breed)
            assertEquals(4, result.age)
            assertEquals(BigDecimal("27.0"), result.weight)
            assertEquals("Light Golden", result.color)
            assertEquals(Gender.MALE, result.gender)
            assertEquals("Updated notes", result.notes)
            assertEquals("Hip dysplasia", result.medicalConditions)

            verify(exactly = 1) { petRepository.findById(petId) }
            verify(exactly = 1) { petRepository.save(any()) }
        }

        @Test
        fun `should throw EntityNotFoundException when pet not found`() {
            // Given
            val petId = PetId.generate()
            val command = UpdatePetCommand(
                petId = petId,
                name = "Updated Name"
            )

            every { petRepository.findById(petId) } returns null

            // When & Then
            val exception = assertThrows<EntityNotFoundException> {
                useCase.updatePet(command)
            }

            assertTrue(exception.message!!.contains("Pet"))
            assertTrue(exception.message!!.contains(petId.value))

            verify(exactly = 1) { petRepository.findById(petId) }
            verify(exactly = 0) { petRepository.save(any()) }
        }

        @Test
        fun `should update only specified fields and keep others unchanged`() {
            // Given
            val petId = PetId.generate()
            val command = UpdatePetCommand(
                petId = petId,
                name = "New Name",
                breed = "New Breed"
                // Other fields not specified
            )

            val existingPet = TestDataBuilder.aPet()
                .withId(petId)
                .withName("Old Name")
                .withBreed("Old Breed")
                .withAge(5)
                .withWeight(BigDecimal("20.0"))
                .withColor("Brown")
                .withGender(Gender.FEMALE)
                .withNotes("Old notes")
                .withMedicalConditions("Old conditions")
                .build()

            val updatedPet = existingPet.update(
                name = command.name,
                breed = command.breed
            )

            every { petRepository.findById(petId) } returns existingPet
            every { petRepository.save(any()) } returns updatedPet

            // When
            val result = useCase.updatePet(command)

            // Then
            assertEquals("New Name", result.name)
            assertEquals("New Breed", result.breed)
            // Other fields should remain unchanged
            assertEquals(5, result.age)
            assertEquals(BigDecimal("20.0"), result.weight)
            assertEquals("Brown", result.color)
            assertEquals(Gender.FEMALE, result.gender)
            assertEquals("Old notes", result.notes)
            assertEquals("Old conditions", result.medicalConditions)
        }
    }

    @Nested
    @DisplayName("Deactivate Pet")
    inner class DeactivatePetTests {

        @Test
        fun `should deactivate pet successfully when pet exists`() {
            // Given
            val petId = PetId.generate()
            val command = DeactivatePetCommand(petId = petId)

            val activePet = TestDataBuilder.aPet()
                .withId(petId)
                .withActive(true)
                .build()

            val deactivatedPet = activePet.deactivate()

            every { petRepository.findById(petId) } returns activePet
            every { petRepository.save(any()) } returns deactivatedPet

            // When
            val result = useCase.deactivatePet(command)

            // Then
            assertNotNull(result)
            assertFalse(result.isActive)

            verify(exactly = 1) { petRepository.findById(petId) }
            verify(exactly = 1) { petRepository.save(any()) }
        }

        @Test
        fun `should throw EntityNotFoundException when pet not found`() {
            // Given
            val petId = PetId.generate()
            val command = DeactivatePetCommand(petId = petId)

            every { petRepository.findById(petId) } returns null

            // When & Then
            val exception = assertThrows<EntityNotFoundException> {
                useCase.deactivatePet(command)
            }

            assertTrue(exception.message!!.contains("Pet"))
            assertTrue(exception.message!!.contains(petId.value))

            verify(exactly = 1) { petRepository.findById(petId) }
            verify(exactly = 0) { petRepository.save(any()) }
        }
    }

    @Nested
    @DisplayName("Activate Pet")
    inner class ActivatePetTests {

        @Test
        fun `should activate pet successfully when pet exists`() {
            // Given
            val petId = PetId.generate()
            val command = ActivatePetCommand(petId = petId)

            val inactivePet = TestDataBuilder.aPet()
                .withId(petId)
                .withActive(false)
                .build()

            val activatedPet = inactivePet.activate()

            every { petRepository.findById(petId) } returns inactivePet
            every { petRepository.save(any()) } returns activatedPet

            // When
            val result = useCase.activatePet(command)

            // Then
            assertNotNull(result)
            assertTrue(result.isActive)

            verify(exactly = 1) { petRepository.findById(petId) }
            verify(exactly = 1) { petRepository.save(any()) }
        }

        @Test
        fun `should throw EntityNotFoundException when pet not found`() {
            // Given
            val petId = PetId.generate()
            val command = ActivatePetCommand(petId = petId)

            every { petRepository.findById(petId) } returns null

            // When & Then
            val exception = assertThrows<EntityNotFoundException> {
                useCase.activatePet(command)
            }

            assertTrue(exception.message!!.contains("Pet"))
            assertTrue(exception.message!!.contains(petId.value))

            verify(exactly = 1) { petRepository.findById(petId) }
            verify(exactly = 0) { petRepository.save(any()) }
        }
    }

    @Nested
    @DisplayName("Get Pet By ID")
    inner class GetPetByIdTests {

        @Test
        fun `should return pet when pet exists`() {
            // Given
            val petId = PetId.generate()
            val query = GetPetByIdQuery(petId = petId)

            val expectedPet = TestDataBuilder.aPet().withId(petId).build()

            every { petRepository.findById(petId) } returns expectedPet

            // When
            val result = useCase.getPetById(query)

            // Then
            assertNotNull(result)
            assertEquals(petId, result.id)

            verify(exactly = 1) { petRepository.findById(petId) }
        }

        @Test
        fun `should return null when pet not found`() {
            // Given
            val petId = PetId.generate()
            val query = GetPetByIdQuery(petId = petId)

            every { petRepository.findById(petId) } returns null

            // When
            val result = useCase.getPetById(query)

            // Then
            assertNull(result)

            verify(exactly = 1) { petRepository.findById(petId) }
        }
    }

    @Nested
    @DisplayName("Get Pets By Client")
    inner class GetPetsByClientTests {

        @Test
        fun `should return all pets for client when activeOnly is false`() {
            // Given
            val clientId = ClientId.generate()
            val query = GetPetsByClientQuery(clientId = clientId, activeOnly = false)

            val activePet = TestDataBuilder.aPet().withClientId(clientId).withActive(true).build()
            val inactivePet = TestDataBuilder.aPet().withClientId(clientId).withActive(false).build()
            val expectedPets = listOf(activePet, inactivePet)

            every { petRepository.findByClientId(clientId, false) } returns expectedPets

            // When
            val result = useCase.getPetsByClient(query)

            // Then
            assertEquals(2, result.size)
            assertTrue(result.contains(activePet))
            assertTrue(result.contains(inactivePet))

            verify(exactly = 1) { petRepository.findByClientId(clientId, false) }
        }

        @Test
        fun `should return only active pets when activeOnly is true`() {
            // Given
            val clientId = ClientId.generate()
            val query = GetPetsByClientQuery(clientId = clientId, activeOnly = true)

            val activePet = TestDataBuilder.aPet().withClientId(clientId).withActive(true).build()
            val expectedPets = listOf(activePet)

            every { petRepository.findByClientId(clientId, true) } returns expectedPets

            // When
            val result = useCase.getPetsByClient(query)

            // Then
            assertEquals(1, result.size)
            assertTrue(result.contains(activePet))

            verify(exactly = 1) { petRepository.findByClientId(clientId, true) }
        }

        @Test
        fun `should return empty list when client has no pets`() {
            // Given
            val clientId = ClientId.generate()
            val query = GetPetsByClientQuery(clientId = clientId, activeOnly = false)

            every { petRepository.findByClientId(clientId, false) } returns emptyList()

            // When
            val result = useCase.getPetsByClient(query)

            // Then
            assertTrue(result.isEmpty())

            verify(exactly = 1) { petRepository.findByClientId(clientId, false) }
        }
    }

    @Nested
    @DisplayName("Search Pets")
    inner class SearchPetsTests {

        @Test
        fun `should search pets with all parameters`() {
            // Given
            val clientId = ClientId.generate()
            val query = SearchPetsQuery(
                searchTerm = "Golden",
                clientId = clientId,
                isActive = true,
                limit = 10,
                offset = 0
            )

            val expectedPets = listOf(
                TestDataBuilder.aPet().withName("Golden Buddy").withClientId(clientId).build(),
                TestDataBuilder.aPet().withBreed("Golden Retriever").withClientId(clientId).build()
            )

            every {
                petRepository.findAll(
                    search = "Golden",
                    clientId = clientId,
                    isActive = true,
                    limit = 10,
                    offset = 0
                )
            } returns expectedPets

            // When
            val result = useCase.searchPets(query)

            // Then
            assertEquals(2, result.size)
            assertEquals(expectedPets, result)

            verify(exactly = 1) {
                petRepository.findAll(
                    search = "Golden",
                    clientId = clientId,
                    isActive = true,
                    limit = 10,
                    offset = 0
                )
            }
        }

        @Test
        fun `should search pets with minimal parameters`() {
            // Given
            val query = SearchPetsQuery(
                searchTerm = "Buddy",
                clientId = null,
                isActive = null,
                limit = null,
                offset = null
            )

            val expectedPets = listOf(
                TestDataBuilder.aPet().withName("Buddy").build()
            )

            every {
                petRepository.findAll(
                    search = "Buddy",
                    clientId = null,
                    isActive = null,
                    limit = null,
                    offset = null
                )
            } returns expectedPets

            // When
            val result = useCase.searchPets(query)

            // Then
            assertEquals(1, result.size)
            assertEquals(expectedPets, result)

            verify(exactly = 1) {
                petRepository.findAll(
                    search = "Buddy",
                    clientId = null,
                    isActive = null,
                    limit = null,
                    offset = null
                )
            }
        }

        @Test
        fun `should return empty list when no pets match search criteria`() {
            // Given
            val query = SearchPetsQuery(
                searchTerm = "NonExistentPet",
                clientId = null,
                isActive = true,
                limit = 10,
                offset = 0
            )

            every {
                petRepository.findAll(
                    search = "NonExistentPet",
                    clientId = null,
                    isActive = true,
                    limit = 10,
                    offset = 0
                )
            } returns emptyList()

            // When
            val result = useCase.searchPets(query)

            // Then
            assertTrue(result.isEmpty())

            verify(exactly = 1) {
                petRepository.findAll(
                    search = "NonExistentPet",
                    clientId = null,
                    isActive = true,
                    limit = 10,
                    offset = 0
                )
            }
        }
    }

    @Nested
    @DisplayName("Get All Pets")
    inner class GetAllPetsTests {

        @Test
        fun `should get all pets with filters`() {
            // Given
            val clientId = ClientId.generate()
            val query = GetAllPetsQuery(
                clientId = clientId,
                isActive = true,
                limit = 20,
                offset = 0
            )

            val expectedPets = listOf(
                TestDataBuilder.aPet().withClientId(clientId).withActive(true).build(),
                TestDataBuilder.aPet().withClientId(clientId).withActive(true).build()
            )

            every {
                petRepository.findAll(
                    search = null,
                    clientId = clientId,
                    isActive = true,
                    limit = 20,
                    offset = 0
                )
            } returns expectedPets

            // When
            val result = useCase.getAllPets(query)

            // Then
            assertEquals(2, result.size)
            assertEquals(expectedPets, result)

            verify(exactly = 1) {
                petRepository.findAll(
                    search = null,
                    clientId = clientId,
                    isActive = true,
                    limit = 20,
                    offset = 0
                )
            }
        }

        @Test
        fun `should get all pets without filters`() {
            // Given
            val query = GetAllPetsQuery(
                clientId = null,
                isActive = null,
                limit = null,
                offset = null
            )

            val expectedPets = listOf(
                TestDataBuilder.aPet().withActive(true).build(),
                TestDataBuilder.aPet().withActive(false).build(),
                TestDataBuilder.aPet().withActive(true).build()
            )

            every {
                petRepository.findAll(
                    search = null,
                    clientId = null,
                    isActive = null,
                    limit = null,
                    offset = null
                )
            } returns expectedPets

            // When
            val result = useCase.getAllPets(query)

            // Then
            assertEquals(3, result.size)
            assertEquals(expectedPets, result)

            verify(exactly = 1) {
                petRepository.findAll(
                    search = null,
                    clientId = null,
                    isActive = null,
                    limit = null,
                    offset = null
                )
            }
        }

        @Test
        fun `should handle pagination correctly`() {
            // Given
            val query = GetAllPetsQuery(
                clientId = null,
                isActive = true,
                limit = 5,
                offset = 10
            )

            val expectedPets = listOf(
                TestDataBuilder.aPet().withActive(true).build()
            )

            every {
                petRepository.findAll(
                    search = null,
                    clientId = null,
                    isActive = true,
                    limit = 5,
                    offset = 10
                )
            } returns expectedPets

            // When
            val result = useCase.getAllPets(query)

            // Then
            assertEquals(1, result.size)
            assertEquals(expectedPets, result)

            verify(exactly = 1) {
                petRepository.findAll(
                    search = null,
                    clientId = null,
                    isActive = true,
                    limit = 5,
                    offset = 10
                )
            }
        }
    }
}
