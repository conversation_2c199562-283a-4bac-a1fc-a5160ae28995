package ro.animaliaprogramari.animalia.application.usecase

import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.*
import ro.animaliaprogramari.animalia.application.command.*
import ro.animaliaprogramari.animalia.application.port.outbound.*
import ro.animaliaprogramari.animalia.application.query.GetUserByFirebaseUidQuery
import ro.animaliaprogramari.animalia.application.query.GetUserByIdQuery
import ro.animaliaprogramari.animalia.domain.exception.EntityNotFoundException
import ro.animaliaprogramari.animalia.domain.model.Email
import ro.animaliaprogramari.animalia.domain.model.UserId
import ro.animaliaprogramari.animalia.domain.model.UserRole
import ro.animaliaprogramari.animalia.test.TestDataBuilder
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

/**
 * Unit tests for UserManagementUseCaseImpl
 * Tests the user management business logic without external dependencies
 */
@DisplayName("UserManagementUseCase")
class UserManagementUseCaseTest {

    // Mocked dependencies
    private val userRepository = mockk<UserRepository>()
    private val staffRepository = mockk<StaffRepository>()
    private val clientRepository = mockk<ClientRepository>()
    private val appointmentRepository = mockk<AppointmentRepository>()

    // System under test
    private lateinit var useCase: UserManagementUseCaseImpl

    @BeforeEach
    fun setUp() {
        clearAllMocks()
        useCase = UserManagementUseCaseImpl(
            userRepository = userRepository,
            staffRepository = staffRepository,
            clientRepository = clientRepository,
            appointmentRepository = appointmentRepository
        )
    }

    @Nested
    @DisplayName("Create User")
    inner class CreateUserTests {

        @Test
        fun `should create user successfully with valid data`() {
            // Given
            val command = CreateUserCommand(
                firebaseUid = "firebase_uid_123",
                email = Email.of("<EMAIL>"),
                name = "John Doe",
                role = UserRole.STAFF
            )

            val expectedUser = TestDataBuilder.aUser()
                .withFirebaseUid(command.firebaseUid)
                .withEmail(command.email)
                .withName(command.name)
                .withRole(command.role)
                .build()

            every { userRepository.save(any()) } returns expectedUser

            // When
            val result = useCase.createUser(command)

            // Then
            assertNotNull(result)
            assertEquals(command.firebaseUid, result.firebaseUid)
            assertEquals(command.email, result.email)
            assertEquals(command.name, result.name)
            assertEquals(command.role, result.role)
            assertTrue(result.isActive)

            verify(exactly = 1) { userRepository.save(any()) }
        }

        @Test
        fun `should create admin user successfully`() {
            // Given
            val command = CreateUserCommand(
                firebaseUid = "admin_firebase_uid",
                email = Email.of("<EMAIL>"),
                name = "Admin User",
                role = UserRole.ADMIN
            )

            val expectedUser = TestDataBuilder.aUser()
                .withFirebaseUid(command.firebaseUid)
                .withEmail(command.email)
                .withName(command.name)
                .withRole(UserRole.ADMIN)
                .build()

            every { userRepository.save(any()) } returns expectedUser

            // When
            val result = useCase.createUser(command)

            // Then
            assertNotNull(result)
            assertEquals(UserRole.ADMIN, result.role)
            assertEquals(command.firebaseUid, result.firebaseUid)

            verify(exactly = 1) { userRepository.save(any()) }
        }
    }

    @Nested
    @DisplayName("Update User")
    inner class UpdateUserTests {

        @Test
        fun `should update user successfully when user exists`() {
            // Given
            val userId = UserId.generate()
            val command = UpdateUserCommand(
                userId = userId,
                name = "Updated Name",
                email = Email.of("<EMAIL>")
            )

            val existingUser = TestDataBuilder.aUser()
                .withId(userId)
                .withName("Original Name")
                .withEmail(Email.of("<EMAIL>"))
                .build()

            val updatedUser = existingUser.updateInfo(
                name = command.name,
                email = command.email
            )

            every { userRepository.findById(userId) } returns existingUser
            every { userRepository.save(any()) } returns updatedUser

            // When
            val result = useCase.updateUser(command)

            // Then
            assertNotNull(result)
            assertEquals(command.name, result.name)
            assertEquals(command.email, result.email)
            assertEquals(userId, result.id)

            verify(exactly = 1) { userRepository.findById(userId) }
            verify(exactly = 1) { userRepository.save(any()) }
        }

        @Test
        fun `should throw EntityNotFoundException when user not found`() {
            // Given
            val userId = UserId.generate()
            val command = UpdateUserCommand(
                userId = userId,
                name = "Updated Name",
                email = Email.of("<EMAIL>")
            )

            every { userRepository.findById(userId) } returns null

            // When & Then
            val exception = assertThrows<EntityNotFoundException> {
                useCase.updateUser(command)
            }

            assertTrue(exception.message!!.contains("User"))
            assertTrue(exception.message!!.contains(userId.value))

            verify(exactly = 1) { userRepository.findById(userId) }
            verify(exactly = 0) { userRepository.save(any()) }
        }

        @Test
        fun `should update user with partial data`() {
            // Given
            val userId = UserId.generate()
            val command = UpdateUserCommand(
                userId = userId,
                name = "New Name Only",
                email = null
            )

            val existingUser = TestDataBuilder.aUser()
                .withId(userId)
                .withName("Original Name")
                .withEmail(Email.of("<EMAIL>"))
                .build()

            val updatedUser = existingUser.updateInfo(
                name = command.name,
                email = command.email
            )

            every { userRepository.findById(userId) } returns existingUser
            every { userRepository.save(any()) } returns updatedUser

            // When
            val result = useCase.updateUser(command)

            // Then
            assertNotNull(result)
            assertEquals(command.name, result.name)
            assertEquals(userId, result.id)

            verify(exactly = 1) { userRepository.findById(userId) }
            verify(exactly = 1) { userRepository.save(any()) }
        }
    }

    @Nested
    @DisplayName("Deactivate User")
    inner class DeactivateUserTests {

        @Test
        fun `should deactivate user successfully when user exists`() {
            // Given
            val userId = UserId.generate()
            val command = DeactivateUserCommand(userId = userId)

            val activeUser = TestDataBuilder.aUser()
                .withId(userId)
                .withActive(true)
                .build()

            val deactivatedUser = activeUser.deactivate()

            every { userRepository.findById(userId) } returns activeUser
            every { userRepository.save(any()) } returns deactivatedUser

            // When
            val result = useCase.deactivateUser(command)

            // Then
            assertNotNull(result)
            assertEquals(userId, result.id)
            assertEquals(false, result.isActive)

            verify(exactly = 1) { userRepository.findById(userId) }
            verify(exactly = 1) { userRepository.save(any()) }
        }

        @Test
        fun `should throw EntityNotFoundException when user not found for deactivation`() {
            // Given
            val userId = UserId.generate()
            val command = DeactivateUserCommand(userId = userId)

            every { userRepository.findById(userId) } returns null

            // When & Then
            val exception = assertThrows<EntityNotFoundException> {
                useCase.deactivateUser(command)
            }

            assertTrue(exception.message!!.contains("User"))
            assertTrue(exception.message!!.contains(userId.value))

            verify(exactly = 1) { userRepository.findById(userId) }
            verify(exactly = 0) { userRepository.save(any()) }
        }
    }

    @Nested
    @DisplayName("Activate User")
    inner class ActivateUserTests {

        @Test
        fun `should activate user successfully when user exists`() {
            // Given
            val userId = UserId.generate()
            val command = ActivateUserCommand(userId = userId)

            val inactiveUser = TestDataBuilder.aUser()
                .withId(userId)
                .withActive(false)
                .build()

            val activatedUser = inactiveUser.activate()

            every { userRepository.findById(userId) } returns inactiveUser
            every { userRepository.save(any()) } returns activatedUser

            // When
            val result = useCase.activateUser(command)

            // Then
            assertNotNull(result)
            assertEquals(userId, result.id)
            assertEquals(true, result.isActive)

            verify(exactly = 1) { userRepository.findById(userId) }
            verify(exactly = 1) { userRepository.save(any()) }
        }

        @Test
        fun `should throw EntityNotFoundException when user not found for activation`() {
            // Given
            val userId = UserId.generate()
            val command = ActivateUserCommand(userId = userId)

            every { userRepository.findById(userId) } returns null

            // When & Then
            val exception = assertThrows<EntityNotFoundException> {
                useCase.activateUser(command)
            }

            assertTrue(exception.message!!.contains("User"))
            assertTrue(exception.message!!.contains(userId.value))

            verify(exactly = 1) { userRepository.findById(userId) }
            verify(exactly = 0) { userRepository.save(any()) }
        }
    }

    @Nested
    @DisplayName("Update User Role")
    inner class UpdateUserRoleTests {

        @Test
        fun `should update user role successfully when user exists`() {
            // Given
            val userId = UserId.generate()
            val command = UpdateUserRoleCommand(
                userId = userId,
                role = UserRole.ADMIN
            )

            val existingUser = TestDataBuilder.aUser()
                .withId(userId)
                .withRole(UserRole.STAFF)
                .build()

            val updatedUser = existingUser.updateRole(UserRole.ADMIN)

            every { userRepository.findById(userId) } returns existingUser
            every { userRepository.save(any()) } returns updatedUser

            // When
            val result = useCase.updateUserRole(command)

            // Then
            assertNotNull(result)
            assertEquals(UserRole.ADMIN, result.role)
            assertEquals(userId, result.id)

            verify(exactly = 1) { userRepository.findById(userId) }
            verify(exactly = 1) { userRepository.save(any()) }
        }

        @Test
        fun `should throw EntityNotFoundException when user not found for role update`() {
            // Given
            val userId = UserId.generate()
            val command = UpdateUserRoleCommand(
                userId = userId,
                role = UserRole.ADMIN
            )

            every { userRepository.findById(userId) } returns null

            // When & Then
            val exception = assertThrows<EntityNotFoundException> {
                useCase.updateUserRole(command)
            }

            assertTrue(exception.message!!.contains("User"))
            assertTrue(exception.message!!.contains(userId.value))

            verify(exactly = 1) { userRepository.findById(userId) }
            verify(exactly = 0) { userRepository.save(any()) }
        }
    }

    @Nested
    @DisplayName("Get User by ID")
    inner class GetUserByIdTests {

        @Test
        fun `should return user when found by ID`() {
            // Given
            val userId = UserId.generate()
            val query = GetUserByIdQuery(userId = userId)

            val user = TestDataBuilder.aUser()
                .withId(userId)
                .build()

            every { userRepository.findById(userId) } returns user

            // When
            val result = useCase.getUserById(query)

            // Then
            assertNotNull(result)
            assertEquals(userId, result!!.id)
            assertEquals(user.name, result.name)
            assertEquals(user.email, result.email)

            verify(exactly = 1) { userRepository.findById(userId) }
        }

        @Test
        fun `should return null when user not found by ID`() {
            // Given
            val userId = UserId.generate()
            val query = GetUserByIdQuery(userId = userId)

            every { userRepository.findById(userId) } returns null

            // When
            val result = useCase.getUserById(query)

            // Then
            assertNull(result)

            verify(exactly = 1) { userRepository.findById(userId) }
        }
    }

    @Nested
    @DisplayName("Get User by Firebase UID")
    inner class GetUserByFirebaseUidTests {

        @Test
        fun `should return user when found by Firebase UID`() {
            // Given
            val firebaseUid = "firebase_uid_123"
            val query = GetUserByFirebaseUidQuery(firebaseUid = firebaseUid)

            val user = TestDataBuilder.aUser()
                .withFirebaseUid(firebaseUid)
                .build()

            every { userRepository.findByFirebaseUid(firebaseUid) } returns user

            // When
            val result = useCase.getUserByFirebaseUid(query)

            // Then
            assertNotNull(result)
            assertEquals(firebaseUid, result!!.firebaseUid)
            assertEquals(user.id, result.id)

            verify(exactly = 1) { userRepository.findByFirebaseUid(firebaseUid) }
        }

        @Test
        fun `should return null when user not found by Firebase UID`() {
            // Given
            val firebaseUid = "nonexistent_firebase_uid"
            val query = GetUserByFirebaseUidQuery(firebaseUid = firebaseUid)

            every { userRepository.findByFirebaseUid(firebaseUid) } returns null

            // When
            val result = useCase.getUserByFirebaseUid(query)

            // Then
            assertNull(result)

            verify(exactly = 1) { userRepository.findByFirebaseUid(firebaseUid) }
        }
    }
}