package ro.animaliaprogramari.animalia.application.usecase

import io.mockk.*
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import ro.animaliaprogramari.animalia.application.command.*
import ro.animaliaprogramari.animalia.application.port.outbound.*
import ro.animaliaprogramari.animalia.domain.exception.*
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.domain.service.*
import ro.animaliaprogramari.animalia.domain.service.ValidationResult
import ro.animaliaprogramari.animalia.testutil.TestDataBuilder
import java.time.ZonedDateTime

/**
 * Focused unit tests for BlockTimeManagementUseCaseImpl
 * Tests core business logic with proper mocking
 */
@DisplayName("BlockTimeManagementUseCase")
@Disabled // Disabled until we have a proper test environment set up
class BlockTimeManagementUseCaseTest {

    private lateinit var useCase: BlockTimeManagementUseCaseImpl
    private lateinit var blockTimeRepository: BlockTimeRepository
    private lateinit var appointmentRepository: AppointmentRepository
    private lateinit var staffRepository: StaffRepository
    private lateinit var userRepository: UserRepository
    private lateinit var salonRepository: SalonRepository
    private lateinit var schedulingService: BlockTimeSchedulingService
    private lateinit var conflictService: SchedulingConflictService


    @BeforeEach
    fun setUp() {
        blockTimeRepository = mockk(relaxed = true)
        appointmentRepository = mockk(relaxed = true)
        staffRepository = mockk(relaxed = true)
        userRepository = mockk(relaxed = true)
        salonRepository = mockk(relaxed = true)
        schedulingService = mockk(relaxed = true)
        conflictService = mockk(relaxed = true)
        useCase = BlockTimeManagementUseCaseImpl(
            blockTimeRepository = blockTimeRepository,
            appointmentRepository = appointmentRepository,
            staffRepository = staffRepository,
            userRepository = userRepository,
            salonRepository = salonRepository,
            workingHoursRepository = mockk(),
            staffWorkingHoursRepository = mockk(),
            blockTimeSchedulingService = schedulingService,
            schedulingConflictService = conflictService
        )
    }

    @Nested
    @DisplayName("Create Block Time")
    inner class CreateBlockTime {

        @Test
        fun `should create block time successfully when no conflicts`() {
            // Given
            val command = CreateBlockTimeCommand(
                salonId = SalonId.of("salon-1"),
                startTime = ZonedDateTime.now().plusHours(1),
                endTime = ZonedDateTime.now().plusHours(3),
                reason = BlockReason.PAUZA,
                staffIds = setOf(StaffId.of("staff-1")),
                createdBy = UserId.of("user-1")
            )

            val salon = TestDataBuilder.aSalon(id = command.salonId)
            val staff = TestDataBuilder.aStaff(
                userId = command.createdBy,
                salonId = command.salonId,
                role = StaffRole.CHIEF_GROOMER,
                permissions = StaffPermissions(
                    clientDataAccess = ClientDataAccess.LIMITED,
                    canManageAppointments = true,
                    canManageServices = false,
                    canViewReports = false,
                    canManageSchedule = true
                )
            )
            val savedBlockTime = TestDataBuilder.aBlockTime(
                salonId = command.salonId,
                startTime = command.startTime,
                endTime = command.endTime,
                reason = command.reason,
                staffIds = command.staffIds,
                createdBy = command.createdBy
            )

            // Mock all required dependencies
            every { staffRepository.findByUserIdAndSalonId(command.createdBy, command.salonId) } returns staff
            every { salonRepository.findById(command.salonId) } returns salon
            every { staffRepository.findByUserIdAndSalonId(UserId.of("staff-1"), command.salonId) } returns staff
            every { staffRepository.findById(StaffId.of("staff-1")) } returns staff
            every { blockTimeRepository.findOverlappingBlocks(any(), any(), any(), any()) } returns emptyList()
            every { appointmentRepository.findBySalonIdAndStaffIdsAndTimeRange(any(), any(), any(), any()) } returns emptyList()
            every { schedulingService.validateBlockTimeCreation(any(), any(), any(), any(), any(), any()) } returns ValidationResult.success()
            every { blockTimeRepository.save(any()) } returns savedBlockTime

            // When
            val result = useCase.createBlockTime(command)

            // Then
            assertNotNull(result)
            assertEquals(savedBlockTime.id, result.blockTime.id)
            assertTrue(result.affectedAppointments.isEmpty())

            verify { blockTimeRepository.save(any()) }
            verify { schedulingService.validateBlockTimeCreation(any(), any(), any(), any(), any(), any()) }
        }

        @Test
        fun `should throw exception when user lacks permission`() {
            // Given
            val command = CreateBlockTimeCommand(
                salonId = SalonId.of("salon-1"),
                startTime = ZonedDateTime.now().plusHours(1),
                endTime = ZonedDateTime.now().plusHours(3),
                reason = BlockReason.PAUZA,
                staffIds = setOf(StaffId.of("staff-1")),
                createdBy = UserId.of("user-1")
            )

            val staffWithoutPermissions = TestDataBuilder.aStaff(
                userId = command.createdBy,
                salonId = command.salonId,
                role = StaffRole.GROOMER,
                permissions = StaffPermissions(
                    clientDataAccess = ClientDataAccess.LIMITED,
                    canManageAppointments = true,
                    canManageServices = false,
                    canViewReports = false,
                    canManageSchedule = false
                )
            )
            every { staffRepository.findByUserIdAndSalonId(command.createdBy, command.salonId) } returns staffWithoutPermissions

            // When & Then
            val exception = assertThrows<UnauthorizedException> {
                useCase.createBlockTime(command)
            }
            assertEquals("User does not have permission to update block times in this salon", exception.message)

            verify(exactly = 0) { blockTimeRepository.save(any()) }
        }

        @Test
        fun `should throw exception when salon not found`() {
            // Given
            val command = CreateBlockTimeCommand(
                salonId = SalonId.of("salon-1"),
                startTime = ZonedDateTime.now().plusHours(1),
                endTime = ZonedDateTime.now().plusHours(3),
                reason = BlockReason.PAUZA,
                staffIds = setOf(StaffId.of("staff-1")),
                createdBy = UserId.of("user-1")
            )

            val staff = TestDataBuilder.aStaff(
                userId = command.createdBy,
                salonId = command.salonId,
                role = StaffRole.CHIEF_GROOMER,
                permissions = StaffPermissions.fullAccess()
            )

            every { staffRepository.findByUserIdAndSalonId(command.createdBy, command.salonId) } returns staff
            every { salonRepository.findById(command.salonId) } returns null

            // When & Then
            val exception = assertThrows<EntityNotFoundException> {
                useCase.createBlockTime(command)
            }
            assertEquals("Salon not found: ${command.salonId.value}", exception.message)
        }

        @Test
        fun `should handle conflicts and return affected appointments`() {
            // Given
            val command = CreateBlockTimeCommand(
                salonId = SalonId.of("salon-1"),
                startTime = ZonedDateTime.now().plusHours(1),
                endTime = ZonedDateTime.now().plusHours(3),
                reason = BlockReason.PAUZA,
                staffIds = setOf(StaffId.of("staff-1")),
                createdBy = UserId.of("user-1")
            )

            val salon = TestDataBuilder.aSalon(id = command.salonId)
            val staff = TestDataBuilder.aStaff(
                userId = command.createdBy,
                salonId = command.salonId,
                role = StaffRole.CHIEF_GROOMER
            )
            val conflictingAppointment = TestDataBuilder.anAppointment(
                staffId = StaffId.of("staff-1"),
                appointmentDate = command.startTime.toLocalDate(),
                startTime = command.startTime.toLocalTime().plusMinutes(30),
                endTime = command.endTime.toLocalTime().minusMinutes(30)
            )
            val savedBlockTime = TestDataBuilder.aBlockTime(
                salonId = command.salonId,
                startTime = command.startTime,
                endTime = command.endTime,
                reason = command.reason,
                staffIds = command.staffIds,
                createdBy = command.createdBy
            )

            // Staff already has permissions set in the builder above
            every { staffRepository.findByUserIdAndSalonId(command.createdBy, command.salonId) } returns staff
            every { salonRepository.findById(command.salonId) } returns salon
            every { staffRepository.findByUserIdAndSalonId(UserId.of("staff-1"), command.salonId) } returns staff
            every { staffRepository.findById(StaffId.of("staff-1")) } returns staff
            every { blockTimeRepository.findOverlappingBlocks(any(), any(), any(), any()) } returns emptyList()
            every { appointmentRepository.findBySalonIdAndStaffIdsAndTimeRange(any(), any(), any(), any()) } returns listOf(conflictingAppointment)
            every { schedulingService.validateBlockTimeCreation(any(), any(), any(), any(), any(), any()) } returns ValidationResult.success()
            every { blockTimeRepository.save(any()) } returns savedBlockTime

            // When
            val result = useCase.createBlockTime(command)

            // Then
            assertNotNull(result)
            assertEquals(savedBlockTime.id, result.blockTime.id)
            assertEquals(1, result.affectedAppointments.size)
            assertEquals(conflictingAppointment.id.value, result.affectedAppointments.first().appointmentId)
        }

        @Test
        fun `should create recurring block time with pattern`() {
            // Given
            val recurrencePattern = TestDataBuilder.aRecurrencePattern(
                type = RecurrenceType.WEEKLY,
                interval = 1,
                daysOfWeek = setOf(java.time.DayOfWeek.MONDAY),
                occurrences = 4
            )
            val command = CreateBlockTimeCommand(
                salonId = SalonId.of("salon-1"),
                startTime = ZonedDateTime.now().plusHours(1),
                endTime = ZonedDateTime.now().plusHours(3),
                reason = BlockReason.PAUZA,
                staffIds = setOf(StaffId.of("staff-1")),
                isRecurring = true,
                recurrencePattern = recurrencePattern,
                createdBy = UserId.of("user-1")
            )

            val salon = TestDataBuilder.aSalon(id = command.salonId)
            val staff = TestDataBuilder.aStaff(
                userId = command.createdBy,
                salonId = command.salonId,
                role = StaffRole.CHIEF_GROOMER
            )
            val savedBlockTime = TestDataBuilder.aBlockTime(
                salonId = command.salonId,
                startTime = command.startTime,
                endTime = command.endTime,
                reason = command.reason,
                staffIds = command.staffIds,
                isRecurring = true,
                recurrencePattern = recurrencePattern,
                createdBy = command.createdBy
            )

            every { salonRepository.findById(command.salonId) } returns salon
            every { staffRepository.findByUserIdAndSalonId(command.createdBy, command.salonId) } returns staff
            // Staff already has permissions set in the builder above
            every { staffRepository.findByUserIdAndSalonId(UserId.of("staff-1"), command.salonId) } returns staff
            every { staffRepository.findById(StaffId.of("staff-1")) } returns staff
            every { blockTimeRepository.findOverlappingBlocks(any(), any(), any(), any()) } returns emptyList()
            every { appointmentRepository.findBySalonIdAndStaffIdsAndTimeRange(any(), any(), any(), any()) } returns emptyList()
            every { schedulingService.validateBlockTimeCreation(any(), any(), any(), any(), any(), any()) } returns ValidationResult.success()
            every { blockTimeRepository.save(any()) } returns savedBlockTime

            // When
            val result = useCase.createBlockTime(command)

            // Then
            assertNotNull(result)
            assertEquals(savedBlockTime.id, result.blockTime.id)
            assertTrue(result.blockTime.isRecurring)
            assertEquals(recurrencePattern, result.blockTime.recurrencePattern)
        }
    }

    @Nested
    @DisplayName("Update Block Time")
    inner class UpdateBlockTime {

        @Test
        fun `should update block time successfully`() {
            // Given
            val blockId = BlockTimeId.of("block-1")
            val command = UpdateBlockTimeCommand(
                salonId = SalonId.of("salon-1"),
                blockId = blockId,
                reason = BlockReason.INTALNIRE,
                notes = "Updated notes",
                updatedBy = UserId.of("user-1")
            )

            val existingBlock = TestDataBuilder.aBlockTime(
                id = blockId,
                salonId = command.salonId,
                reason = BlockReason.PAUZA,
                notes = "Original notes"
            )
            val updatedBlock = existingBlock.update(
                reason = command.reason,
                notes = command.notes,
                updatedBy = command.updatedBy
            )

            val updaterStaff = TestDataBuilder.aStaff(
                userId = command.updatedBy,
                salonId = command.salonId,
                role = StaffRole.CHIEF_GROOMER,
                permissions = StaffPermissions.fullAccess()
            )
            every { staffRepository.findByUserIdAndSalonId(command.updatedBy, command.salonId) } returns updaterStaff
            every { blockTimeRepository.findByIdAndSalonId(blockId, command.salonId) } returns existingBlock
            every { blockTimeRepository.update(any()) } returns updatedBlock

            // When
            val result = useCase.updateBlockTime(command)

            // Then
            assertNotNull(result)
            assertEquals(BlockReason.INTALNIRE, result.blockTime.reason)
            assertEquals("Updated notes", result.blockTime.notes)
            assertEquals(command.updatedBy, result.blockTime.updatedBy)

            verify { blockTimeRepository.update(any()) }
        }

        @Test
        fun `should throw exception when block not found`() {
            // Given
            val command = UpdateBlockTimeCommand(
                salonId = SalonId.of("salon-1"),
                blockId = BlockTimeId.of("block-1"),
                reason = BlockReason.INTALNIRE,
                updatedBy = UserId.of("user-1")
            )

            val updaterStaff = TestDataBuilder.aStaff(
                userId = command.updatedBy,
                salonId = command.salonId,
                role = StaffRole.CHIEF_GROOMER,
                permissions = StaffPermissions.fullAccess()
            )
            every { staffRepository.findByUserIdAndSalonId(command.updatedBy, command.salonId) } returns updaterStaff
            every { blockTimeRepository.findByIdAndSalonId(command.blockId, command.salonId) } returns null

            // When & Then
            val exception = assertThrows<EntityNotFoundException> {
                useCase.updateBlockTime(command)
            }
            assertEquals("Block time not found: ${command.blockId.value}", exception.message)
        }
    }

    @Nested
    @DisplayName("Delete Block Time")
    inner class DeleteBlockTime {

        @Test
        fun `should delete block time successfully`() {
            // Given
            val blockId = BlockTimeId.of("block-1")
            val command = DeleteBlockTimeCommand(
                salonId = SalonId.of("salon-1"),
                blockId = blockId,
                reason = "No longer needed",
                deletedBy = UserId.of("user-1")
            )

            val existingBlock = TestDataBuilder.aBlockTime(
                id = blockId,
                salonId = command.salonId,
                status = BlockTimeStatus.ACTIVE
            )
            val cancelledBlock = existingBlock.cancel(command.deletedBy, command.reason)

            val deleterStaff = TestDataBuilder.aStaff(
                userId = command.deletedBy,
                salonId = command.salonId,
                role = StaffRole.CHIEF_GROOMER,
                permissions = StaffPermissions.fullAccess()
            )
            every { staffRepository.findByUserIdAndSalonId(command.deletedBy, command.salonId) } returns deleterStaff
            every { blockTimeRepository.findByIdAndSalonId(blockId, command.salonId) } returns existingBlock
            every { blockTimeRepository.update(any()) } returns cancelledBlock

            // When
            val result = useCase.deleteBlockTime(command)

            // Then
            assertNotNull(result)
            assertEquals("CANCELLED", result.status)
            assertEquals(command.deletedBy.value, result.cancelledBy)
            assertEquals(command.reason, result.cancellationReason)

            verify { blockTimeRepository.update(any()) }
        }

        @Test
        fun `should throw exception when trying to delete already cancelled block`() {
            // Given
            val blockId = BlockTimeId.of("block-1")
            val command = DeleteBlockTimeCommand(
                salonId = SalonId.of("salon-1"),
                blockId = blockId,
                deletedBy = UserId.of("user-1")
            )

            val cancelledBlock = TestDataBuilder.aBlockTime(
                id = blockId,
                salonId = command.salonId,
                status = BlockTimeStatus.CANCELLED
            )

            val deleterStaff = TestDataBuilder.aStaff(
                userId = command.deletedBy,
                salonId = command.salonId,
                role = StaffRole.CHIEF_GROOMER,
                permissions = StaffPermissions.fullAccess()
            )
            every { staffRepository.findByUserIdAndSalonId(command.deletedBy, command.salonId) } returns deleterStaff
            every { blockTimeRepository.findByIdAndSalonId(blockId, command.salonId) } returns cancelledBlock

            // When & Then
            val exception = assertThrows<BlockTimeOperationException> {
                useCase.deleteBlockTime(command)
            }
            assertEquals("Block time is already cancelled or expired", exception.message)
        }
    }
}
