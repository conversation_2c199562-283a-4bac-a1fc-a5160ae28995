package ro.animaliaprogramari.animalia.application.usecase

import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.*
import ro.animaliaprogramari.animalia.application.command.SendSalonInvitationCommand
import ro.animaliaprogramari.animalia.application.port.inbound.InvitationManagementUseCase
import ro.animaliaprogramari.animalia.application.port.outbound.SalonInvitationRepository
import ro.animaliaprogramari.animalia.application.port.outbound.StaffRepository
import ro.animaliaprogramari.animalia.application.port.outbound.UserRepository
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.test.TestDataBuilder
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

/**
 * Unit tests for SalonStaffManagementUseCaseImpl
 * Tests all salon staff management operations with comprehensive coverage
 */
@DisplayName("SalonStaffManagementUseCase")
class SalonStaffManagementUseCaseTest {

    // Mocked dependencies
    private val staffRepository = mockk<StaffRepository>()
    private val userRepository = mockk<UserRepository>()
    private val invitationManagementUseCase = mockk<InvitationManagementUseCase>()
    private val invitationRepository = mockk<SalonInvitationRepository>()

    // System under test
    private lateinit var useCase: SalonStaffManagementUseCaseImpl

    @BeforeEach
    fun setUp() {
        clearAllMocks()
        useCase = SalonStaffManagementUseCaseImpl(
            staffRepository = staffRepository,
            userRepository = userRepository,
            invitationManagementUseCase = invitationManagementUseCase,
            invitationRepository = invitationRepository
        )
    }

    @Nested
    @DisplayName("Get Salon Staff")
    inner class GetSalonStaffTests {

        @Test
        fun `should return active staff with user details when activeOnly is true`() {
            // Given
            val salonId = SalonId.generate()
            val userId1 = UserId.generate()
            val userId2 = UserId.generate()

            val staff1 = TestDataBuilder.aStaff()
                .withUserId(userId1)
                .withSalonId(salonId)
                .withRole(StaffRole.CHIEF_GROOMER)
                .withActive(true)
                .build()

            val staff2 = TestDataBuilder.aStaff()
                .withUserId(userId2)
                .withSalonId(salonId)
                .withRole(StaffRole.GROOMER)
                .withActive(true)
                .build()

            val user1 = TestDataBuilder.aUser()
                .withId(userId1)
                .withName("Chief Groomer")
                .withEmail(Email.of("<EMAIL>"))
                .build()

            val user2 = TestDataBuilder.aUser()
                .withId(userId2)
                .withName("Regular Groomer")
                .withEmail(Email.of("<EMAIL>"))
                .build()

            every { staffRepository.findActiveBySalonWithUserDetails(salonId) } returns listOf(staff1, staff2)
            every { userRepository.findById(userId1) } returns user1
            every { userRepository.findById(userId2) } returns user2

            // When
            val result = useCase.getSalonStaff(salonId, activeOnly = true, search = null)

            // Then
            assertEquals(2, result.size)
            
            val chiefGroomerInfo = result.find { it.staff.role == StaffRole.CHIEF_GROOMER }
            assertNotNull(chiefGroomerInfo)
            assertEquals("Chief Groomer", chiefGroomerInfo.userName)
            assertEquals(userId1, chiefGroomerInfo.staff.userId)

            val groomerInfo = result.find { it.staff.role == StaffRole.GROOMER }
            assertNotNull(groomerInfo)
            assertEquals("Regular Groomer", groomerInfo.userName)
            assertEquals(userId2, groomerInfo.staff.userId)

            verify(exactly = 1) { staffRepository.findActiveBySalonWithUserDetails(salonId) }
            verify(exactly = 1) { userRepository.findById(userId1) }
            verify(exactly = 1) { userRepository.findById(userId2) }
        }

        @Test
        fun `should return all staff including inactive when activeOnly is false`() {
            // Given
            val salonId = SalonId.generate()
            val userId1 = UserId.generate()
            val userId2 = UserId.generate()

            val activeStaff = TestDataBuilder.aStaff()
                .withUserId(userId1)
                .withSalonId(salonId)
                .withActive(true)
                .build()

            val inactiveStaff = TestDataBuilder.aStaff()
                .withUserId(userId2)
                .withSalonId(salonId)
                .withActive(false)
                .build()

            val user1 = TestDataBuilder.aUser().withId(userId1).withName("Active Staff").build()
            val user2 = TestDataBuilder.aUser().withId(userId2).withName("Inactive Staff").build()

            every { staffRepository.findBySalonWithUserDetails(salonId) } returns listOf(activeStaff, inactiveStaff)
            every { userRepository.findById(userId1) } returns user1
            every { userRepository.findById(userId2) } returns user2

            // When
            val result = useCase.getSalonStaff(salonId, activeOnly = false, search = null)

            // Then
            assertEquals(2, result.size)
            assertTrue(result.any { it.staff.isActive })
            assertTrue(result.any { !it.staff.isActive })

            verify(exactly = 1) { staffRepository.findBySalonWithUserDetails(salonId) }
            verify(exactly = 1) { userRepository.findById(userId1) }
            verify(exactly = 1) { userRepository.findById(userId2) }
        }

        @Test
        fun `should filter staff by search term in name`() {
            // Given
            val salonId = SalonId.generate()
            val userId1 = UserId.generate()
            val userId2 = UserId.generate()

            val staff1 = TestDataBuilder.aStaff().withUserId(userId1).withSalonId(salonId).build()
            val staff2 = TestDataBuilder.aStaff().withUserId(userId2).withSalonId(salonId).build()

            val user1 = TestDataBuilder.aUser().withId(userId1).withName("John Smith").build()
            val user2 = TestDataBuilder.aUser().withId(userId2).withName("Jane Doe").build()

            every { staffRepository.findActiveBySalonWithUserDetails(salonId) } returns listOf(staff1, staff2)
            every { userRepository.findById(userId1) } returns user1
            every { userRepository.findById(userId2) } returns user2

            // When
            val result = useCase.getSalonStaff(salonId, activeOnly = true, search = "John")

            // Then
            assertEquals(1, result.size)
            assertEquals("John Smith", result[0].userName)

            verify(exactly = 1) { staffRepository.findActiveBySalonWithUserDetails(salonId) }
            verify(exactly = 1) { userRepository.findById(userId1) }
            verify(exactly = 1) { userRepository.findById(userId2) }
        }

        @Test
        fun `should filter staff by search term in email`() {
            // Given
            val salonId = SalonId.generate()
            val userId1 = UserId.generate()
            val userId2 = UserId.generate()

            val staff1 = TestDataBuilder.aStaff().withUserId(userId1).withSalonId(salonId).build()
            val staff2 = TestDataBuilder.aStaff().withUserId(userId2).withSalonId(salonId).build()

            val user1 = TestDataBuilder.aUser()
                .withId(userId1)
                .withName("John Smith")
                .withEmail(Email.of("<EMAIL>"))
                .build()

            val user2 = TestDataBuilder.aUser()
                .withId(userId2)
                .withName("Jane Doe")
                .withEmail(Email.of("<EMAIL>"))
                .build()

            every { staffRepository.findActiveBySalonWithUserDetails(salonId) } returns listOf(staff1, staff2)
            every { userRepository.findById(userId1) } returns user1
            every { userRepository.findById(userId2) } returns user2

            // When
            val result = useCase.getSalonStaff(salonId, activeOnly = true, search = "example")

            // Then
            assertEquals(1, result.size)
            assertEquals("<EMAIL>", result[0].userEmail)

            verify(exactly = 1) { staffRepository.findActiveBySalonWithUserDetails(salonId) }
            verify(exactly = 1) { userRepository.findById(userId1) }
            verify(exactly = 1) { userRepository.findById(userId2) }
        }

        @Test
        fun `should return empty list when no staff found`() {
            // Given
            val salonId = SalonId.generate()

            every { staffRepository.findActiveBySalonWithUserDetails(salonId) } returns emptyList()

            // When
            val result = useCase.getSalonStaff(salonId, activeOnly = true, search = null)

            // Then
            assertTrue(result.isEmpty())

            verify(exactly = 1) { staffRepository.findActiveBySalonWithUserDetails(salonId) }
            verify(exactly = 0) { userRepository.findById(any()) }
        }

        @Test
        fun `should exclude staff when user not found`() {
            // Given
            val salonId = SalonId.generate()
            val userId1 = UserId.generate()
            val userId2 = UserId.generate()

            val staff1 = TestDataBuilder.aStaff().withUserId(userId1).withSalonId(salonId).build()
            val staff2 = TestDataBuilder.aStaff().withUserId(userId2).withSalonId(salonId).build()

            val user1 = TestDataBuilder.aUser().withId(userId1).withName("Valid User").build()

            every { staffRepository.findActiveBySalonWithUserDetails(salonId) } returns listOf(staff1, staff2)
            every { userRepository.findById(userId1) } returns user1
            every { userRepository.findById(userId2) } returns null // User not found

            // When
            val result = useCase.getSalonStaff(salonId, activeOnly = true, search = null)

            // Then
            assertEquals(1, result.size)
            assertEquals("Valid User", result[0].userName)

            verify(exactly = 1) { staffRepository.findActiveBySalonWithUserDetails(salonId) }
            verify(exactly = 1) { userRepository.findById(userId1) }
            verify(exactly = 1) { userRepository.findById(userId2) }
        }
    }

    @Nested
    @DisplayName("Get Salon Pending Invitations")
    inner class GetSalonPendingInvitationsTests {

        @Test
        fun `should return pending invitations with phone numbers`() {
            // Given
            val salonId = SalonId.generate()
            val inviterUserId1 = UserId.generate()
            val inviterUserId2 = UserId.generate()

            val invitation1 = TestDataBuilder.aSalonInvitation()
                .withSalonId(salonId)
                .withInviterUserId(inviterUserId1)
                .withInvitedUserPhone("+40731446895")
                .withStatus(InvitationStatus.PENDING)
                .build()

            val invitation2 = TestDataBuilder.aSalonInvitation()
                .withSalonId(salonId)
                .withInviterUserId(inviterUserId2)
                .withInvitedUserPhone("+40712345679")
                .withStatus(InvitationStatus.PENDING)
                .build()

            val inviter1 = TestDataBuilder.aUser().withId(inviterUserId1).withName("Inviter One").build()
            val inviter2 = TestDataBuilder.aUser().withId(inviterUserId2).withName("Inviter Two").build()

            every { invitationRepository.findPendingBySalonId(salonId) } returns listOf(invitation1, invitation2)
            every { userRepository.findById(inviterUserId1) } returns inviter1
            every { userRepository.findById(inviterUserId2) } returns inviter2

            // When
            val result = useCase.getSalonPendingInvitations(salonId, search = null)

            // Then
            assertEquals(2, result.size)
            assertEquals(invitation1, result[0].first)
            assertEquals("Inviter One", result[0].second)
            assertEquals(invitation2, result[1].first)
            assertEquals("Inviter Two", result[1].second)

            verify(exactly = 1) { invitationRepository.findPendingBySalonId(salonId) }
            verify(exactly = 1) { userRepository.findById(inviterUserId1) }
            verify(exactly = 1) { userRepository.findById(inviterUserId2) }
        }

        @Test
        fun `should filter invitations by search term in phone number`() {
            // Given
            val salonId = SalonId.generate()
            val inviterUserId1 = UserId.generate()
            val inviterUserId2 = UserId.generate()

            val invitation1 = TestDataBuilder.aSalonInvitation()
                .withSalonId(salonId)
                .withInviterUserId(inviterUserId1)
                .withInvitedUserPhone("+40731446895")
                .withStatus(InvitationStatus.PENDING)
                .build()

            val invitation2 = TestDataBuilder.aSalonInvitation()
                .withSalonId(salonId)
                .withInviterUserId(inviterUserId2)
                .withInvitedUserPhone("+40798765432")
                .withStatus(InvitationStatus.PENDING)
                .build()

            val inviter1 = TestDataBuilder.aUser().withId(inviterUserId1).withName("Inviter One").build()
            val inviter2 = TestDataBuilder.aUser().withId(inviterUserId2).withName("Inviter Two").build()

            every { invitationRepository.findPendingBySalonId(salonId) } returns listOf(invitation1, invitation2)
            every { userRepository.findById(inviterUserId1) } returns inviter1
            every { userRepository.findById(inviterUserId2) } returns inviter2

            // When
            val result = useCase.getSalonPendingInvitations(salonId, search = "144")

            // Then
            assertEquals(1, result.size)
            assertEquals("Inviter One", result[0].second)

            verify(exactly = 1) { invitationRepository.findPendingBySalonId(salonId) }
            verify(exactly = 1) { userRepository.findById(inviterUserId1) }
            verify(exactly = 1) { userRepository.findById(inviterUserId2) }
        }

        @Test
        fun `should return empty list when no pending invitations found`() {
            // Given
            val salonId = SalonId.generate()

            every { invitationRepository.findPendingBySalonId(salonId) } returns emptyList()

            // When
            val result = useCase.getSalonPendingInvitations(salonId, search = null)

            // Then
            assertTrue(result.isEmpty())

            verify(exactly = 1) { invitationRepository.findPendingBySalonId(salonId) }
        }
    }

    @Nested
    @DisplayName("Add User to Salon")
    inner class AddUserToSalonTests {

        @Test
        fun `should add user as chief groomer successfully`() {
            // Given
            val salonId = SalonId.generate()
            val userId = UserId.generate()
            val role = StaffRole.CHIEF_GROOMER
            val permissions = StaffPermissions.fullAccess()

            val user = TestDataBuilder.aUser().withId(userId).build()
            val expectedStaff = TestDataBuilder.aStaff()
                .withUserId(userId)
                .withSalonId(salonId)
                .withRole(role)
                .withPermissions(permissions)
                .build()

            every { userRepository.findById(userId) } returns user
            every { staffRepository.findByUserIdAndSalonId(userId, salonId) } returns null
            every { staffRepository.save(any()) } returns expectedStaff

            // When
            val result = useCase.addUserToSalon(salonId, userId, role, permissions)

            // Then
            assertNotNull(result)
            assertEquals(userId, result.userId)
            assertEquals(salonId, result.salonId)
            assertEquals(StaffRole.CHIEF_GROOMER, result.role)
            assertEquals(StaffPermissions.fullAccess(), result.permissions)
            assertTrue(result.isActive)

            verify(exactly = 1) { userRepository.findById(userId) }
            verify(exactly = 1) { staffRepository.findByUserIdAndSalonId(userId, salonId) }
            verify(exactly = 1) { staffRepository.save(any()) }
        }

        @Test
        fun `should add user as regular groomer successfully`() {
            // Given
            val salonId = SalonId.generate()
            val userId = UserId.generate()
            val role = StaffRole.GROOMER
            val permissions = StaffPermissions.defaultGroomerAccess()

            val user = TestDataBuilder.aUser().withId(userId).build()
            val expectedStaff = TestDataBuilder.aStaff()
                .withUserId(userId)
                .withSalonId(salonId)
                .withRole(role)
                .withPermissions(permissions)
                .build()

            every { userRepository.findById(userId) } returns user
            every { staffRepository.findByUserIdAndSalonId(userId, salonId) } returns null
            every { staffRepository.save(any()) } returns expectedStaff

            // When
            val result = useCase.addUserToSalon(salonId, userId, role, permissions)

            // Then
            assertNotNull(result)
            assertEquals(userId, result.userId)
            assertEquals(salonId, result.salonId)
            assertEquals(StaffRole.GROOMER, result.role)
            assertEquals(StaffPermissions.defaultGroomerAccess(), result.permissions)

            verify(exactly = 1) { userRepository.findById(userId) }
            verify(exactly = 1) { staffRepository.findByUserIdAndSalonId(userId, salonId) }
            verify(exactly = 1) { staffRepository.save(any()) }
        }

        @Test
        fun `should throw IllegalArgumentException when user not found`() {
            // Given
            val salonId = SalonId.generate()
            val userId = UserId.generate()
            val role = StaffRole.GROOMER
            val permissions = StaffPermissions.defaultGroomerAccess()

            every { userRepository.findById(userId) } returns null

            // When & Then
            val exception = assertThrows<IllegalArgumentException> {
                useCase.addUserToSalon(salonId, userId, role, permissions)
            }

            assertTrue(exception.message!!.contains("Utilizatorul nu a fost găsit"))

            verify(exactly = 1) { userRepository.findById(userId) }
            verify(exactly = 0) { staffRepository.save(any()) }
        }

        @Test
        fun `should throw IllegalArgumentException when user already exists as staff`() {
            // Given
            val salonId = SalonId.generate()
            val userId = UserId.generate()
            val role = StaffRole.GROOMER
            val permissions = StaffPermissions.defaultGroomerAccess()

            val user = TestDataBuilder.aUser().withId(userId).build()
            val existingStaff = TestDataBuilder.aStaff()
                .withUserId(userId)
                .withSalonId(salonId)
                .build()

            every { userRepository.findById(userId) } returns user
            every { staffRepository.findByUserIdAndSalonId(userId, salonId) } returns existingStaff

            // When & Then
            val exception = assertThrows<IllegalArgumentException> {
                useCase.addUserToSalon(salonId, userId, role, permissions)
            }

            assertTrue(exception.message!!.contains("Utilizatorul este deja angajat în acest salon"))

            verify(exactly = 1) { userRepository.findById(userId) }
            verify(exactly = 1) { staffRepository.findByUserIdAndSalonId(userId, salonId) }
            verify(exactly = 0) { staffRepository.save(any()) }
        }
    }

    @Nested
    @DisplayName("Invite Staff by Phone")
    inner class InviteStaffByPhoneTests {

        @Test
        fun `should invite staff by phone successfully`() {
            // Given
            val salonId = SalonId.generate()
            val inviterUserId = UserId.generate()
            val phoneNumber = "+40731446895"
            val role = StaffRole.GROOMER
            val permissions = StaffPermissions.defaultGroomerAccess()
            val message = "Join our team!"

            val expectedCommand = SendSalonInvitationCommand(
                salonId = salonId,
                inviterUserId = inviterUserId,
                invitedUserPhone = phoneNumber,
                proposedRole = role,
                proposedPermissions = permissions,
                message = message
            )

            val expectedInvitation = TestDataBuilder.aSalonInvitation()
                .withSalonId(salonId)
                .withInviterUserId(inviterUserId)
                .withInvitedUserPhone(phoneNumber)
                .withProposedRole(role)
                .withProposedPermissions(permissions)
                .withMessage(message)
                .build()

            every { invitationManagementUseCase.sendInvitation(expectedCommand) } returns expectedInvitation

            // When
            val result = useCase.inviteStaffByPhone(
                salonId = salonId,
                inviterUserId = inviterUserId,
                phoneNumber = phoneNumber,
                role = role,
                permissions = permissions,
                message = message
            )

            // Then
            assertNotNull(result)
            assertEquals(salonId, result.salonId)
            assertEquals(inviterUserId, result.inviterUserId)
            assertEquals(phoneNumber, result.invitedUserPhone)
            assertEquals(role, result.proposedRole)
            assertEquals(permissions, result.proposedPermissions)
            assertEquals(message, result.message)

            verify(exactly = 1) { invitationManagementUseCase.sendInvitation(expectedCommand) }
        }

        @Test
        fun `should invite staff with default permissions when not specified`() {
            // Given
            val salonId = SalonId.generate()
            val inviterUserId = UserId.generate()
            val phoneNumber = "+40731446895"
            val role = StaffRole.GROOMER

            val expectedCommand = SendSalonInvitationCommand(
                salonId = salonId,
                inviterUserId = inviterUserId,
                invitedUserPhone = phoneNumber,
                proposedRole = role,
                proposedPermissions = StaffPermissions.defaultGroomerAccess(),
                message = null
            )

            val expectedInvitation = TestDataBuilder.aSalonInvitation()
                .withSalonId(salonId)
                .withInviterUserId(inviterUserId)
                .withInvitedUserPhone(phoneNumber)
                .withProposedRole(role)
                .withProposedPermissions(StaffPermissions.defaultGroomerAccess())
                .withMessage(null)
                .build()

            every { invitationManagementUseCase.sendInvitation(expectedCommand) } returns expectedInvitation

            // When
            val result = useCase.inviteStaffByPhone(
                salonId = salonId,
                inviterUserId = inviterUserId,
                phoneNumber = phoneNumber,
                role = role,
                permissions = StaffPermissions.defaultGroomerAccess(),
                message = null
            )

            // Then
            assertNotNull(result)
            assertEquals(StaffPermissions.defaultGroomerAccess(), result.proposedPermissions)
            assertNull(result.message)

            verify(exactly = 1) { invitationManagementUseCase.sendInvitation(expectedCommand) }
        }
    }

    @Nested
    @DisplayName("Update Staff Role")
    inner class UpdateStaffRoleTests {

        @Test
        fun `should update staff role and permissions successfully`() {
            // Given
            val salonId = SalonId.generate()
            val userId = UserId.generate()
            val newRole = StaffRole.CHIEF_GROOMER
            val newPermissions = StaffPermissions.fullAccess()

            val existingStaff = TestDataBuilder.aStaff()
                .withUserId(userId)
                .withSalonId(salonId)
                .withRole(StaffRole.GROOMER)
                .withPermissions(StaffPermissions.defaultGroomerAccess())
                .build()

            val updatedStaff = existingStaff.copy(
                role = newRole,
                permissions = newPermissions
            )

            val user = TestDataBuilder.aUser().withId(userId).build()

            every { staffRepository.findByUserIdAndSalonId(userId, salonId) } returns existingStaff
            every { userRepository.findById(userId) } returns user
            every { staffRepository.save(any()) } returns updatedStaff

            // When
            val result = useCase.updateStaffRole(salonId, userId, newRole, newPermissions, null)

            // Then
            assertNotNull(result)
            assertEquals(newRole, result.role)
            assertEquals(newPermissions, result.permissions)

            verify(exactly = 1) { staffRepository.findByUserIdAndSalonId(userId, salonId) }
            verify(exactly = 1) { staffRepository.save(any()) }
        }

        @Test
        fun `should throw IllegalArgumentException when staff not found`() {
            // Given
            val salonId = SalonId.generate()
            val userId = UserId.generate()
            val newRole = StaffRole.CHIEF_GROOMER
            val newPermissions = StaffPermissions.fullAccess()

            every { staffRepository.findByUserIdAndSalonId(userId, salonId) } returns null

            // When & Then
            val exception = assertThrows<IllegalArgumentException> {
                useCase.updateStaffRole(salonId, userId, newRole, newPermissions, null)
            }

            assertTrue(exception.message!!.contains("Angajatul nu a fost găsit în acest salon"))

            verify(exactly = 1) { staffRepository.findByUserIdAndSalonId(userId, salonId) }
            verify(exactly = 0) { staffRepository.save(any()) }
        }
    }

    @Nested
    @DisplayName("Toggle Staff Status")
    inner class ToggleStaffStatusTests {

        @Test
        fun `should deactivate active staff successfully`() {
            // Given
            val salonId = SalonId.generate()
            val userId = UserId.generate()

            val activeStaff = TestDataBuilder.aStaff()
                .withUserId(userId)
                .withSalonId(salonId)
                .withActive(true)
                .build()

            val deactivatedStaff = activeStaff.copy(isActive = false)

            every { staffRepository.findByUserIdAndSalonId(userId, salonId) } returns activeStaff
            every { staffRepository.save(any()) } returns deactivatedStaff

            // When
            val result = useCase.toggleStaffStatus(salonId, userId)

            // Then
            assertNotNull(result)
            assertFalse(result.isActive)

            verify(exactly = 1) { staffRepository.findByUserIdAndSalonId(userId, salonId) }
            verify(exactly = 1) { staffRepository.save(any()) }
        }

        @Test
        fun `should reactivate inactive staff successfully`() {
            // Given
            val salonId = SalonId.generate()
            val userId = UserId.generate()

            val inactiveStaff = TestDataBuilder.aStaff()
                .withUserId(userId)
                .withSalonId(salonId)
                .withActive(false)
                .build()

            val reactivatedStaff = inactiveStaff.copy(isActive = true)

            every { staffRepository.findByUserIdAndSalonId(userId, salonId) } returns inactiveStaff
            every { staffRepository.save(any()) } returns reactivatedStaff

            // When
            val result = useCase.toggleStaffStatus(salonId, userId)

            // Then
            assertNotNull(result)
            assertTrue(result.isActive)

            verify(exactly = 1) { staffRepository.findByUserIdAndSalonId(userId, salonId) }
            verify(exactly = 1) { staffRepository.save(any()) }
        }

        @Test
        fun `should throw IllegalArgumentException when staff not found`() {
            // Given
            val salonId = SalonId.generate()
            val userId = UserId.generate()

            every { staffRepository.findByUserIdAndSalonId(userId, salonId) } returns null

            // When & Then
            val exception = assertThrows<IllegalArgumentException> {
                useCase.toggleStaffStatus(salonId, userId)
            }

            assertTrue(exception.message!!.contains("Angajatul nu a fost găsit în acest salon"))

            verify(exactly = 1) { staffRepository.findByUserIdAndSalonId(userId, salonId) }
            verify(exactly = 0) { staffRepository.save(any()) }
        }
    }

    @Nested
    @DisplayName("Remove Staff from Salon")
    inner class RemoveStaffFromSalonTests {

        @Test
        fun `should remove staff by deactivating successfully`() {
            // Given
            val salonId = SalonId.generate()
            val userId = UserId.generate()

            val activeStaff = TestDataBuilder.aStaff()
                .withUserId(userId)
                .withSalonId(salonId)
                .withActive(true)
                .build()

            val deactivatedStaff = activeStaff.copy(isActive = false)

            every { staffRepository.findByUserIdAndSalonId(userId, salonId) } returns activeStaff
            every { staffRepository.save(any()) } returns deactivatedStaff

            // When
            val result = useCase.removeStaffFromSalon(salonId, userId)

            // Then
            assertNotNull(result)
            assertFalse(result.isActive)

            verify(exactly = 1) { staffRepository.findByUserIdAndSalonId(userId, salonId) }
            verify(exactly = 1) { staffRepository.save(any()) }
        }

        @Test
        fun `should remove already inactive staff successfully`() {
            // Given
            val salonId = SalonId.generate()
            val userId = UserId.generate()

            val inactiveStaff = TestDataBuilder.aStaff()
                .withUserId(userId)
                .withSalonId(salonId)
                .withActive(false)
                .build()

            val stillInactiveStaff = inactiveStaff.copy(isActive = false)

            every { staffRepository.findByUserIdAndSalonId(userId, salonId) } returns inactiveStaff
            every { staffRepository.save(any()) } returns stillInactiveStaff

            // When
            val result = useCase.removeStaffFromSalon(salonId, userId)

            // Then
            assertNotNull(result)
            assertFalse(result.isActive)

            verify(exactly = 1) { staffRepository.findByUserIdAndSalonId(userId, salonId) }
            verify(exactly = 1) { staffRepository.save(any()) }
        }

        @Test
        fun `should throw IllegalArgumentException when staff not found`() {
            // Given
            val salonId = SalonId.generate()
            val userId = UserId.generate()

            every { staffRepository.findByUserIdAndSalonId(userId, salonId) } returns null

            // When & Then
            val exception = assertThrows<IllegalArgumentException> {
                useCase.removeStaffFromSalon(salonId, userId)
            }

            assertTrue(exception.message!!.contains("Angajatul nu a fost găsit în acest salon"))

            verify(exactly = 1) { staffRepository.findByUserIdAndSalonId(userId, salonId) }
            verify(exactly = 0) { staffRepository.save(any()) }
        }
    }
}
