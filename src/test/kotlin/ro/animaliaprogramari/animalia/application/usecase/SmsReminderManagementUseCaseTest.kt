package ro.animaliaprogramari.animalia.application.usecase

import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.*
import ro.animaliaprogramari.animalia.application.command.UpdateSmsReminderSettingsCommand
import ro.animaliaprogramari.animalia.application.port.outbound.SalonRepository
import ro.animaliaprogramari.animalia.application.port.outbound.SmsReminderSettingsRepository
import ro.animaliaprogramari.animalia.application.port.outbound.StaffRepository
import ro.animaliaprogramari.animalia.application.query.GetSmsReminderSettingsQuery
import ro.animaliaprogramari.animalia.domain.exception.BusinessRuleViolationException
import ro.animaliaprogramari.animalia.domain.exception.EntityNotFoundException
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.test.TestDataBuilder
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

/**
 * Unit tests for SmsReminderManagementUseCaseImpl
 * Tests SMS reminder functionality with comprehensive coverage
 */
@DisplayName("SmsReminderManagementUseCase")
class SmsReminderManagementUseCaseTest {

    // Mocked dependencies
    private val smsReminderSettingsRepository = mockk<SmsReminderSettingsRepository>()
    private val salonRepository = mockk<SalonRepository>()
    private val staffRepository = mockk<StaffRepository>()


    // System under test
    private lateinit var useCase: SmsReminderManagementUseCaseImpl

    @BeforeEach
    fun setUp() {
        clearAllMocks()
        useCase = SmsReminderManagementUseCaseImpl(
            smsReminderSettingsRepository = smsReminderSettingsRepository,
            salonRepository = salonRepository,
            staffRepository = staffRepository
        )
    }

    @Nested
    @DisplayName("Get SMS Reminder Settings")
    inner class GetSmsReminderSettingsTests {

        @Test
        fun `should return existing SMS reminder settings when found`() {
            // Given
            val salonId = SalonId.generate()
            val query = GetSmsReminderSettingsQuery(salonId = salonId)

            val salon = TestDataBuilder.aSalon().withId(salonId).build()
            val existingSettings = SmsReminderSettings(
                salonId = salonId,
                appointmentConfirmations = true,
                dayBeforeReminders = true,
                followUpMessages = true,
                selectedProvider = SmsProvider.ORANGE
            )

            every { salonRepository.findById(salonId) } returns salon
            every { smsReminderSettingsRepository.findBySalonId(salonId) } returns existingSettings

            // When
            val result = useCase.getSmsReminderSettings(query)

            // Then
            assertNotNull(result)
            assertEquals(salonId, result.salonId)
            assertTrue(result.appointmentConfirmations)
            assertTrue(result.dayBeforeReminders)
            assertTrue(result.followUpMessages)
            assertEquals(SmsProvider.ORANGE, result.selectedProvider)

            verify(exactly = 1) { salonRepository.findById(salonId) }
            verify(exactly = 1) { smsReminderSettingsRepository.findBySalonId(salonId) }
        }

        @Test
        fun `should create and return default settings when none exist`() {
            // Given
            val salonId = SalonId.generate()
            val query = GetSmsReminderSettingsQuery(salonId = salonId)

            val salon = TestDataBuilder.aSalon().withId(salonId).build()

            every { salonRepository.findById(salonId) } returns salon
            every { smsReminderSettingsRepository.findBySalonId(salonId) } returns null
            // When
            val result = useCase.getSmsReminderSettings(query)

            // Then
            assertNotNull(result)
            assertEquals(salonId, result.salonId)
            assertTrue(result.appointmentConfirmations) // Default value
            assertTrue(result.dayBeforeReminders) // Default value
            assertFalse(result.followUpMessages) // Default value
            assertEquals(SmsProvider.ORANGE, result.selectedProvider) // Default provider

            verify(exactly = 1) { salonRepository.findById(salonId) }
            verify(exactly = 1) { smsReminderSettingsRepository.findBySalonId(salonId) }
        }

        @Test
        fun `should throw EntityNotFoundException when salon not found`() {
            // Given
            val salonId = SalonId.generate()
            val query = GetSmsReminderSettingsQuery(salonId = salonId)

            every { salonRepository.findById(salonId) } returns null

            // When & Then
            val exception = assertThrows<EntityNotFoundException> {
                useCase.getSmsReminderSettings(query)
            }

            assertTrue(exception.message!!.contains("Salon not found"))
            verify(exactly = 1) { salonRepository.findById(salonId) }
            verify(exactly = 0) { smsReminderSettingsRepository.findBySalonId(any()) }
        }
    }

    @Nested
    @DisplayName("Update SMS Reminder Settings")
    inner class UpdateSmsReminderSettingsTests {

        @Test
        fun `should update SMS reminder settings successfully when user is chief groomer`() {
            // Given
            val salonId = SalonId.generate()
            val updaterUserId = UserId.generate()

            val command = UpdateSmsReminderSettingsCommand(
                salonId = salonId,
                updaterUserId = updaterUserId,
                appointmentConfirmations = false,
                dayBeforeReminders = true,
                followUpMessages = false,
                selectedProvider = SmsProvider.VODAFONE
            )

            val salon = TestDataBuilder.aSalon().withId(salonId).build()

            val updaterStaff = TestDataBuilder.aStaff()
                .withUserId(updaterUserId)
                .withSalonId(salonId)
                .withRole(StaffRole.CHIEF_GROOMER)
                .withActive(true)
                .build()

            val existingSettings = SmsReminderSettings.createDefault(salonId)
            val updatedSettings = existingSettings.updateSettings(
                appointmentConfirmations = command.appointmentConfirmations,
                dayBeforeReminders = command.dayBeforeReminders,
                followUpMessages = command.followUpMessages,
                selectedProvider = command.selectedProvider
            )

            every { salonRepository.findById(salonId) } returns salon

            every { staffRepository.findByUserIdAndSalonId(updaterUserId, salonId) } returns updaterStaff
            every { smsReminderSettingsRepository.findBySalonId(salonId) } returns existingSettings
            every { smsReminderSettingsRepository.save(any()) } returns updatedSettings

            // When
            val result = useCase.updateSmsReminderSettings(command)

            // Then
            assertNotNull(result)
            assertEquals(salonId, result.salonId)
            assertFalse(result.appointmentConfirmations)
            assertTrue(result.dayBeforeReminders)
            assertFalse(result.followUpMessages)
            assertEquals(SmsProvider.VODAFONE, result.selectedProvider)

            verify(exactly = 1) { salonRepository.findById(salonId) }

            verify(exactly = 1) { staffRepository.findByUserIdAndSalonId(updaterUserId, salonId) }
            verify(exactly = 1) { smsReminderSettingsRepository.findBySalonId(salonId) }
            verify(exactly = 1) { smsReminderSettingsRepository.save(any()) }
        }

        @Test
        fun `should throw BusinessRuleViolationException when admin has no salon association`() {
            // Given - Based on actual implementation, admin doesn't have special privileges
            val salonId = SalonId.generate()
            val adminUserId = UserId.generate()

            val command = UpdateSmsReminderSettingsCommand(
                salonId = salonId,
                updaterUserId = adminUserId,
                appointmentConfirmations = true,
                dayBeforeReminders = true,
                followUpMessages = true,
                selectedProvider = SmsProvider.ORANGE
            )

            val salon = TestDataBuilder.aSalon().withId(salonId).build()

            every { salonRepository.findById(salonId) } returns salon
            every { staffRepository.findByUserIdAndSalonId(adminUserId, salonId) } returns null // No association

            // When & Then
            val exception = assertThrows<BusinessRuleViolationException> {
                useCase.updateSmsReminderSettings(command)
            }

            assertTrue(exception.message!!.contains("User does not have permission"))
            verify(exactly = 1) { staffRepository.findByUserIdAndSalonId(adminUserId, salonId) }
        }

        @Test
        fun `should throw EntityNotFoundException when salon not found`() {
            // Given
            val command = UpdateSmsReminderSettingsCommand(
                salonId = SalonId.generate(),
                updaterUserId = UserId.generate(),
                appointmentConfirmations = true,
                dayBeforeReminders = false,
                followUpMessages = true,
                selectedProvider = SmsProvider.ORANGE
            )

            every { salonRepository.findById(command.salonId) } returns null

            // When & Then
            val exception = assertThrows<EntityNotFoundException> {
                useCase.updateSmsReminderSettings(command)
            }

            assertTrue(exception.message!!.contains("Salon not found"))
            verify(exactly = 1) { salonRepository.findById(command.salonId) }
        }



        @Test
        fun `should throw BusinessRuleViolationException when user not associated with salon`() {
            // Given
            val command = UpdateSmsReminderSettingsCommand(
                salonId = SalonId.generate(),
                updaterUserId = UserId.generate(),
                appointmentConfirmations = true,
                dayBeforeReminders = false,
                followUpMessages = true,
                selectedProvider = SmsProvider.ORANGE
            )

            val salon = TestDataBuilder.aSalon().withId(command.salonId).build()
            every { salonRepository.findById(command.salonId) } returns salon
            every { staffRepository.findByUserIdAndSalonId(command.updaterUserId, command.salonId) } returns null

            // When & Then
            val exception = assertThrows<BusinessRuleViolationException> {
                useCase.updateSmsReminderSettings(command)
            }

            assertTrue(exception.message!!.contains("User does not have permission"))
            verify(exactly = 1) { staffRepository.findByUserIdAndSalonId(command.updaterUserId, command.salonId) }
        }

        @Test
        fun `should throw BusinessRuleViolationException when user is not chief groomer`() {
            // Given
            val command = UpdateSmsReminderSettingsCommand(
                salonId = SalonId.generate(),
                updaterUserId = UserId.generate(),
                appointmentConfirmations = true,
                dayBeforeReminders = false,
                followUpMessages = true,
                selectedProvider = SmsProvider.ORANGE
            )

            val salon = TestDataBuilder.aSalon().withId(command.salonId).build()

            val updaterStaff = TestDataBuilder.aStaff()
                .withUserId(command.updaterUserId)
                .withSalonId(command.salonId)
                .withRole(StaffRole.GROOMER) // Not chief groomer
                .withActive(true)
                .build()

            every { salonRepository.findById(command.salonId) } returns salon

            every { staffRepository.findByUserIdAndSalonId(command.updaterUserId, command.salonId) } returns updaterStaff

            // When & Then
            val exception = assertThrows<BusinessRuleViolationException> {
                useCase.updateSmsReminderSettings(command)
            }

            assertTrue(exception.message!!.contains("User does not have permission"))
        }

        @Test
        fun `should create new settings when none exist and update them`() {
            // Given
            val salonId = SalonId.generate()
            val updaterUserId = UserId.generate()

            val command = UpdateSmsReminderSettingsCommand(
                salonId = salonId,
                updaterUserId = updaterUserId,
                appointmentConfirmations = false,
                dayBeforeReminders = true,
                followUpMessages = true,
                selectedProvider = SmsProvider.VODAFONE
            )

            val salon = TestDataBuilder.aSalon().withId(salonId).build()

            val updaterStaff = TestDataBuilder.aStaff()
                .withUserId(updaterUserId)
                .withSalonId(salonId)
                .withRole(StaffRole.CHIEF_GROOMER)
                .withActive(true)
                .build()

            val defaultSettings = SmsReminderSettings.createDefault(salonId)
            val updatedSettings = defaultSettings.updateSettings(
                appointmentConfirmations = command.appointmentConfirmations,
                dayBeforeReminders = command.dayBeforeReminders,
                followUpMessages = command.followUpMessages,
                selectedProvider = command.selectedProvider
            )

            every { salonRepository.findById(salonId) } returns salon

            every { staffRepository.findByUserIdAndSalonId(updaterUserId, salonId) } returns updaterStaff
            every { smsReminderSettingsRepository.findBySalonId(salonId) } returns null
            every { smsReminderSettingsRepository.save(any()) } returns updatedSettings

            // When
            val result = useCase.updateSmsReminderSettings(command)

            // Then
            assertNotNull(result)
            assertFalse(result.appointmentConfirmations)
            assertTrue(result.dayBeforeReminders)
            assertTrue(result.followUpMessages)
            assertEquals(SmsProvider.VODAFONE, result.selectedProvider)

            verify(exactly = 1) { smsReminderSettingsRepository.save(any()) }
        }
    }
}
