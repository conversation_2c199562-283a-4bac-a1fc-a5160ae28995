//package ro.animaliaprogramari.animalia.application.usecase
//
//import io.mockk.*
//import org.junit.jupiter.api.*
//import ro.animaliaprogramari.animalia.application.command.MarkInProgressCommand
//import ro.animaliaprogramari.animalia.application.command.MarkNoShowCommand
//import ro.animaliaprogramari.animalia.application.command.RescheduleAppointmentCommand
//import ro.animaliaprogramari.animalia.application.port.outbound.*
//import ro.animaliaprogramari.animalia.application.query.CheckGroomerAvailabilityQuery
//import ro.animaliaprogramari.animalia.application.query.GetAppointmentsByDateRangeQuery
//import ro.animaliaprogramari.animalia.application.query.GetAppointmentsByGroomerQuery
//import ro.animaliaprogramari.animalia.domain.exception.BusinessRuleViolationException
//import ro.animaliaprogramari.animalia.domain.exception.EntityNotFoundException
//import ro.animaliaprogramari.animalia.domain.exception.UnauthorizedException
//import ro.animaliaprogramari.animalia.domain.model.*
//import ro.animaliaprogramari.animalia.domain.service.AppointmentSchedulingService
//
//import ro.animaliaprogramari.animalia.domain.service.PricingService
//import ro.animaliaprogramari.animalia.domain.validation.ValidationService
//import ro.animaliaprogramari.animalia.test.TestCommandBuilder
//import ro.animaliaprogramari.animalia.test.TestDataBuilder
//import java.time.LocalDate
//import java.time.LocalTime
//import kotlin.test.assertEquals
//import kotlin.test.assertNotNull
//import kotlin.test.assertTrue
//
///**
// * Unit tests for AppointmentManagementUseCaseImpl
// * Tests the business logic orchestration without external dependencies
// */
//@DisplayName("AppointmentManagementUseCase")
//class AppointmentManagementUseCaseTest {
//
//    // Mocked dependencies
//    private val appointmentRepository = mockk<AppointmentRepository>()
//    private val clientRepository = mockk<ClientRepository>()
//    private val petRepository = mockk<PetRepository>()
//    private val userRepository = mockk<UserRepository>()
//    private val salonRepository = mockk<SalonRepository>()
//    private val salonServiceRepository = mockk<SalonServiceRepository>()
//    private val staffRepository = mockk<StaffRepository>()
//    private val blockTimeRepository = mockk<BlockTimeRepository>()
//    private val domainEventPublisher = mockk<DomainEventPublisher>()
//    private val appointmentSchedulingService = mockk<AppointmentSchedulingService>()
//    private val pricingService = mockk<PricingService>()
//
//    private val validationService = mockk<ValidationService>()
//
//    // System under test
//    private lateinit var useCase: AppointmentManagementUseCaseImpl
//
//    @BeforeEach
//    fun setUp() {
//        clearAllMocks()
//        useCase = AppointmentManagementUseCaseImpl(
//            appointmentRepository = appointmentRepository,
//            clientRepository = clientRepository,
//            petRepository = petRepository,
//            userRepository = userRepository,
//            salonRepository = salonRepository,
//            salonServiceRepository = salonServiceRepository,
//            staffRepository = staffRepository,
//            blockTimeRepository = blockTimeRepository,
//            domainEventPublisher = domainEventPublisher,
//            appointmentSchedulingService = appointmentSchedulingService,
//            validationService = validationService
//        )
//    }
//
//    // Helper function to create staff with permissions
//    private fun createStaffWithPermissions(
//        userId: UserId,
//        salonId: SalonId,
//        role: StaffRole = StaffRole.GROOMER,
//        canManageAppointments: Boolean = true,
//        canViewReports: Boolean = false,
//        clientDataAccess: ClientDataAccess = ClientDataAccess.LIMITED
//    ): Staff {
//        return TestDataBuilder.aStaff()
//            .withUserId(userId)
//            .withSalonId(salonId)
//            .withRole(role)
//            .withPermissions(StaffPermissions(
//                clientDataAccess = clientDataAccess,
//                canManageAppointments = canManageAppointments,
//                canManageServices = false,
//                canViewReports = canViewReports,
//                canManageSchedule = true
//            ))
//            .build()
//    }
//
//    @Nested
//    @DisplayName("Schedule Appointment")
//    inner class ScheduleAppointmentTests {
//
//        @Test
//        fun `should schedule appointment successfully when all validations pass`() {
//            // Given
//            val salonId = SalonId.generate()
//            val clientId = ClientId.generate()
//            val petId = PetId.generate()
//            val staffId = StaffId.generate()
//            val userId = UserId.generate()
//            val serviceId = ServiceId.generate()
//
//            val command = TestCommandBuilder.aScheduleAppointmentCommand()
//                .withSalonId(salonId)
//                .withClientId(clientId)
//                .withPetId(petId)
//                .withStaffId(staffId)
//                .withServiceIds(listOf(serviceId))
//                .withDate(LocalDate.now().plusDays(1))
//                .withTimeSlot(LocalTime.of(10, 0), LocalTime.of(11, 0))
//                .build()
//
//            val salon = TestDataBuilder.aSalon()
//                .withId(salonId)
//                .withClientIds(setOf(clientId))
//                .build()
//
//            val client = TestDataBuilder.aClient()
//                .withId(clientId)
//                .build()
//
//            val pet = TestDataBuilder.aPet()
//                .withId(petId)
//                .withClientId(clientId)
//                .build()
//
//            val staff = TestDataBuilder.aUser()
//                .withId(userId)
//                .withRole(UserRole.STAFF)
//                .build()
//
//            val staffMember = TestDataBuilder.aStaff()
//                .withUserId(userId)
//                .withSalonId(salonId)
//                .withRole(StaffRole.GROOMER)
//                .withActive(true)
//                .withPermissions(StaffPermissions(
//                    clientDataAccess = ClientDataAccess.LIMITED,
//                    canManageAppointments = true,
//                    canManageServices = false,
//                    canViewReports = false,
//                    canManageSchedule = true
//                ))
//                .build()
//
//            val service = TestDataBuilder.aSalonService()
//                .withId(serviceId)
//                .withSalonId(salonId)
//                .withActive(true)
//                .build()
//
//            val expectedAppointment = TestDataBuilder.anAppointment()
//                .withSalonId(salonId)
//                .withClientId(clientId)
//                .withPetId(petId)
//                .withStaffId(staffId)
//                .withServiceIds(listOf(serviceId))
//                .build()
//
//            // Mock validation service
//            every { validationService.validateScheduleAppointment(command) } returns mockk {
//                every { throwIfInvalid() } just Runs
//            }
//
//            // Staff member already has permissions set in the builder above
//
//            // Mock repository calls
//            every { salonRepository.findById(salonId) } returns salon
//            every { userRepository.findById(userid) } returns staff
//            every { staffRepository.findByUserIdAndSalonId(staffId, salonId) } returns staffMember
//            every { clientRepository.findById(clientId) } returns client
//            every { petRepository.findById(petId) } returns pet
//            every { salonServiceRepository.findById(serviceId) } returns service
//            every { appointmentRepository.findBySalonIdAndStaffIdAndDate(salonId, staffId, command.appointmentDate) } returns emptyList()
//            every { appointmentRepository.save(any()) } returns expectedAppointment
//
//            // Mock block time repository (no conflicts)
//            every { blockTimeRepository.findOverlappingBlocks(any(), any(), any(), any()) } returns emptyList()
//
//            // Mock domain services
//            every { appointmentSchedulingService.validateTimeSlotAvailability(any(), any(), any(), any(), any()) } just Runs
//
//            // When
//            val result = useCase.scheduleAppointment(command)
//
//            // Then
//            assertNotNull(result)
//            assertEquals(expectedAppointment.id, result.id)
//            assertEquals(salonId, result.salonId)
//            assertEquals(clientId, result.clientId)
//            assertEquals(petId, result.petId)
//            assertEquals(staffId, result.staffId)
//
//            // Verify interactions
//            verify(exactly = 1) { validationService.validateScheduleAppointment(command) }
//            verify(exactly = 1) { staffRepository.findByUserIdAndSalonId(staffId, salonId) }
//            verify(atLeast = 1) { salonRepository.findById(salonId) } // Called multiple times during validation
//            verify(exactly = 1) { userRepository.findById(userid) }
//            verify(exactly = 1) { clientRepository.findById(clientId) }
//            verify(exactly = 1) { petRepository.findById(petId) }
//            verify(exactly = 1) { salonServiceRepository.findById(serviceId) }
//            verify(exactly = 1) { appointmentRepository.save(any()) }
//        }
//
//        @Test
//        fun `should throw EntityNotFoundException when salon not found`() {
//            // Given
//            val command = TestCommandBuilder.aScheduleAppointmentCommand().build()
//
//            every { validationService.validateScheduleAppointment(command) } returns mockk {
//                every { throwIfInvalid() } just Runs
//            }
//            every { salonRepository.findById(command.salonId) } returns null
//
//            // When & Then
//            val exception = assertThrows<EntityNotFoundException> {
//                useCase.scheduleAppointment(command)
//            }
//
//            assertTrue(exception.message!!.contains("Salon not found"))
//            verify(exactly = 1) { salonRepository.findById(command.salonId) }
//        }
//
//        @Test
//        fun `should throw UnauthorizedException when staff has no permission`() {
//            // Given
//            val command = TestCommandBuilder.aScheduleAppointmentCommand().build()
//            val salon = TestDataBuilder.aSalon().withId(command.salonId).build()
//
//            every { validationService.validateScheduleAppointment(command) } returns mockk {
//                every { throwIfInvalid() } just Runs
//            }
//            every { salonRepository.findById(command.salonId) } returns salon
//            val staffWithoutPermissions = createStaffWithPermissions(
//                userId = command.staffId,
//                salonId = command.salonId,
//                canManageAppointments = false
//            )
//            every { staffRepository.findByUserIdAndSalonId(command.staffId, command.salonId) } returns staffWithoutPermissions
//
//            // When & Then
//            val exception = assertThrows<UnauthorizedException> {
//                useCase.scheduleAppointment(command)
//            }
//
//            assertTrue(exception.message!!.contains("does not have permission"))
//            verify(exactly = 1) { staffRepository.findByUserIdAndSalonId(command.staffId, command.salonId) }
//        }
//
//        @Test
//        fun `should throw EntityNotFoundException when staff not found`() {
//            // Given
//            val command = TestCommandBuilder.aScheduleAppointmentCommand().build()
//            val salon = TestDataBuilder.aSalon().withId(command.salonId).build()
//
//            every { validationService.validateScheduleAppointment(command) } returns mockk {
//                every { throwIfInvalid() } just Runs
//            }
//            every { salonRepository.findById(command.salonId) } returns salon
//            // Staff permissions handled by createStaffWithPermissions helper
//            every { userRepository.findById(command.staffId) } returns null
//
//            // When & Then
//            val exception = assertThrows<EntityNotFoundException> {
//                useCase.scheduleAppointment(command)
//            }
//
//            assertTrue(exception.message!!.contains("Staff member not found"))
//            verify(exactly = 1) { userRepository.findById(command.staffId) }
//        }
//
//        @Test
//        fun `should throw BusinessRuleViolationException when staff is not active in salon`() {
//            // Given
//            val command = TestCommandBuilder.aScheduleAppointmentCommand().build()
//            val salon = TestDataBuilder.aSalon().withId(command.salonId).build()
//            val staff = TestDataBuilder.aUser().withId(command.staffId).build()
//            val inactiveStaff = TestDataBuilder.aStaff()
//                .withUserId(command.staffId)
//                .withSalonId(command.salonId)
//                .withActive(false)
//                .build()
//
//            every { validationService.validateScheduleAppointment(command) } returns mockk {
//                every { throwIfInvalid() } just Runs
//            }
//            every { salonRepository.findById(command.salonId) } returns salon
//            // Staff permissions handled by createStaffWithPermissions helper
//            every { userRepository.findById(command.staffId) } returns staff
//            every { staffRepository.findByUserIdAndSalonId(command.staffId, command.salonId) } returns inactiveStaff
//
//            // When & Then
//            val exception = assertThrows<BusinessRuleViolationException> {
//                useCase.scheduleAppointment(command)
//            }
//
//            assertTrue(exception.message!!.contains("Staff member is not active"))
//            verify(exactly = 1) { staffRepository.findByUserIdAndSalonId(command.staffId, command.salonId) }
//        }
//
//        @Test
//        fun `should create new client when client not found and data provided`() {
//            // Given
//            val salonId = SalonId.generate()
//            val clientId = ClientId.generate()
//            val petId = PetId.generate()
//            val staffId = StaffId.generate()
//            val userid = UserId.generate()
//            val serviceId = ServiceId.generate()
//
//            val command = TestCommandBuilder.aScheduleAppointmentCommand()
//                .withSalonId(salonId)
//                .withClientId(clientId)
//                .withNewClient("New Client", PhoneNumber.of("+***********"))
//                .withPetId(petId)
//                .withNewPet("New Pet", "Dog")
//                .withStaffId(staffId)
//                .withServiceIds(listOf(serviceId))
//                .build()
//
//            val salon = TestDataBuilder.aSalon().withId(salonId).build()
//            val staff = TestDataBuilder.aUser().withId(userid).build()
//            val staffMember = TestDataBuilder.aStaff()
//                .withUserId(userid)
//                .withSalonId(salonId)
//                .withActive(true)
//                .build()
//            val service = TestDataBuilder.aSalonService().withId(serviceId).withSalonId(salonId).build()
//
//            val newClient = TestDataBuilder.aClient()
//                .withId(clientId)
//                .withName("New Client")
//                .withPhone(PhoneNumber.of("+***********"))
//                .build()
//
//            val newPet = TestDataBuilder.aPet()
//                .withId(petId)
//                .withClientId(clientId)
//                .withName("New Pet")
//                .withBreed("Dog")
//                .build()
//
//            val expectedAppointment = TestDataBuilder.anAppointment()
//                .withSalonId(salonId)
//                .withClientId(clientId)
//                .withPetId(petId)
//                .withStaffId(staffId)
//                .build()
//
//            // Setup mocks
//            every { validationService.validateScheduleAppointment(command) } returns mockk {
//                every { throwIfInvalid() } just Runs
//            }
//            // Staff permissions handled by createStaffWithPermissions helper
//            every { salonRepository.findById(salonId) } returns salon
//            every { salonRepository.save(any()) } returns salon.addClient(newClient.id)
//            every { userRepository.findById(userid) } returns staff
//            every { staffRepository.findByUserIdAndSalonId(userid, salonId) } returns staffMember
//            every { clientRepository.findById(clientId) } returns null andThen newClient
//            every { clientRepository.save(any()) } returns newClient
//            every { petRepository.findById(petId) } returns null andThen newPet
//            every { petRepository.save(any()) } returns newPet
//            every { salonServiceRepository.findById(serviceId) } returns service
//            every { appointmentRepository.findBySalonIdAndStaffIdAndDate(any(), any(), any()) } returns emptyList()
//            every { blockTimeRepository.findOverlappingBlocks(any(), any(), any(), any()) } returns emptyList()
//            every { appointmentSchedulingService.validateTimeSlotAvailability(any(), any(), any(), any(), any()) } just Runs
//            every { appointmentRepository.save(any()) } returns expectedAppointment
//
//            // When
//            val result = useCase.scheduleAppointment(command)
//
//            // Then
//            assertNotNull(result)
//            assertEquals(expectedAppointment.id, result.id)
//
//            // Verify new client and pet were created
//            verify(exactly = 1) { clientRepository.save(any()) }
//            verify(exactly = 1) { petRepository.save(any()) }
//        }
//
//        @Test
//        fun `should create new pet when petId is null and pet data provided`() {
//            // Given
//            val salonId = SalonId.generate()
//            val userId = UserId.generate()
//            val clientId = ClientId.generate()
//            val staffId = StaffId.generate()
//            val serviceId = ServiceId.generate()
//
//            val command = TestCommandBuilder.aScheduleAppointmentCommand()
//                .withSalonId(salonId)
//                .withClientId(clientId)
//                .withNewPet("New Pet", "Cat") // This sets petId to null
//                .withStaffId(staffId)
//                .withServiceIds(listOf(serviceId))
//                .build()
//
//            val salon = TestDataBuilder.aSalon()
//                .withId(salonId)
//                .withClientIds(setOf(clientId))
//                .build()
//
//            val client = TestDataBuilder.aClient()
//                .withId(clientId)
//                .build()
//
//            val staff = TestDataBuilder.aUser()
//                .withId(userId)
//                .withRole(UserRole.STAFF)
//                .build()
//
//            val staffMember = TestDataBuilder.aStaff()
//                .withUserId(userIdid)
//                .withSalonId(salonId)
//                .withRole(StaffRole.GROOMER)
//                .withActive(true)
//                .build()
//
//            val service = TestDataBuilder.aSalonService()
//                .withId(serviceId)
//                .withSalonId(salonId)
//                .withActive(true)
//                .build()
//
//            val newPet = TestDataBuilder.aPet()
//                .withClientId(clientId)
//                .withName("New Pet")
//                .withBreed("Cat")
//                .build()
//
//            val expectedAppointment = TestDataBuilder.anAppointment()
//                .withSalonId(salonId)
//                .withClientId(clientId)
//                .withPetId(newPet.id)
//                .withStaffId(staffId)
//                .withServiceIds(listOf(serviceId))
//                .build()
//
//            // Mock validation service
//            every { validationService.validateScheduleAppointment(command) } returns mockk {
//                every { throwIfInvalid() } just Runs
//            }
//
//            // Mock repositories
//            every { salonRepository.findById(salonId) } returns salon
//            // Staff permissions handled by createStaffWithPermissions helper
//            every { userRepository.findById(userId) } returns staff
//            every { staffRepository.findByUserIdAndSalonId(userId, salonId) } returns staffMember
//            every { clientRepository.findById(clientId) } returns client
//            every { petRepository.findById(any()) } returns null // No existing pet
//            every { petRepository.save(any()) } returns newPet
//            every { salonServiceRepository.findById(serviceId) } returns service
//            every { appointmentRepository.findBySalonIdAndStaffIdAndDate(any(), any(), any()) } returns emptyList()
//            every { blockTimeRepository.findOverlappingBlocks(any(), any(), any(), any()) } returns emptyList()
//
//            // Mock domain services
//            every { appointmentSchedulingService.validateTimeSlotAvailability(any(), any(), any(), any(), any()) } just Runs
//            every { appointmentRepository.save(any()) } returns expectedAppointment
//
//            // When
//            val result = useCase.scheduleAppointment(command)
//
//            // Then
//            assertNotNull(result)
//            assertEquals(expectedAppointment.id, result.id)
//
//            // Verify new pet was created (petId was null in command)
//            verify(exactly = 1) { petRepository.save(any()) }
//            verify(exactly = 0) { petRepository.findById(any()) } // No lookup since petId was null
//        }
//
//        @Test
//        fun `should throw BusinessRuleViolationException when petId is null but pet data missing`() {
//            // Given
//            val salonId = SalonId.generate()
//            val clientId = ClientId.generate()
//            val StaffId = StaffId.generate()
//            val serviceId = ServiceId.generate()
//
//            val command = TestCommandBuilder.aScheduleAppointmentCommand()
//                .withSalonId(salonId)
//                .withClientId(clientId)
//                .withPetId(null) // No petId
//                // No pet name or species provided
//                .withStaffId(staffId)
//                .withServiceIds(listOf(serviceId))
//                .build()
//
//            val salon = TestDataBuilder.aSalon()
//                .withId(salonId)
//                .withClientIds(setOf(clientId))
//                .build()
//
//            val client = TestDataBuilder.aClient()
//                .withId(clientId)
//                .build()
//
//            val staff = TestDataBuilder.aUser()
//                .withId(userId)
//                .withRole(UserRole.STAFF)
//                .build()
//
//            val staffMember = TestDataBuilder.aStaff()
//                .withUserId(userid)
//                .withSalonId(salonId)
//                .withRole(StaffRole.GROOMER)
//                .withActive(true)
//                .build()
//
//            // Mock validation service
//            every { validationService.validateScheduleAppointment(command) } returns mockk {
//                every { throwIfInvalid() } just Runs
//            }
//
//            // Mock repositories
//            every { salonRepository.findById(salonId) } returns salon
//            // Staff permissions handled by createStaffWithPermissions helper
//            every { userRepository.findById(userid) } returns staff
//            every { staffRepository.findByUserIdAndSalonId(staffId, salonId) } returns staffMember
//            every { clientRepository.findById(clientId) } returns client
//
//            // When & Then
//            val exception = assertThrows<BusinessRuleViolationException> {
//                useCase.scheduleAppointment(command)
//            }
//
//            assertTrue(exception.message!!.contains("Pet name and species are required for new pets"))
//        }
//    }
//
//    @Nested
//    @DisplayName("Get Appointment By ID")
//    inner class GetAppointmentByIdTests {
//
//        @Test
//        fun `should return appointment when found and no access control`() {
//            // Given
//            val appointmentId = AppointmentId.generate()
//            val query = TestCommandBuilder.aGetAppointmentByIdQuery()
//                .withAppointmentId(appointmentId)
//                .build()
//
//            val expectedAppointment = TestDataBuilder.anAppointment()
//                .withId(appointmentId)
//                .build()
//
//            every { appointmentRepository.findById(appointmentId) } returns expectedAppointment
//
//            // When
//            val result = useCase.getAppointmentById(query)
//
//            // Then
//            assertNotNull(result)
//            assertEquals(appointmentId, result.id)
//            verify(exactly = 1) { appointmentRepository.findById(appointmentId) }
//        }
//
//        @Test
//        fun `should return null when appointment not found`() {
//            // Given
//            val appointmentId = AppointmentId.generate()
//            val query = TestCommandBuilder.aGetAppointmentByIdQuery()
//                .withAppointmentId(appointmentId)
//                .build()
//
//            every { appointmentRepository.findById(appointmentId) } returns null
//
//            // When
//            val result = useCase.getAppointmentById(query)
//
//            // Then
//            assertEquals(null, result)
//            verify(exactly = 1) { appointmentRepository.findById(appointmentId) }
//        }
//
//        @Test
//        fun `should validate salon context when provided`() {
//            // Given
//            val appointmentId = AppointmentId.generate()
//            val salonId = SalonId.generate()
//            val requesterId = UserId.generate()
//            val wrongSalonId = SalonId.generate()
//
//            val query = TestCommandBuilder.aGetAppointmentByIdQuery()
//                .withAppointmentId(appointmentId)
//                .withSalonId(wrongSalonId)
//                .withRequesterId(requesterId)
//                .build()
//
//            val appointment = TestDataBuilder.anAppointment()
//                .withId(appointmentId)
//                .withSalonId(salonId) // Different salon
//                .build()
//
//            every { appointmentRepository.findById(appointmentId) } returns appointment
//
//            // When & Then
//            val exception = assertThrows<BusinessRuleViolationException> {
//                useCase.getAppointmentById(query)
//            }
//
//            assertTrue(exception.message!!.contains("does not belong to the specified salon"))
//        }
//
//        @Test
//        fun `should validate permission when requester provided`() {
//            // Given
//            val appointmentId = AppointmentId.generate()
//            val salonId = SalonId.generate()
//            val requesterId = UserId.generate()
//
//            val query = TestCommandBuilder.aGetAppointmentByIdQuery()
//                .withAppointmentId(appointmentId)
//                .withSalonId(salonId)
//                .withRequesterId(requesterId)
//                .build()
//
//            val appointment = TestDataBuilder.anAppointment()
//                .withId(appointmentId)
//                .withSalonId(salonId)
//                .build()
//
//            every { appointmentRepository.findById(appointmentId) } returns appointment
//            // Staff permissions handled by staffRepository mock
//
//            // When & Then
//            val exception = assertThrows<UnauthorizedException> {
//                useCase.getAppointmentById(query)
//            }
//
//            assertTrue(exception.message!!.contains("does not have permission to view"))
//            // Verification handled by staffRepository mock
//        }
//    }
//
//    @Nested
//    @DisplayName("Get Salon Appointments")
//    inner class GetSalonAppointmentsTests {
//
//        @Test
//        fun `should return salon appointments when user has access`() {
//            // Given
//            val salonId = SalonId.generate()
//            val requesterId = UserId.generate()
//
//            val query = TestCommandBuilder.aGetSalonAppointmentsQuery()
//                .withSalonId(salonId)
//                .withRequesterId(requesterId)
//                .build()
//
//            val appointments = listOf(
//                TestDataBuilder.anAppointment().withSalonId(salonId).build(),
//                TestDataBuilder.anAppointment().withSalonId(salonId).build()
//            )
//
//            // Staff access handled by staffRepository mock
//            every { appointmentRepository.findBySalonIdWithFilters(
//                salonId = salonId,
//                date = query.date,
//                startDate = query.startDate,
//                endDate = query.endDate,
//                status = query.status,
//                clientId = query.clientId,
//                staffId = query.staffId
//            ) } returns appointments
//
//            // When
//            val result = useCase.getSalonAppointments(query)
//
//            // Then
//            assertEquals(2, result.size)
//            assertTrue(result.all { it.salonId == salonId })
//            // Verification handled by staffRepository mock
//            verify(exactly = 1) { appointmentRepository.findBySalonIdWithFilters(any(), any(), any(), any(), any(), any(), any()) }
//        }
//
//        @Test
//        fun `should throw UnauthorizedException when user has no salon access`() {
//            // Given
//            val salonId = SalonId.generate()
//            val requesterId = UserId.generate()
//
//            val query = TestCommandBuilder.aGetSalonAppointmentsQuery()
//                .withSalonId(salonId)
//                .withRequesterId(requesterId)
//                .build()
//
//            // Staff access handled by staffRepository mock
//
//            // When & Then
//            val exception = assertThrows<UnauthorizedException> {
//                useCase.getSalonAppointments(query)
//            }
//
//            assertTrue(exception.message!!.contains("does not have access to this salon"))
//            // Verification handled by staffRepository mock
//            verify(exactly = 0) { appointmentRepository.findBySalonIdWithFilters(any(), any(), any(), any(), any(), any(), any()) }
//        }
//    }
//
//    @Nested
//    @DisplayName("Cancel Appointment")
//    inner class CancelAppointmentTests {
//
//        @Test
//        fun `should cancel appointment successfully when all validations pass`() {
//            // Given
//            val appointmentId = AppointmentId.generate()
//            val salonId = SalonId.generate()
//            val cancellerUserId = UserId.generate()
//
//            val command = TestCommandBuilder.aCancelAppointmentCommand()
//                .withAppointmentId(appointmentId)
//                .withSalonId(salonId)
//                .withCancellerUserId(cancellerUserId)
//                .withReason("Test cancellation")
//                .build()
//
//            val appointment = TestDataBuilder.anAppointment()
//                .withId(appointmentId)
//                .withSalonId(salonId)
//                .withStatus(AppointmentStatus.SCHEDULED)
//                .build()
//
//            val cancelledAppointment = appointment.cancel(command.reason)
//
//            every { appointmentRepository.findById(appointmentId) } returns appointment
//            // Staff permissions handled by staffRepository mock
//            every { appointmentRepository.save(any()) } returns cancelledAppointment
//
//            // When
//            val result = useCase.cancelAppointment(command)
//
//            // Then
//            assertNotNull(result)
//            assertEquals(appointmentId, result.id)
//            assertEquals(AppointmentStatus.CANCELLED, result.status)
//            verify(exactly = 1) { appointmentRepository.findById(appointmentId) }
//            // Verification handled by staffRepository mock
//            verify(exactly = 1) { appointmentRepository.save(any()) }
//        }
//
//        @Test
//        fun `should throw EntityNotFoundException when appointment not found`() {
//            // Given
//            val command = TestCommandBuilder.aCancelAppointmentCommand().build()
//
//            every { appointmentRepository.findById(command.appointmentId) } returns null
//
//            // When & Then
//            val exception = assertThrows<EntityNotFoundException> {
//                useCase.cancelAppointment(command)
//            }
//
//            assertTrue(exception.message!!.contains("Appointment not found"))
//            verify(exactly = 1) { appointmentRepository.findById(command.appointmentId) }
//        }
//
//        @Test
//        fun `should throw BusinessRuleViolationException when appointment belongs to different salon`() {
//            // Given
//            val appointmentId = AppointmentId.generate()
//            val salonId = SalonId.generate()
//            val wrongSalonId = SalonId.generate()
//
//            val command = TestCommandBuilder.aCancelAppointmentCommand()
//                .withAppointmentId(appointmentId)
//                .withSalonId(wrongSalonId)
//                .build()
//
//            val appointment = TestDataBuilder.anAppointment()
//                .withId(appointmentId)
//                .withSalonId(salonId) // Different salon
//                .build()
//
//            every { appointmentRepository.findById(appointmentId) } returns appointment
//
//            // When & Then
//            val exception = assertThrows<BusinessRuleViolationException> {
//                useCase.cancelAppointment(command)
//            }
//
//            assertTrue(exception.message!!.contains("does not belong to the specified salon"))
//        }
//
//        @Test
//        fun `should throw UnauthorizedException when user cannot modify appointment`() {
//            // Given
//            val appointmentId = AppointmentId.generate()
//            val salonId = SalonId.generate()
//            val cancellerUserId = UserId.generate()
//
//            val command = TestCommandBuilder.aCancelAppointmentCommand()
//                .withAppointmentId(appointmentId)
//                .withSalonId(salonId)
//                .withCancellerUserId(cancellerUserId)
//                .build()
//
//            val appointment = TestDataBuilder.anAppointment()
//                .withId(appointmentId)
//                .withSalonId(salonId)
//                .build()
//
//            every { appointmentRepository.findById(appointmentId) } returns appointment
//            // Staff permissions handled by staffRepository mock
//
//            // When & Then
//            val exception = assertThrows<UnauthorizedException> {
//                useCase.cancelAppointment(command)
//            }
//
//            assertTrue(exception.message!!.contains("does not have permission to cancel"))
//            // Verification handled by staffRepository mock
//        }
//    }
//
//    @Nested
//    @DisplayName("Complete Appointment")
//    inner class CompleteAppointmentTests {
//
//        @Test
//        fun `should complete appointment successfully when all validations pass`() {
//            // Given
//            val appointmentId = AppointmentId.generate()
//            val salonId = SalonId.generate()
//            val completerUserId = UserId.generate()
//
//            val command = TestCommandBuilder.aCompleteAppointmentCommand()
//                .withAppointmentId(appointmentId)
//                .withSalonId(salonId)
//                .withCompleterUserId(completerUserId)
//                .withNotes("Service completed successfully")
//                .build()
//
//            val appointment = TestDataBuilder.anAppointment()
//                .withId(appointmentId)
//                .withSalonId(salonId)
//                .withStatus(AppointmentStatus.IN_PROGRESS)
//                .build()
//
//            val completedAppointment = appointment.complete(command.notes)
//
//            every { appointmentRepository.findById(appointmentId) } returns appointment
//            // Staff permissions handled by staffRepository mock
//            every { appointmentRepository.save(any()) } returns completedAppointment
//
//            // When
//            val result = useCase.completeAppointment(command)
//
//            // Then
//            assertNotNull(result)
//            assertEquals(appointmentId, result.id)
//            assertEquals(AppointmentStatus.COMPLETED, result.status)
//            verify(exactly = 1) { appointmentRepository.findById(appointmentId) }
//            // Verification handled by staffRepository mock
//            verify(exactly = 1) { appointmentRepository.save(any()) }
//        }
//
//        @Test
//        fun `should throw EntityNotFoundException when appointment not found for completion`() {
//            // Given
//            val command = TestCommandBuilder.aCompleteAppointmentCommand().build()
//
//            every { appointmentRepository.findById(command.appointmentId) } returns null
//
//            // When & Then
//            val exception = assertThrows<EntityNotFoundException> {
//                useCase.completeAppointment(command)
//            }
//
//            assertTrue(exception.message!!.contains("Appointment not found"))
//            verify(exactly = 1) { appointmentRepository.findById(command.appointmentId) }
//        }
//
//        @Test
//        fun `should throw UnauthorizedException when user cannot complete appointment`() {
//            // Given
//            val appointmentId = AppointmentId.generate()
//            val salonId = SalonId.generate()
//            val completerUserId = UserId.generate()
//
//            val command = TestCommandBuilder.aCompleteAppointmentCommand()
//                .withAppointmentId(appointmentId)
//                .withSalonId(salonId)
//                .withCompleterUserId(completerUserId)
//                .build()
//
//            val appointment = TestDataBuilder.anAppointment()
//                .withId(appointmentId)
//                .withSalonId(salonId)
//                .build()
//
//            every { appointmentRepository.findById(appointmentId) } returns appointment
//            // Staff permissions handled by staffRepository mock
//
//            // When & Then
//            val exception = assertThrows<UnauthorizedException> {
//                useCase.completeAppointment(command)
//            }
//
//            assertTrue(exception.message!!.contains("does not have permission to complete"))
//            // Verification handled by staffRepository mock
//        }
//    }
//
//    @Nested
//    @DisplayName("Legacy Support Methods")
//    inner class LegacySupportMethodsTests {
//
//        @Test
//        fun `should check groomer availability correctly`() {
//            // Given
//            val staffId = StaffId.generate()
//            val userIrd = UserId.generate()
//            val date = LocalDate.now().plusDays(1)
//            val timeSlot = TimeSlot(
//                startTime = LocalTime.of(10, 0),
//                endTime = LocalTime.of(11, 0)
//            )
//
//            val query = CheckGroomerAvailabilityQuery(
//                salonId = SalonId.generate(),
//                staffId = staffId,
//                date = date,
//                timeSlot = timeSlot
//            )
//
//            val existingAppointments = listOf(
//                TestDataBuilder.anAppointment()
//                    .withStaffId(staffId)
//                    .withDate(date)
//                    .withTimeSlot(LocalTime.of(14, 0), LocalTime.of(15, 0)) // No conflict
//                    .withStatus(AppointmentStatus.SCHEDULED)
//                    .build()
//            )
//
//            every { appointmentRepository.findByStaffIdAndDate(staffId, date) } returns existingAppointments
//
//            // When
//            val result = useCase.checkGroomerAvailability(query)
//
//            // Then
//            assertTrue(result.isAvailable)
//            assertEquals(null, result.reason)
//            assertTrue(result.conflictingAppointments.isEmpty())
//            verify(exactly = 1) { appointmentRepository.findByStaffIdAndDate(staffId, date) }
//        }
//
//        @Test
//        fun `should detect groomer availability conflict`() {
//            // Given
//            val staffId = StaffId.generate()
//            val date = LocalDate.now().plusDays(1)
//            val timeSlot = TimeSlot(
//                startTime = LocalTime.of(10, 0),
//                endTime = LocalTime.of(11, 0)
//            )
//
//            val query = CheckGroomerAvailabilityQuery(
//                salonId = SalonId.generate(),
//                staffId = staffId,
//                date = date,
//                timeSlot = timeSlot
//            )
//
//            val conflictingAppointment = TestDataBuilder.anAppointment()
//                .withStaffId(staffId)
//                .withDate(date)
//                .withTimeSlot(LocalTime.of(10, 30), LocalTime.of(11, 30)) // Overlaps
//                .withStatus(AppointmentStatus.SCHEDULED)
//                .build()
//
//            val existingAppointments = listOf(conflictingAppointment)
//
//            every { appointmentRepository.findByStaffIdAndDate(staffId, date) } returns existingAppointments
//
//            // When
//            val result = useCase.checkGroomerAvailability(query)
//
//            // Then
//            assertEquals(false, result.isAvailable)
//            assertEquals("Time slot conflicts with existing appointment", result.reason)
//            assertEquals(1, result.conflictingAppointments.size)
//            assertEquals(conflictingAppointment.id, result.conflictingAppointments.first())
//            verify(exactly = 1) { appointmentRepository.findByStaffIdAndDate(staffId, date) }
//        }
//
//        @Test
//        fun `should get appointments by date range`() {
//            // Given
//            val startDate = LocalDate.now()
//            val endDate = LocalDate.now().plusDays(7)
//            val staffId = StaffId.generate()
//            val status = AppointmentStatus.SCHEDULED
//
//            val query = GetAppointmentsByDateRangeQuery(
//                salonId = SalonId.generate(),
//                dateRange = DateRange(startDate, endDate),
//                staffId = staffId,
//                status = status
//            )
//
//            val appointments = listOf(
//                TestDataBuilder.anAppointment().withStaffId(staffId).withStatus(status).build(),
//                TestDataBuilder.anAppointment().withStaffId(staffId).withStatus(status).build()
//            )
//
//            every { appointmentRepository.findByDateRange(startDate, endDate, staffId, status) } returns appointments
//
//            // When
//            val result = useCase.getAppointmentsByDateRange(query)
//
//            // Then
//            assertEquals(2, result.size)
//            assertTrue(result.all { it.staffId == staffId && it.status == status })
//            verify(exactly = 1) { appointmentRepository.findByDateRange(startDate, endDate, staffId, status) }
//        }
//
//        @Test
//        fun `should get appointments by groomer`() {
//            // Given
//            val staffId = StaffId.generate()
//            val startDate = LocalDate.now()
//            val endDate = LocalDate.now().plusDays(7)
//            val status = AppointmentStatus.SCHEDULED
//
//            val query = GetAppointmentsByGroomerQuery(
//                salonId = SalonId.generate(),
//                staffId = staffId,
//                dateRange = DateRange(startDate, endDate),
//                status = status
//            )
//
//            val appointments = listOf(
//                TestDataBuilder.anAppointment().withStaffId(staffId).withStatus(status).build()
//            )
//
//            every { appointmentRepository.findByStaffId(staffId, startDate, endDate, status) } returns appointments
//
//            // When
//            val result = useCase.getAppointmentsByGroomer(query)
//
//            // Then
//            assertEquals(1, result.size)
//            assertEquals(staffId, result.first().staffId)
//            assertEquals(status, result.first().status)
//            verify(exactly = 1) { appointmentRepository.findByStaffId(staffId, startDate, endDate, status) }
//        }
//    }
//
//    @Nested
//    @DisplayName("Not Implemented Methods")
//    inner class NotImplementedMethodsTests {
//
//        @Test
//        fun `should reschedule appointment successfully when all validations pass`() {
//            // Given
//            val appointmentId = AppointmentId.generate()
//            val salonId = SalonId.generate()
//            val reschedulerUserId = UserId.generate()
//            val clientId = ClientId.generate()
//            val petId = PetId.generate()
//            val staffId = StaffId.generate()
//
//            val existingAppointment = TestDataBuilder.anAppointment()
//                .withId(appointmentId)
//                .withSalonId(salonId)
//                .withClientId(clientId)
//                .withPetId(petId)
//                .withStaffId(staffId)
//                .withStatus(AppointmentStatus.SCHEDULED)
//                .withDate(LocalDate.now().plusDays(1))
//                .withStartTime(LocalTime.of(10, 0))
//                .withEndTime(LocalTime.of(11, 0))
//                .build()
//
//            val command = RescheduleAppointmentCommand(
//                appointmentId = appointmentId,
//                salonId = salonId,
//                reschedulerUserId = reschedulerUserId,
//                newDate = LocalDate.now().plusDays(2),
//                newStartTime = LocalTime.of(14, 0),
//                newEndTime = LocalTime.of(15, 0),
//                reason = "Client requested time change"
//            )
//
//            val rescheduledAppointment = existingAppointment.reschedule(
//                newDate = command.newDate!!,
//                newStartTime = command.newStartTime,
//                newEndTime = command.newEndTime
//            ).updateNotes("Rescheduled: ${command.reason}")
//
//            // Mock repositories
//            every { appointmentRepository.findById(appointmentId) } returns existingAppointment
//            // Staff permissions handled by staffRepository mock
//            every { appointmentRepository.findBySalonIdAndStaffIdAndDate(any(), any(), any()) } returns emptyList()
//            every { appointmentSchedulingService.validateTimeSlotAvailability(any(), any(), any(), any(), any(), any()) } just Runs
//            every { appointmentRepository.save(any()) } returns rescheduledAppointment
//
//            // When
//            val result = useCase.rescheduleAppointment(command)
//
//            // Then
//            assertNotNull(result)
//            assertEquals(AppointmentStatus.RESCHEDULED, result.status)
//            assertEquals(command.newDate, result.appointmentDate)
//            assertEquals(command.newStartTime, result.startTime)
//            assertEquals(command.newEndTime, result.endTime)
//            assertTrue(result.notes!!.contains("Rescheduled: ${command.reason}"))
//
//            verify(exactly = 1) { appointmentRepository.findById(appointmentId) }
//            // Verification handled by staffRepository mock
//            verify(exactly = 1) { appointmentRepository.save(any()) }
//        }
//
//        @Test
//        fun `should throw EntityNotFoundException when appointment not found for reschedule`() {
//            // Given
//            val appointmentId = AppointmentId.generate()
//            val command = RescheduleAppointmentCommand(
//                appointmentId = appointmentId,
//                salonId = SalonId.generate(),
//                reschedulerUserId = UserId.generate(),
//                newStartTime = LocalTime.of(14, 0),
//                newEndTime = LocalTime.of(15, 0)
//            )
//
//            every { appointmentRepository.findById(appointmentId) } returns null
//
//            // When & Then
//            val exception = assertThrows<EntityNotFoundException> {
//                useCase.rescheduleAppointment(command)
//            }
//
//            assertTrue(exception.message!!.contains("Appointment not found"))
//        }
//
//        @Test
//        fun `should throw UnauthorizedException when user cannot modify appointment for reschedule`() {
//            // Given
//            val appointmentId = AppointmentId.generate()
//            val salonId = SalonId.generate()
//            val reschedulerUserId = UserId.generate()
//
//            val existingAppointment = TestDataBuilder.anAppointment()
//                .withId(appointmentId)
//                .withSalonId(salonId)
//                .withStatus(AppointmentStatus.SCHEDULED)
//                .build()
//
//            val command = RescheduleAppointmentCommand(
//                appointmentId = appointmentId,
//                salonId = salonId,
//                reschedulerUserId = reschedulerUserId,
//                newStartTime = LocalTime.of(14, 0),
//                newEndTime = LocalTime.of(15, 0)
//            )
//
//            every { appointmentRepository.findById(appointmentId) } returns existingAppointment
//            // Staff permissions handled by staffRepository mock
//
//            // When & Then
//            val exception = assertThrows<UnauthorizedException> {
//                useCase.rescheduleAppointment(command)
//            }
//
//            assertTrue(exception.message!!.contains("does not have permission to reschedule"))
//        }
//
//        @Test
//        fun `should throw BusinessRuleViolationException when appointment cannot be rescheduled`() {
//            // Given
//            val appointmentId = AppointmentId.generate()
//            val salonId = SalonId.generate()
//            val reschedulerUserId = UserId.generate()
//
//            val existingAppointment = TestDataBuilder.anAppointment()
//                .withId(appointmentId)
//                .withSalonId(salonId)
//                .withStatus(AppointmentStatus.COMPLETED) // Cannot reschedule completed appointment
//                .build()
//
//            val command = RescheduleAppointmentCommand(
//                appointmentId = appointmentId,
//                salonId = salonId,
//                reschedulerUserId = reschedulerUserId,
//                newStartTime = LocalTime.of(14, 0),
//                newEndTime = LocalTime.of(15, 0)
//            )
//
//            every { appointmentRepository.findById(appointmentId) } returns existingAppointment
//            // Staff permissions handled by staffRepository mock
//
//            // When & Then
//            val exception = assertThrows<BusinessRuleViolationException> {
//                useCase.rescheduleAppointment(command)
//            }
//
//            assertTrue(exception.message!!.contains("cannot be rescheduled"))
//        }
//
//        @Test
//        fun `should throw NotImplementedError for mark no show`() {
//            // Given
//            val command = MarkNoShowCommand(
//                appointmentId = AppointmentId.generate(),
//                salonId = SalonId.generate(),
//                markerUserId = UserId.generate(),
//                noShowNotes = "Client did not show up"
//            )
//
//            // When & Then
//            assertThrows<NotImplementedError> {
//                useCase.markNoShow(command)
//            }
//        }
//
//        @Test
//        fun `should throw NotImplementedError for mark in progress`() {
//            // Given
//            val command = MarkInProgressCommand(
//                appointmentId = AppointmentId.generate(),
//                salonId = SalonId.generate(),
//                markerUserId = UserId.generate()
//            )
//
//            // When & Then
//            assertThrows<NotImplementedError> {
//                useCase.markInProgress(command)
//            }
//        }
//    }
//}
