package ro.animaliaprogramari.animalia.application.usecase

import io.mockk.*
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.Nested
import ro.animaliaprogramari.animalia.application.command.UpdateStaffWorkingHoursCommand
import ro.animaliaprogramari.animalia.application.port.outbound.AppointmentRepository
import ro.animaliaprogramari.animalia.application.port.outbound.StaffRepository
import ro.animaliaprogramari.animalia.application.port.outbound.StaffWorkingHoursRepository
import ro.animaliaprogramari.animalia.application.query.GetStaffWorkingHoursQuery
import ro.animaliaprogramari.animalia.application.query.GetStaffAvailabilityQuery
import ro.animaliaprogramari.animalia.domain.exception.BusinessRuleViolationException
import ro.animaliaprogramari.animalia.domain.exception.EntityNotFoundException
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.test.TestDataBuilder
import java.math.BigDecimal
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.LocalTime

@Disabled
class StaffWorkingHoursManagementUseCaseImplTest {

    private val staffWorkingHoursRepository = mockk<StaffWorkingHoursRepository>()
    private val staffRepository = mockk<StaffRepository>()
    private val appointmentRepository = mockk<AppointmentRepository>()

    private val useCase = StaffWorkingHoursManagementUseCaseImpl(
        staffWorkingHoursRepository,
        staffRepository,
        appointmentRepository
    )

    private val staffId = StaffId.of("staff-123")
    private val salonId = SalonId.of("salon-456")
    private val userId = UserId.of("user-789")
    private val requesterId = UserId.of("requester-101")

    private val testStaff = Staff.createGroomer(userId, salonId, StaffPermissions.defaultGroomerAccess())
    private val chiefGroomerStaff = Staff.createChiefGroomer(requesterId, salonId)

    @BeforeEach
    fun setUp() {
        clearAllMocks()
    }

    @Nested
    inner class GetStaffWorkingHours {

        @Test
        fun `should get existing working hours successfully`() {
            // Given
            val query = GetStaffWorkingHoursQuery(staffId, salonId, requesterId)
            val existingWorkingHours = StaffWorkingHoursSettings.createDefault(staffId, salonId)

            every { staffRepository.findByUserIdAndSalonId(UserId.of(staffId.value), salonId) } returns testStaff
            every { staffRepository.findByUserIdAndSalonId(requesterId, salonId) } returns chiefGroomerStaff
            every { staffWorkingHoursRepository.findByStaffIdAndSalonId(staffId, salonId) } returns existingWorkingHours

            // When
            val result = useCase.getStaffWorkingHours(query)

            // Then
            assertEquals(existingWorkingHours, result)
            verify { staffRepository.findByUserIdAndSalonId(UserId.of(staffId.value), salonId) }
            verify { staffWorkingHoursRepository.findByStaffIdAndSalonId(staffId, salonId) }
        }

        @Test
        fun `should create default working hours when none exist`() {
            // Given
            val query = GetStaffWorkingHoursQuery(staffId, salonId, requesterId)

            every { staffRepository.findByUserIdAndSalonId(UserId.of(staffId.value), salonId) } returns testStaff
            every { staffRepository.findByUserIdAndSalonId(requesterId, salonId) } returns chiefGroomerStaff
            every { staffWorkingHoursRepository.findByStaffIdAndSalonId(staffId, salonId) } returns StaffWorkingHoursSettings.createDefault(staffId, salonId)

            // When
            val result = useCase.getStaffWorkingHours(query)

            // Then
            assertEquals(staffId, result.staffId)
            assertEquals(salonId, result.salonId)
            assertEquals(7, result.weeklySchedule.size)
            assertTrue(result.holidays.isNotEmpty())
        }

        @Test
        fun `should throw exception when staff not found`() {
            // Given
            val query = GetStaffWorkingHoursQuery(staffId, salonId, requesterId)

            every { staffRepository.findByUserIdAndSalonId(UserId.of(staffId.value), salonId) } returns null

            // When & Then
            assertThrows<EntityNotFoundException> {
                useCase.getStaffWorkingHours(query)
            }
        }

        @Test
        fun `should allow staff to view their own working hours`() {
            // Given
            val selfQuery = GetStaffWorkingHoursQuery(staffId, salonId, UserId.of(staffId.value))
            val existingWorkingHours = StaffWorkingHoursSettings.createDefault(staffId, salonId)

            every { staffRepository.findByUserIdAndSalonId(UserId.of(staffId.value), salonId) } returns testStaff
            every { staffWorkingHoursRepository.findByStaffIdAndSalonId(staffId, salonId) } returns existingWorkingHours

            // When
            val result = useCase.getStaffWorkingHours(selfQuery)

            // Then
            assertEquals(existingWorkingHours, result)
        }

        @Test
        fun `should throw exception when requester has no permission`() {
            // Given
            val unauthorizedRequesterId = UserId.of("unauthorized-user")
            val query = GetStaffWorkingHoursQuery(staffId, salonId, unauthorizedRequesterId)

            every { staffRepository.findByUserIdAndSalonId(UserId.of(staffId.value), salonId) } returns testStaff
            every { staffRepository.findByUserIdAndSalonId(unauthorizedRequesterId, salonId) } returns null

            // When & Then
            assertThrows<BusinessRuleViolationException> {
                useCase.getStaffWorkingHours(query)
            }
        }
    }

    @Nested
    inner class UpdateStaffWorkingHours {

        @Test
        fun `should update working hours successfully by chief groomer`() {
            // Given
            val newSchedule = mapOf(
                DayOfWeek.MONDAY to DaySchedule.workingDay(LocalTime.of(8, 0), LocalTime.of(16, 0)),
                DayOfWeek.TUESDAY to DaySchedule.workingDay(LocalTime.of(8, 0), LocalTime.of(16, 0)),
                DayOfWeek.WEDNESDAY to DaySchedule.workingDay(LocalTime.of(8, 0), LocalTime.of(16, 0)),
                DayOfWeek.THURSDAY to DaySchedule.workingDay(LocalTime.of(8, 0), LocalTime.of(16, 0)),
                DayOfWeek.FRIDAY to DaySchedule.workingDay(LocalTime.of(8, 0), LocalTime.of(16, 0)),
                DayOfWeek.SATURDAY to DaySchedule.dayOff(),
                DayOfWeek.SUNDAY to DaySchedule.dayOff()
            )

            val command = UpdateStaffWorkingHoursCommand(
                staffId = staffId,
                salonId = salonId,
                updaterUserId = requesterId,
                weeklySchedule = newSchedule,
                holidays = emptyList(),
                customClosures = emptyList()
            )

            val existingWorkingHours = StaffWorkingHoursSettings.createDefault(staffId, salonId)
            val updatedWorkingHours = existingWorkingHours.updateWeeklySchedule(newSchedule)

            every { staffRepository.findByUserIdAndSalonId(UserId.of(staffId.value), salonId) } returns testStaff
            every { staffRepository.findByUserIdAndSalonId(requesterId, salonId) } returns chiefGroomerStaff
            every { staffWorkingHoursRepository.findByStaffIdAndSalonId(staffId, salonId) } returns existingWorkingHours
            every { staffWorkingHoursRepository.save(any()) } returns updatedWorkingHours

            // When
            val result = useCase.updateStaffWorkingHours(command)

            // Then
            assertEquals(LocalTime.of(8, 0), result.weeklySchedule[DayOfWeek.MONDAY]?.startTime)
            verify { staffWorkingHoursRepository.save(any()) }
        }

        @Test
        fun `should allow staff to update their own working hours`() {
            // Given
            val selfUpdateCommand = UpdateStaffWorkingHoursCommand(
                staffId = staffId,
                salonId = salonId,
                updaterUserId = UserId.of(staffId.value),
                weeklySchedule = mapOf(
                    DayOfWeek.MONDAY to DaySchedule.workingDay(LocalTime.of(9, 0), LocalTime.of(17, 0)),
                    DayOfWeek.TUESDAY to DaySchedule.workingDay(LocalTime.of(9, 0), LocalTime.of(17, 0)),
                    DayOfWeek.WEDNESDAY to DaySchedule.workingDay(LocalTime.of(9, 0), LocalTime.of(17, 0)),
                    DayOfWeek.THURSDAY to DaySchedule.workingDay(LocalTime.of(9, 0), LocalTime.of(17, 0)),
                    DayOfWeek.FRIDAY to DaySchedule.workingDay(LocalTime.of(9, 0), LocalTime.of(17, 0)),
                    DayOfWeek.SATURDAY to DaySchedule.dayOff(),
                    DayOfWeek.SUNDAY to DaySchedule.dayOff()
                ),
                holidays = emptyList(),
                customClosures = emptyList()
            )

            val existingWorkingHours = StaffWorkingHoursSettings.createDefault(staffId, salonId)

            every { staffRepository.findByUserIdAndSalonId(UserId.of(staffId.value), salonId) } returns testStaff
            every { staffWorkingHoursRepository.findByStaffIdAndSalonId(staffId, salonId) } returns existingWorkingHours
            every { staffWorkingHoursRepository.save(any()) } returns existingWorkingHours

            // When
            val result = useCase.updateStaffWorkingHours(selfUpdateCommand)

            // Then
            assertNotNull(result)
            verify { staffWorkingHoursRepository.save(any()) }
        }

        @Test
        fun `should throw exception when staff not found for update`() {
            // Given
            val command = UpdateStaffWorkingHoursCommand(
                staffId = staffId,
                salonId = salonId,
                updaterUserId = requesterId,
                weeklySchedule = emptyMap(),
                holidays = emptyList(),
                customClosures = emptyList()
            )

            every { staffRepository.findByUserIdAndSalonId(UserId.of(staffId.value), salonId) } returns null

            // When & Then
            assertThrows<EntityNotFoundException> {
                useCase.updateStaffWorkingHours(command)
            }
        }

        @Test
        fun `should throw exception when updater has no permission`() {
            // Given
            val unauthorizedRequesterId = UserId.of("unauthorized-user")
            val unauthorizedStaff = Staff.createGroomer(unauthorizedRequesterId, salonId, StaffPermissions.defaultGroomerAccess())
            
            val command = UpdateStaffWorkingHoursCommand(
                staffId = staffId,
                salonId = salonId,
                updaterUserId = unauthorizedRequesterId,
                weeklySchedule = emptyMap(),
                holidays = emptyList(),
                customClosures = emptyList()
            )

            every { staffRepository.findByUserIdAndSalonId(UserId.of(staffId.value), salonId) } returns testStaff
            every { staffRepository.findByUserIdAndSalonId(unauthorizedRequesterId, salonId) } returns unauthorizedStaff

            // When & Then
            assertThrows<BusinessRuleViolationException> {
                useCase.updateStaffWorkingHours(command)
            }
        }
    }

    @Nested
    inner class ResetStaffWorkingHours {

        @Test
        fun `should reset working hours successfully by chief groomer`() {
            // Given
            val defaultWorkingHours = StaffWorkingHoursSettings.createDefault(staffId, salonId)

            every { staffRepository.findByUserIdAndSalonId(UserId.of(staffId.value), salonId) } returns testStaff
            every { staffRepository.findByUserIdAndSalonId(requesterId, salonId) } returns chiefGroomerStaff
            every { staffWorkingHoursRepository.save(any()) } returns defaultWorkingHours

            // When
            val result = useCase.resetStaffWorkingHours(staffId.value, salonId.value, requesterId.value)

            // Then
            assertEquals(staffId, result.staffId)
            assertEquals(salonId, result.salonId)
            verify { staffWorkingHoursRepository.save(any()) }
        }

        @Test
        fun `should throw exception when non-chief groomer tries to reset`() {
            // Given
            val regularGroomer = Staff.createGroomer(requesterId, salonId, StaffPermissions.defaultGroomerAccess())

            every { staffRepository.findByUserIdAndSalonId(UserId.of(staffId.value), salonId) } returns testStaff
            every { staffRepository.findByUserIdAndSalonId(requesterId, salonId) } returns regularGroomer

            // When & Then
            assertThrows<BusinessRuleViolationException> {
                useCase.resetStaffWorkingHours(staffId.value, salonId.value, requesterId.value)
            }
        }
    }

    @Nested
    inner class GetStaffAvailability {

        @Test
        fun `should get staff availability successfully`() {
            // Given
            val date = LocalDate.of(2024, 1, 8) // Monday
            val query = GetStaffAvailabilityQuery(salonId, date, null, null, requesterId)
            val workingHours = StaffWorkingHoursSettings.createDefault(staffId, salonId)

            every { staffRepository.findActiveBySalonWithUserDetails(salonId) } returns listOf(testStaff)
            every { staffWorkingHoursRepository.findByStaffIdAndSalonId(StaffId.of(testStaff.userId.value), salonId) } returns workingHours
            every { appointmentRepository.findBySalonIdAndStaffIdAndDate(salonId, testStaff.id, date) } returns emptyList()

            // When
            val result = useCase.getStaffAvailability(query)

            // Then
            assertEquals(date, result.date)
            assertEquals(1, result.staffAvailability.size)
            assertTrue(result.staffAvailability[0].isAvailable)
            assertNull(result.staffAvailability[0].reason)
        }

        @Test
        fun `should show staff as unavailable on day off`() {
            // Given
            val date = LocalDate.of(2024, 1, 7) // Sunday (day off)
            val query = GetStaffAvailabilityQuery(salonId, date, null, null, requesterId)
            val workingHours = StaffWorkingHoursSettings.createDefault(staffId, salonId)

            every { staffRepository.findActiveBySalonWithUserDetails(salonId) } returns listOf(testStaff)
            every { staffWorkingHoursRepository.findByStaffIdAndSalonId(StaffId.of(testStaff.userId.value), salonId) } returns workingHours

            // When
            val result = useCase.getStaffAvailability(query)

            // Then
            assertEquals(1, result.staffAvailability.size)
            assertFalse(result.staffAvailability[0].isAvailable)
            assertEquals("Day off", result.staffAvailability[0].reason)
        }

        @Test
        fun `should show staff as unavailable due to conflicting appointment`() {
            // Given
            val date = LocalDate.of(2024, 1, 8) // Monday
            val startTime = LocalTime.of(10, 0)
            val endTime = LocalTime.of(11, 0)
            val query = GetStaffAvailabilityQuery(salonId, date, startTime, endTime, requesterId)
            val workingHours = StaffWorkingHoursSettings.createDefault(staffId, salonId)

            val testService = TestDataBuilder.aSalonService()
                .withSalonId(salonId)
                .withName("Basic Grooming")
                .withPrice(Money.of(BigDecimal("50.00")))
                .withDuration(Duration.ofMinutes(60))
                .build()

            val conflictingAppointment = Appointment.create(
                salonId = salonId,
                clientId = ClientId.of("client-123"),
                petId = PetId.of("pet-456"),
                staffId = testStaff.id,
                appointmentDate = date,
                startTime = LocalTime.of(10, 30),
                endTime = LocalTime.of(11, 30),
                salonServices = listOf(testService)
            )

            every { staffRepository.findActiveBySalonWithUserDetails(salonId) } returns listOf(testStaff)
            every { staffWorkingHoursRepository.findByStaffIdAndSalonId(StaffId.of(testStaff.userId.value), salonId) } returns workingHours
            every { appointmentRepository.findBySalonIdAndStaffIdAndDate(salonId, testStaff.id, date) } returns listOf(conflictingAppointment)

            // When
            val result = useCase.getStaffAvailability(query)

            // Then
            assertEquals(1, result.staffAvailability.size)
            assertFalse(result.staffAvailability[0].isAvailable)
            assertEquals("Conflicting appointment", result.staffAvailability[0].reason)
            assertEquals(1, result.staffAvailability[0].conflictingAppointments.size)
        }
    }
}
