package ro.animaliaprogramari.animalia.integration

import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.testutil.TestDataBuilder
import java.time.ZonedDateTime
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

/**
 * Integration tests for the Block Time feature
 * Tests the complete domain model behavior and business logic
 */
@DisplayName("Block Time Integration Tests")
class BlockTimeIntegrationTest {

    @BeforeEach
    fun setUp() {
        // Setup test data if needed
    }

    @Test
    fun `should create and persist block time successfully`() {
        // Given
        val salonId = SalonId.of("test-salon-1")
        val staffIds = setOf(StaffId.of("staff-1"), StaffId.of("staff-2"))
        val startTime = ZonedDateTime.now().plusHours(2)
        val endTime = startTime.plusHours(1)
        
        val blockTime = TestDataBuilder.aBlockTime(
            salonId = salonId,
            startTime = startTime,
            endTime = endTime,
            reason = BlockReason.PAUZA,
            staffIds = staffIds,
            notes = "Integration test block"
        )

        // When - Domain model validation should pass
        val duration = blockTime.getDurationMinutes()
        val displayReason = blockTime.getDisplayReason()

        // Then
        assertEquals(60, duration)
        assertEquals("Pauză", displayReason)
        assertTrue(blockTime.isActive())
        assertTrue(blockTime.affectsStaff(StaffId.of("staff-1")))
        assertTrue(blockTime.affectsStaff(StaffId.of("staff-2")))
    }

    @Test
    fun `should handle recurring block time patterns`() {
        // Given
        val recurrencePattern = TestDataBuilder.aRecurrencePattern(
            type = RecurrenceType.WEEKLY,
            interval = 1,
            daysOfWeek = setOf(java.time.DayOfWeek.MONDAY, java.time.DayOfWeek.WEDNESDAY),
            occurrences = 5
        )

        val blockTime = TestDataBuilder.aBlockTime(
            isRecurring = true,
            recurrencePattern = recurrencePattern,
            notes = "Weekly recurring break"
        )

        // When
        val nextOccurrence = recurrencePattern.getNextOccurrence(ZonedDateTime.now())
        val isValidForMonday = recurrencePattern.isValidForStartDate(
            ZonedDateTime.now().with(java.time.DayOfWeek.MONDAY)
        )

        // Then
        assertNotNull(nextOccurrence)
        assertTrue(isValidForMonday)
        assertTrue(blockTime.isRecurring)
        assertEquals(RecurrenceType.WEEKLY, blockTime.recurrencePattern?.type)
    }

    @Test
    fun `should validate block time business rules`() {
        // Given
        val startTime = ZonedDateTime.now().plusHours(1)
        val endTime = startTime.plusHours(2)

        // When & Then - Valid block time
        val validBlock = TestDataBuilder.aBlockTime(
            startTime = startTime,
            endTime = endTime,
            reason = BlockReason.INTALNIRE
        )
        
        assertEquals(120, validBlock.getDurationMinutes())
        assertEquals("Întâlnire", validBlock.getDisplayReason())
    }

    @Test
    fun `should handle block time state changes`() {
        // Given
        val blockTime = TestDataBuilder.aBlockTime(
            notes = "Original notes"
        )
        val cancelledBy = UserId.of("admin-user")

        // When
        val cancelledBlock = blockTime.cancel(cancelledBy, "Meeting cancelled")
        val updatedBlock = blockTime.update(
            reason = BlockReason.PERSONAL,
            notes = "Updated notes",
            updatedBy = UserId.of("staff-user")
        )

        // Then
        assertEquals(BlockTimeStatus.CANCELLED, cancelledBlock.status)
        assertEquals(cancelledBy, cancelledBlock.updatedBy)
        assertTrue(cancelledBlock.notes!!.contains("Cancelled: Meeting cancelled"))

        assertEquals(BlockReason.PERSONAL, updatedBlock.reason)
        assertEquals("Updated notes", updatedBlock.notes)
        assertEquals(UserId.of("staff-user"), updatedBlock.updatedBy)
    }

    @Test
    fun `should detect time overlaps correctly`() {
        // Given
        val baseStartTime = ZonedDateTime.now().plusHours(1)
        val baseEndTime = baseStartTime.plusHours(2)
        val blockTime = TestDataBuilder.aBlockTime(
            startTime = baseStartTime,
            endTime = baseEndTime
        )

        // When & Then - Overlapping scenarios
        assertTrue(blockTime.overlapsWith(
            baseStartTime.minusMinutes(30), 
            baseStartTime.plusMinutes(30)
        ))
        
        assertTrue(blockTime.overlapsWith(
            baseEndTime.minusMinutes(30), 
            baseEndTime.plusMinutes(30)
        ))
        
        assertTrue(blockTime.overlapsWith(
            baseStartTime.plusMinutes(30), 
            baseEndTime.minusMinutes(30)
        ))

        // Non-overlapping scenarios
        assertTrue(!blockTime.overlapsWith(
            baseStartTime.minusHours(2), 
            baseStartTime.minusHours(1)
        ))
        
        assertTrue(!blockTime.overlapsWith(
            baseEndTime.plusHours(1), 
            baseEndTime.plusHours(2)
        ))
    }

    @Test
    fun `should handle custom reasons for ALTELE category`() {
        // Given
        val customReason = "Întâlnire cu furnizorul de produse"
        
        // When
        val blockTime = TestDataBuilder.aBlockTime(
            reason = BlockReason.ALTELE,
            customReason = customReason
        )

        // Then
        assertEquals(BlockReason.ALTELE, blockTime.reason)
        assertEquals(customReason, blockTime.customReason)
        assertEquals(customReason, blockTime.getDisplayReason())
    }

    @Test
    fun `should validate staff assignment`() {
        // Given
        val staff1 = StaffId.of("staff-1")
        val staff2 = StaffId.of("staff-2")
        val staff3 = StaffId.of("staff-3")
        val staffIds = setOf(staff1, staff2)

        val blockTime = TestDataBuilder.aBlockTime(staffIds = staffIds)

        // When & Then
        assertTrue(blockTime.affectsStaff(staff1))
        assertTrue(blockTime.affectsStaff(staff2))
        assertTrue(!blockTime.affectsStaff(staff3))
        assertEquals(2, blockTime.staffIds.size)
    }

    @Test
    fun `should handle time zone considerations`() {
        // Given
        val startTime = ZonedDateTime.now().plusHours(1)
        val endTime = startTime.plusHours(2)
        
        val blockTime = TestDataBuilder.aBlockTime(
            startTime = startTime,
            endTime = endTime
        )

        // When
        val duration = blockTime.getDurationMinutes()
        val isPast = blockTime.isInPast()

        // Then
        assertEquals(120, duration)
        assertTrue(!isPast) // Should not be in past since it's in the future
        assertEquals(startTime.zone, blockTime.startTime.zone)
        assertEquals(endTime.zone, blockTime.endTime.zone)
    }

    @Test
    fun `should support fluent builder pattern for complex scenarios`() {
        // Given & When
        val complexBlock = TestDataBuilder.blockTime()
            .withTimeRange(
                ZonedDateTime.now().plusDays(1).withHour(9).withMinute(0),
                ZonedDateTime.now().plusDays(1).withHour(17).withMinute(0)
            )
            .withReason(BlockReason.CONCEDIU, "Concediu medical")
            .withStaff("staff-1", "staff-2", "staff-3")
            .withNotes("Concediu planificat pentru echipa de grooming")
            .withRecurrence(
                TestDataBuilder.aRecurrencePattern(
                    type = RecurrenceType.DAILY,
                    interval = 1,
                    occurrences = 5
                )
            )
            .build()

        // Then
        assertEquals(BlockReason.CONCEDIU, complexBlock.reason)
        assertEquals("Concediu medical", complexBlock.customReason)
        assertEquals(3, complexBlock.staffIds.size)
        assertTrue(complexBlock.isRecurring)
        assertEquals(480, complexBlock.getDurationMinutes()) // 8 hours
        assertTrue(complexBlock.notes!!.contains("Concediu planificat"))
    }

    @Test
    fun `should validate minimum and maximum duration constraints`() {
        // Given
        val baseTime = ZonedDateTime.now().plusHours(1)

        // When & Then - Valid durations
        val shortValidBlock = TestDataBuilder.aBlockTime(
            startTime = baseTime,
            endTime = baseTime.plusMinutes(15) // Minimum valid duration
        )
        assertEquals(15, shortValidBlock.getDurationMinutes())

        val longValidBlock = TestDataBuilder.aBlockTime(
            startTime = baseTime,
            endTime = baseTime.plusHours(12) // Maximum valid duration
        )
        assertEquals(720, longValidBlock.getDurationMinutes())
    }

    @Test
    fun `should handle different block reasons correctly`() {
        // Given & When
        val pauseBlock = TestDataBuilder.aBlockTime(reason = BlockReason.PAUZA)
        val meetingBlock = TestDataBuilder.aBlockTime(reason = BlockReason.INTALNIRE)
        val vacationBlock = TestDataBuilder.aBlockTime(reason = BlockReason.CONCEDIU)
        val personalBlock = TestDataBuilder.aBlockTime(reason = BlockReason.PERSONAL)
        val trainingBlock = TestDataBuilder.aBlockTime(reason = BlockReason.TRAINING)

        // Then
        assertEquals("Pauză", pauseBlock.getDisplayReason())
        assertEquals("Întâlnire", meetingBlock.getDisplayReason())
        assertEquals("Concediu", vacationBlock.getDisplayReason())
        assertEquals("Personal", personalBlock.getDisplayReason())
        assertEquals("Training", trainingBlock.getDisplayReason())
    }
}
