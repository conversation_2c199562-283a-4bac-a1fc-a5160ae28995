
package ro.animaliaprogramari.animalia.integration

import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.assertThrows
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.jdbc.core.JdbcTemplate
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.jdbc.Sql
import org.springframework.transaction.annotation.Transactional
import ro.animaliaprogramari.animalia.application.port.inbound.AppointmentManagementUseCase
import ro.animaliaprogramari.animalia.application.port.inbound.BlockTimeManagementUseCase
import ro.animaliaprogramari.animalia.application.port.outbound.BlockTimeRepository
import ro.animaliaprogramari.animalia.domain.exception.AppointmentSchedulingConflictException
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.test.TestCommandBuilder
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZoneId
import java.time.ZonedDateTime

/**
 * Integration test to verify that appointments cannot be scheduled during blocked time periods
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
@Sql("/test-data-setup.sql")
@DisplayName("Block Time and Appointment Conflict Integration Test")
@Disabled("Requires database setup not available in CI")
class BlockTimeAppointmentConflictTest {

    @Autowired
    private lateinit var blockTimeManagementUseCase: BlockTimeManagementUseCase

    @Autowired
    private lateinit var appointmentManagementUseCase: AppointmentManagementUseCase

    @Autowired
    private lateinit var blockTimeRepository: BlockTimeRepository

    @Autowired
    private lateinit var jdbcTemplate: JdbcTemplate

    private var salonId: SalonId? = null
    private var staffId: StaffId? = null
    private var clientId: ClientId? = null
    private var petId: PetId? = null
    private var serviceId: ServiceId? = null
    private lateinit var blockStartTime: ZonedDateTime
    private lateinit var blockEndTime: ZonedDateTime

    @BeforeEach
    fun setUp() {
        // Setup test data - these IDs match the ones inserted by test-data-setup.sql
        salonId = SalonId.of("test-salon-conflict")
        staffId = StaffId.of("staff-test-conflict")
        clientId = ClientId.of("test-client-conflict")
        petId = PetId.of("test-pet-conflict")
        serviceId = ServiceId.of("test-service-conflict")

        // Create a block time for tomorrow from 10:00 to 11:00
        val tomorrow = LocalDate.now().plusDays(1)
        blockStartTime = ZonedDateTime.of(
            tomorrow,
            LocalTime.of(10, 0),
            ZoneId.systemDefault()
        )
        blockEndTime = blockStartTime.plusHours(1)

        // Update the placeholder block time with actual test dates using direct SQL
        // This avoids the JPA entity array mapping issues with H2
        jdbcTemplate.update(
            """
            UPDATE block_times
            SET start_time = ?, end_time = ?
            WHERE id = 'test-block-conflict'
            """,
            blockStartTime,
            blockEndTime
        )
    }

    @Test
    fun `should not allow scheduling appointment during blocked time`() {
        // Given - An appointment that overlaps with the block time
        val appointmentDate = blockStartTime.toLocalDate()
        
        // Appointment from 10:30 to 11:30 (overlaps with block time)
        val appointmentStartTime = LocalTime.of(10, 30)
        val appointmentEndTime = LocalTime.of(11, 30)
        
        val scheduleCommand = TestCommandBuilder.aScheduleAppointmentCommand()
            .withSalonId(salonId!!)
            .withClientId(clientId!!)
            .withPetId(petId!!)
            .withStaffId(staffId!!)
            .withServiceIds(listOf(serviceId!!))
            .withDate(appointmentDate)
            .withTimeSlot(appointmentStartTime, appointmentEndTime)
            .build()

        // When & Then - Expect an exception when trying to schedule during blocked time
        val exception = assertThrows<AppointmentSchedulingConflictException> {
            appointmentManagementUseCase.scheduleAppointment(scheduleCommand)
        }
        assert(exception.conflicts.isNotEmpty())
    }

    @Test
    fun `should allow scheduling appointment outside blocked time`() {
        // Given - An appointment that does not overlap with the block time
        val appointmentDate = blockStartTime.toLocalDate()
        
        // Appointment from 11:30 to 12:30 (after block time ends)
        val appointmentStartTime = LocalTime.of(11, 30)
        val appointmentEndTime = LocalTime.of(12, 30)
        
        val scheduleCommand = TestCommandBuilder.aScheduleAppointmentCommand()
            .withSalonId(salonId!!)
            .withClientId(clientId!!)
            .withPetId(petId!!)
            .withStaffId(staffId!!)
            .withServiceIds(listOf(serviceId!!))
            .withDate(appointmentDate)
            .withTimeSlot(appointmentStartTime, appointmentEndTime)
            .build()

        // When - Schedule the appointment
        val result = appointmentManagementUseCase.scheduleAppointment(scheduleCommand)

        // Then - Appointment should be created successfully
        assert(result.id.value.isNotEmpty())
        assert(result.staffId == staffId!!)
        assert(result.startTime == appointmentStartTime)
        assert(result.endTime == appointmentEndTime)
    }

    @Test
    fun `should not allow scheduling appointment that partially overlaps with block time`() {
        // Given - An appointment that partially overlaps with the block time
        val appointmentDate = blockStartTime.toLocalDate()
        
        // Appointment from 9:30 to 10:30 (overlaps with first 30 minutes of block time)
        val appointmentStartTime = LocalTime.of(9, 30)
        val appointmentEndTime = LocalTime.of(10, 30)
        
        val scheduleCommand = TestCommandBuilder.aScheduleAppointmentCommand()
            .withSalonId(salonId!!)
            .withClientId(clientId!!)
            .withPetId(petId!!)
            .withStaffId(staffId!!)
            .withServiceIds(listOf(serviceId!!))
            .withDate(appointmentDate)
            .withTimeSlot(appointmentStartTime, appointmentEndTime)
            .build()

        // When & Then - Expect an exception when trying to schedule during blocked time
        val exception = assertThrows<AppointmentSchedulingConflictException> {
            appointmentManagementUseCase.scheduleAppointment(scheduleCommand)
        }
        assert(exception.conflicts.isNotEmpty())
    }
}