package ro.animaliaprogramari.animalia.integration

import org.junit.jupiter.api.*
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.ClientEntityMapper
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa.SpringClientRepository
import ro.animaliaprogramari.animalia.application.port.inbound.AppointmentManagementUseCase
import ro.animaliaprogramari.animalia.application.port.outbound.*
import ro.animaliaprogramari.animalia.domain.exception.AppointmentSchedulingConflictException
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.test.TestCommandBuilder
import ro.animaliaprogramari.animalia.testutil.TestDataBuilder
import java.time.*
import kotlin.test.*
import org.junit.jupiter.api.Test

/**
 * Integration tests for appointment scheduling conflict detection and alternative suggestions.
 * Tests the complete workflow from conflict detection to alternative generation.
 */
@SpringBootTest
@ActiveProfiles("test")
@TestMethodOrder(MethodOrderer.OrderAnnotation::class)
@DisplayName("Scheduling Conflict Alternatives Integration Tests")
class SchedulingConflictAlternativesIntegrationTest(
) {


    @Autowired
    private lateinit var appointmentManagementUseCase: AppointmentManagementUseCase

    @Autowired
    private lateinit var salonRepository: SalonRepository

    @Autowired
    private lateinit var clientRepository: ClientRepository

    @Autowired
    private lateinit var petRepository: PetRepository

    @Autowired
    private lateinit var staffRepository: StaffRepository

    @Autowired
    private lateinit var salonServiceRepository: SalonServiceRepository

    @Autowired
    private lateinit var workingHoursRepository: WorkingHoursRepository

    @Autowired
    private lateinit var staffWorkingHoursRepository: StaffWorkingHoursRepository

    @Autowired
    private lateinit var appointmentRepository: AppointmentRepository

    @Autowired
    private lateinit var blockTimeRepository: BlockTimeRepository

    @Autowired
    private lateinit var userRepository: UserRepository

    @Autowired
    private lateinit var clientEntityMapper: ClientEntityMapper

    @Autowired
    private lateinit var springClientRepository: SpringClientRepository

    // Test data IDs
    private var salonId: SalonId? = null
    private var clientId: ClientId? = null
    private var petId: PetId? = null
    private var userId1: UserId? = null
    private var userId2: UserId? = null
    private var staffId1: StaffId? = null
    private var staffId2: StaffId? = null
    private var serviceId: ServiceId? = null

    @BeforeEach
    fun setUp() {
        setupTestData()
    }

    @AfterEach
    fun tearDown() {
        cleanupTestData()
    }

    private fun setupTestData() {
        // Create salon
        val salon = TestDataBuilder.aSalon(
            name = "Test Conflict Salon"
        )
        salonId = salon.id
        salonRepository.save(salon)

        // Create users first (required for staff foreign key)
        val user1 = TestDataBuilder.aUser(
            email = Email.of("<EMAIL>"),
            firebaseUid = "firebase-uid-1"
        )
        userId1 = user1.id
        userRepository.save(user1)

        val user2 = TestDataBuilder.aUser(
            email = Email.of("<EMAIL>"),
            firebaseUid = "firebase-uid-2"
        )
        userId2 = user2.id
        userRepository.save(user2)

        // Create client and associate with salon
        val client = TestDataBuilder.aClient(
            name = "Test Conflict Client",
            phone = PhoneNumber.of("+40123456789")
        )
        clientId = client.id

        // Save client with salon association using the entity mapper
        val clientEntity = clientEntityMapper.toEntity(client, salonId!!.value)
        springClientRepository.save(clientEntity)

        // Also add client to salon's client list
        val updatedSalon = salon.addClient(clientId!!)
        salonRepository.save(updatedSalon)

        // Create pet
        val pet = TestDataBuilder.aPet(
            clientId = clientId!!,
            name = "Buddy",
            species = "Dog",
            breed = "Golden Retriever"
        )
        petId = pet.id
        petRepository.save(pet)

        // Create staff members
        val staff1 = TestDataBuilder.aStaff(
            userId = userId1!!,
            salonId = salonId!!,
            nickname = "Staff One"
        )
        staffId1 = staff1.id
        staffRepository.save(staff1)

        val staff2 = TestDataBuilder.aStaff(
            userId = userId2!!,
            salonId = salonId!!,
            nickname = "Staff Two"
        )
        staffId2 = staff2.id
        staffRepository.save(staff2)

        // Create service
        val service = TestDataBuilder.aSalonService(
            salonId = salonId!!,
            name = "Test Grooming",
            duration = ro.animaliaprogramari.animalia.domain.model.Duration.ofMinutes(60),
            basePrice = Money.of(50.0)
        )
        serviceId = service.id
        salonServiceRepository.save(service)

        // Setup working hours (Monday-Friday 9:00-18:00, lunch break 13:00-14:00)
        val daySchedule = DaySchedule.workingDay(
            startTime = LocalTime.of(9, 0),
            endTime = LocalTime.of(18, 0),
            breakStart = LocalTime.of(13, 0),
            breakEnd = LocalTime.of(14, 0)
        )

        val weeklySchedule = mapOf(
            DayOfWeek.MONDAY to daySchedule,
            DayOfWeek.TUESDAY to daySchedule,
            DayOfWeek.WEDNESDAY to daySchedule,
            DayOfWeek.THURSDAY to daySchedule,
            DayOfWeek.FRIDAY to daySchedule,
            DayOfWeek.SATURDAY to DaySchedule.dayOff(),
            DayOfWeek.SUNDAY to DaySchedule.dayOff()
        )

        val workingHours = WorkingHoursSettings(
            salonId = salonId!!,
            weeklySchedule = weeklySchedule,
            holidays = emptyList(),
            customClosures = emptyList()
        )
        workingHoursRepository.save(workingHours)

        // Setup staff working hours (same as salon)
        listOf(staffId1!!, staffId2!!).forEach { staffId ->
            val staffWorkingHours = StaffWorkingHoursSettings(
                staffId = staffId,
                salonId = salonId!!,
                weeklySchedule = weeklySchedule,
                holidays = emptyList(),
                customClosures = emptyList()
            )
            staffWorkingHoursRepository.save(staffWorkingHours)
        }
    }

    private fun cleanupTestData() {
        try {
            // Clean up in reverse order of dependencies
            appointmentRepository.findBySalonIdWithFilters(salonId!!).forEach { appointment ->
                appointmentRepository.deleteById(appointment.id)
            }
            blockTimeRepository.findBySalonId(salonId!!).forEach { blockTime ->
                blockTimeRepository.delete(blockTime.id)
            }
            staffWorkingHoursRepository.findBySalonId(salonId!!).forEach { staffHours ->
                staffWorkingHoursRepository.deleteByStaffIdAndSalonId(staffHours.staffId, staffHours.salonId)
            }
            workingHoursRepository.findBySalonId(salonId!!)?.let { workingHours ->
                workingHoursRepository.deleteBySalonId(workingHours.salonId)
            }
            salonServiceRepository.findBySalonId(salonId!!).forEach { service ->
                salonServiceRepository.deleteById(service.id)
            }
            staffRepository.findBySalonId(salonId!!).forEach { staff ->
                staffRepository.delete(staff)
            }
            petRepository.findByClientId(clientId!!).forEach { pet ->
                petRepository.deleteById(pet.id)
            }
            clientRepository.findById(clientId!!)?.let { client ->
                clientRepository.deleteById(client.id)
            }
            userRepository.findById(userId1!!)?.let { user ->
                userRepository.deleteById(user.id)
            }
            userRepository.findById(userId2!!)?.let { user ->
                userRepository.deleteById(user.id)
            }
            salonRepository.findById(salonId!!)?.let { salon ->
                salonRepository.deleteById(salon.id)
            }
        } catch (e: Exception) {
            // Ignore cleanup errors in tests
        }
    }

    private fun getNextWeekday(): LocalDate {
        var date = LocalDate.now().plusDays(1)
        while (date.dayOfWeek == DayOfWeek.SATURDAY || date.dayOfWeek == DayOfWeek.SUNDAY) {
            date = date.plusDays(1)
        }
        return date
    }

    @Test
    @Order(1)
    @DisplayName("Should successfully create base appointment without conflicts")
    fun testCreateBaseAppointment() {
        // Given
        val appointmentDate = getNextWeekday()
        val command = TestCommandBuilder.aScheduleAppointmentCommand()
            .withSalonId(salonId!!)
            .withClientId(clientId!!)
            .withPetId(petId)
            .withStaffId(staffId1!!)
            .withDate(appointmentDate)
            .withTimeSlot(LocalTime.of(10, 0), LocalTime.of(11, 0))
            .withServiceIds(listOf(serviceId!!))
            .withNotes("Base appointment for conflict testing")
            .build()

        // When
        val appointment = appointmentManagementUseCase.scheduleAppointment(command)

        // Then
        assertNotNull(appointment)
        assertEquals(salonId, appointment.salonId)
        assertEquals(clientId, appointment.clientId)
        assertEquals(petId, appointment.petId)
        assertEquals(staffId1, appointment.staffId)
        assertEquals(LocalTime.of(10, 0), appointment.startTime)
        assertEquals(LocalTime.of(11, 0), appointment.endTime)
        assertEquals(AppointmentStatus.SCHEDULED, appointment.status)
    }

    @Test
    @Order(2)
    @DisplayName("Should detect appointment overlap conflict and suggest alternatives")
    fun testAppointmentOverlapConflictWithAlternatives() {
        // Given - Create base appointment first
        val appointmentDate = getNextWeekday().plusDays(3) // Use a different date to avoid same-day conflicts
        val baseCommand = TestCommandBuilder.aScheduleAppointmentCommand()
            .withSalonId(salonId!!)
            .withClientId(clientId!!)
            .withPetId(petId)
            .withStaffId(staffId1!!)
            .withDate(appointmentDate)
            .withTimeSlot(LocalTime.of(10, 0), LocalTime.of(11, 0))
            .withServiceIds(listOf(serviceId!!))
            .withNotes("Base appointment")
            .build()

        val baseAppointment = appointmentManagementUseCase.scheduleAppointment(baseCommand)

        // When - Try to create overlapping appointment
        val conflictingCommand = TestCommandBuilder.aScheduleAppointmentCommand()
            .withSalonId(salonId!!)
            .withClientId(clientId!!)
            .withNewPet("Max", "Dog", "Labrador", "L")
            .withStaffId(staffId1!!) // Same staff
            .withDate(appointmentDate)
            .withTimeSlot(LocalTime.of(10, 30), LocalTime.of(11, 30)) // Overlaps with base appointment
            .withServiceIds(listOf(serviceId!!))
            .withNotes("Conflicting appointment")
            .build()

        // Then - Should throw conflict exception with alternatives
        val exception = assertThrows<AppointmentSchedulingConflictException> {
            appointmentManagementUseCase.scheduleAppointment(conflictingCommand)
        }

        // Verify conflict details
        assertTrue(exception.conflicts.isNotEmpty(), "Should have conflict items")
        assertEquals(1, exception.conflicts.size, "Should have exactly one conflict")
        
        val conflict = exception.conflicts.first()
        assertEquals(ConflictItemType.APPOINTMENT, conflict.type)
        assertEquals(staffId1!!.value, conflict.staffId.value)
        assertEquals(baseAppointment.id.value, conflict.id)

        // Verify alternatives are provided
        assertTrue(exception.alternatives.isNotEmpty(), "Should provide alternatives")
        assertTrue(exception.alternatives.size <= 5, "Should not exceed 5 alternatives")

        // Check for different types of alternatives
        val hasStaffAlternative = exception.alternatives.any { it.type == AlternativeType.STAFF_ALTERNATIVE }
        val hasTimeAdjustment = exception.alternatives.any { it.type == AlternativeType.TIME_ADJUSTMENT }
        val hasDayAdjustment = exception.alternatives.any { it.type == AlternativeType.DAY_ADJUSTMENT }

        assertTrue(hasStaffAlternative || hasTimeAdjustment || hasDayAdjustment, 
            "Should provide at least one type of alternative")

        // Verify alternative suggestions are realistic
        exception.alternatives.forEach { alternative ->
            assertTrue(alternative.confidence > 0.0, "Alternative should have positive confidence")
            assertTrue(alternative.confidence <= 1.0, "Alternative confidence should not exceed 1.0")
            assertTrue(alternative.priority > 0, "Alternative should have positive priority")
            assertNotNull(alternative.reason, "Alternative should have a reason")
            assertNotNull(alternative.suggestion.staffName, "Alternative should have staff name")
        }
    }

    @Test
    @Order(3)
    @DisplayName("Should suggest staff alternative when same time slot is available with different staff")
    fun testStaffAlternativeSuggestion() {
        // Given - Create base appointment with staff1
        val appointmentDate = getNextWeekday().plusDays(4)
        val baseCommand = TestCommandBuilder.aScheduleAppointmentCommand()
            .withSalonId(salonId!!)
            .withClientId(clientId!!)
            .withPetId(petId)
            .withStaffId(staffId1!!)
            .withDate(appointmentDate)
            .withTimeSlot(LocalTime.of(10, 0), LocalTime.of(11, 0))
            .withServiceIds(listOf(serviceId!!))
            .withNotes("Base appointment")
            .build()

        appointmentManagementUseCase.scheduleAppointment(baseCommand)

        // When - Try to book same time with same staff (should conflict and suggest staff2)
        val command = TestCommandBuilder.aScheduleAppointmentCommand()
            .withSalonId(salonId!!)
            .withClientId(clientId!!)
            .withNewPet("Luna", "Cat", "Persian", "S")
            .withStaffId(staffId1!!) // Same staff as base appointment
            .withDate(appointmentDate)
            .withTimeSlot(LocalTime.of(10, 0), LocalTime.of(11, 0)) // Exact same time
            .withServiceIds(listOf(serviceId!!))
            .withNotes("Should suggest staff alternative")
            .build()

        println("Attempting to schedule conflicting appointment with command: $command")
        // Then
        val exception = assertThrows<AppointmentSchedulingConflictException> {
            appointmentManagementUseCase.scheduleAppointment(command)
        }
        println("Caught exception: $exception")

        // Should have staff alternative suggestion
        val timeAltternatives = exception.alternatives.filter { it.type == AlternativeType.TIME_ADJUSTMENT }
        assertTrue(timeAltternatives.isNotEmpty(), "Should suggest staff alternative")

        val staffAlternative = timeAltternatives.first()
        assertEquals(staffId1!!.value, staffAlternative.suggestion.staffId, "Should suggest staff1")
        assertEquals(appointmentDate, staffAlternative.suggestion.date, "Should keep same date")
        assertEquals(LocalTime.of(11, 30), staffAlternative.suggestion.startTime, "Should not keep same start time")
        assertEquals(LocalTime.of(12, 30), staffAlternative.suggestion.endTime, "Should not keep same end time")
        assertTrue(staffAlternative.confidence >= 0.8, "Time alternative should have high confidence")
        assertEquals(2, staffAlternative.priority, "Staff alternative should have highest priority")
    }
}
