package ro.animaliaprogramari.animalia.adapter.outbound.persistence

import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.StaffRoleEntity
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.test.TestDataBuilder
import kotlin.test.assertEquals
import kotlin.test.assertTrue

/**
 * Unit tests for StaffEntityMapper
 */
@DisplayName("StaffEntityMapper")
class StaffEntityMapperTest {

    private val mapper = StaffEntityMapper()

    @Nested
    @DisplayName("toDomain method")
    inner class ToDomainMethod {

        @Test
        fun `should map StaffEntity to Staff domain object correctly`() {
            // Given
            val staffEntity = TestDataBuilder.aStaffEntity()
                .withRole(StaffRoleEntity.GROOMER)
                .build()

            // When
            val result = mapper.toDomain(staffEntity)

            // Then
            assertEquals(StaffId.of(staffEntity.id), result.id)
            assertEquals(UserId.of(staffEntity.userId), result.userId)
            assertEquals(SalonId.of(staffEntity.salonId), result.salonId)
            assertEquals(StaffRole.GROOMER, result.role)
            assertEquals(StaffPermissions.defaultGroomerAccess(), result.permissions)
            assertEquals(staffEntity.isActive, result.isActive)
            assertEquals(staffEntity.hireDate.atStartOfDay(), result.hiredAt)
            assertEquals(staffEntity.createdAt, result.createdAt)
            assertEquals(staffEntity.updatedAt, result.updatedAt)
        }

        @Test
        fun `should map chief groomer entity with full permissions`() {
            // Given
            val chiefGroomerEntity = TestDataBuilder.aStaffEntity()
                .withRole(StaffRoleEntity.CHIEF_GROOMER)
                .build()

            // When
            val result = mapper.toDomain(chiefGroomerEntity)

            // Then
            assertEquals(StaffRole.CHIEF_GROOMER, result.role)
            assertEquals(StaffPermissions.fullAccess(), result.permissions)
            assertTrue(result.permissions.canManageServices)
            assertTrue(result.permissions.canViewReports)
        }

        @Test
        fun `should map regular groomer entity with limited permissions`() {
            // Given
            val groomerEntity = TestDataBuilder.aStaffEntity()
                .withRole(StaffRoleEntity.GROOMER)
                .build()

            // When
            val result = mapper.toDomain(groomerEntity)

            // Then
            assertEquals(StaffRole.GROOMER, result.role)
            assertEquals(StaffPermissions.defaultGroomerAccess(), result.permissions)
            assertEquals(false, result.permissions.canManageServices)
            assertEquals(false, result.permissions.canViewReports)
        }
    }

    @Nested
    @DisplayName("toEntity method")
    inner class ToEntityMethod {

        @Test
        fun `should map Staff domain object to StaffEntity correctly`() {
            // Given
            val staff = TestDataBuilder.aStaff()
                .withRole(StaffRole.GROOMER)
                .build()

            // When
            val result = mapper.toEntity(staff)

            // Then
            assertEquals(staff.id.value, result.id)
            assertEquals(staff.userId.value, result.userId)
            assertEquals(staff.salonId.value, result.salonId)
            assertEquals(StaffRoleEntity.GROOMER, result.role)
            assertEquals(staff.isActive, result.isActive)
            assertEquals(staff.hiredAt.toLocalDate(), result.hireDate)
            assertEquals(staff.createdAt, result.createdAt)
            assertEquals(staff.updatedAt, result.updatedAt)
        }

        @Test
        fun `should map chief groomer staff to chief groomer entity`() {
            // Given
            val chiefGroomerStaff = Staff.createChiefGroomer(
                userId = UserId.generate(),
                salonId = SalonId.generate()
            )

            // When
            val result = mapper.toEntity(chiefGroomerStaff)

            // Then
            assertEquals(chiefGroomerStaff.id.value, result.id)
            assertEquals(chiefGroomerStaff.userId.value, result.userId)
            assertEquals(chiefGroomerStaff.salonId.value, result.salonId)
            assertEquals(StaffRoleEntity.CHIEF_GROOMER, result.role)
            assertEquals(chiefGroomerStaff.isActive, result.isActive)
        }

        @Test
        fun `should map regular groomer staff to groomer entity`() {
            // Given
            val groomerStaff = Staff.createGroomer(
                userId = UserId.generate(),
                salonId = SalonId.generate()
            )

            // When
            val result = mapper.toEntity(groomerStaff)

            // Then
            assertEquals(groomerStaff.id.value, result.id)
            assertEquals(groomerStaff.userId.value, result.userId)
            assertEquals(groomerStaff.salonId.value, result.salonId)
            assertEquals(StaffRoleEntity.GROOMER, result.role)
            assertEquals(groomerStaff.isActive, result.isActive)
        }
    }

    @Nested
    @DisplayName("Round-trip conversion")
    inner class RoundTripConversion {

        @Test
        fun `should maintain data integrity in round-trip conversion for chief groomer`() {
            // Given
            val originalStaff = Staff.createChiefGroomer(
                userId = UserId.generate(),
                salonId = SalonId.generate()
            )

            // When
            val entity = mapper.toEntity(originalStaff)
            val convertedStaff = mapper.toDomain(entity)

            // Then
            assertEquals(originalStaff.id, convertedStaff.id)
            assertEquals(originalStaff.userId, convertedStaff.userId)
            assertEquals(originalStaff.salonId, convertedStaff.salonId)
            assertEquals(originalStaff.role, convertedStaff.role)
            assertEquals(originalStaff.isActive, convertedStaff.isActive)
            // Note: permissions might differ due to mapping logic, but should be valid for role
            assertTrue(convertedStaff.permissions.isValidForRole(convertedStaff.role))
        }

        @Test
        fun `should maintain data integrity in round-trip conversion for regular groomer`() {
            // Given
            val originalStaff = Staff.createGroomer(
                userId = UserId.generate(),
                salonId = SalonId.generate()
            )

            // When
            val entity = mapper.toEntity(originalStaff)
            val convertedStaff = mapper.toDomain(entity)

            // Then
            assertEquals(originalStaff.id, convertedStaff.id)
            assertEquals(originalStaff.userId, convertedStaff.userId)
            assertEquals(originalStaff.salonId, convertedStaff.salonId)
            assertEquals(originalStaff.role, convertedStaff.role)
            assertEquals(originalStaff.isActive, convertedStaff.isActive)
            assertTrue(convertedStaff.permissions.isValidForRole(convertedStaff.role))
        }
    }
}
