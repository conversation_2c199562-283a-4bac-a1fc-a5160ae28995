package ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity

import jakarta.persistence.*
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

/**
 * Test-specific StaffEntity that avoids H2 compatibility issues
 * This version uses TEXT instead of JSON for the working_hours column
 */
@Entity
@Table(name = "staff")
class TestStaffEntity(
    @Id
    val id: String,

    @Column(name = "user_id", nullable = false)
    val userId: String,

    @Column(name = "nickname", nullable = true)
    val nickName: String? = null,

    @Column(name = "salon_id", nullable = false)
    val salonId: String,

    @Column(name = "role", nullable = false)
    @Enumerated(EnumType.STRING)
    val role: StaffRoleEntity,

    @Column(name = "working_hours", columnDefinition = "TEXT")
    val workingHours: String, // JSON representation as TEXT for H2 compatibility

    @Column(name = "hire_date", nullable = false)
    val hireDate: LocalDate,

    @ElementCollection(fetch = FetchType.EAGER)
    @CollectionTable(
        name = "staff_specializations",
        joinColumns = [JoinColumn(name = "staff_id")]
    )
    @Enumerated(EnumType.STRING)
    @Column(name = "specialization")
    val specializations: Set<SpecializationEntity>,

    @Column(name = "is_active", nullable = false)
    val isActive: Boolean = true,

    @Column(name = "created_at", nullable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @Column(name = "updated_at", nullable = false)
    val updatedAt: LocalDateTime = LocalDateTime.now()
) {
    // Default constructor for JPA
    constructor() : this(
        id = UUID.randomUUID().toString(),
        userId = UUID.randomUUID().toString(),
        nickName = null,
        salonId = UUID.randomUUID().toString(),
        role = StaffRoleEntity.GROOMER,
        workingHours = "{}",
        hireDate = LocalDate.now(),
        specializations = emptySet(),
        isActive = true,
        createdAt = LocalDateTime.now(),
        updatedAt = LocalDateTime.now()
    )
}
