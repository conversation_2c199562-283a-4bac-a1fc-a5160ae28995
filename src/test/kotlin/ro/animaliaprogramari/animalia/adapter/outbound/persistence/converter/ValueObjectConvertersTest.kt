package ro.animaliaprogramari.animalia.adapter.outbound.persistence.converter

import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import ro.animaliaprogramari.animalia.domain.model.SalonId
import ro.animaliaprogramari.animalia.domain.model.StaffId
import ro.animaliaprogramari.animalia.domain.model.UserId
import kotlin.test.assertEquals
import kotlin.test.assertNull

/**
 * Unit tests for JPA value object converters
 */
@DisplayName("Value Object Converters")
class ValueObjectConvertersTest {

    @Nested
    @DisplayName("StaffIdConverter")
    inner class StaffIdConverterTest {
        
        private val converter = StaffIdConverter()

        @Test
        fun `should convert StaffId to database column`() {
            // Given
            val staffId = StaffId.of("staff-123")

            // When
            val result = converter.convertToDatabaseColumn(staffId)

            // Then
            assertEquals("staff-123", result)
        }

        @Test
        fun `should convert null StaffId to null database column`() {
            // When
            val result = converter.convertToDatabaseColumn(null)

            // Then
            assertNull(result)
        }

        @Test
        fun `should convert database column to StaffId`() {
            // Given
            val dbValue = "staff-456"

            // When
            val result = converter.convertToEntityAttribute(dbValue)

            // Then
            assertEquals(StaffId.of("staff-456"), result)
        }

        @Test
        fun `should convert null database column to null StaffId`() {
            // When
            val result = converter.convertToEntityAttribute(null)

            // Then
            assertNull(result)
        }

        @Test
        fun `should convert blank database column to null StaffId`() {
            // When
            val result = converter.convertToEntityAttribute("")

            // Then
            assertNull(result)
        }
    }

    @Nested
    @DisplayName("UserIdConverter")
    inner class UserIdConverterTest {
        
        private val converter = UserIdConverter()

        @Test
        fun `should convert UserId to database column`() {
            // Given
            val userId = UserId.of("user-123")

            // When
            val result = converter.convertToDatabaseColumn(userId)

            // Then
            assertEquals("user-123", result)
        }

        @Test
        fun `should convert null UserId to null database column`() {
            // When
            val result = converter.convertToDatabaseColumn(null)

            // Then
            assertNull(result)
        }

        @Test
        fun `should convert database column to UserId`() {
            // Given
            val dbValue = "user-456"

            // When
            val result = converter.convertToEntityAttribute(dbValue)

            // Then
            assertEquals(UserId.of("user-456"), result)
        }

        @Test
        fun `should convert null database column to null UserId`() {
            // When
            val result = converter.convertToEntityAttribute(null)

            // Then
            assertNull(result)
        }

        @Test
        fun `should convert blank database column to null UserId`() {
            // When
            val result = converter.convertToEntityAttribute("")

            // Then
            assertNull(result)
        }
    }

    @Nested
    @DisplayName("SalonIdConverter")
    inner class SalonIdConverterTest {
        
        private val converter = SalonIdConverter()

        @Test
        fun `should convert SalonId to database column`() {
            // Given
            val salonId = SalonId.of("salon-123")

            // When
            val result = converter.convertToDatabaseColumn(salonId)

            // Then
            assertEquals("salon-123", result)
        }

        @Test
        fun `should convert null SalonId to null database column`() {
            // When
            val result = converter.convertToDatabaseColumn(null)

            // Then
            assertNull(result)
        }

        @Test
        fun `should convert database column to SalonId`() {
            // Given
            val dbValue = "salon-456"

            // When
            val result = converter.convertToEntityAttribute(dbValue)

            // Then
            assertEquals(SalonId.of("salon-456"), result)
        }

        @Test
        fun `should convert null database column to null SalonId`() {
            // When
            val result = converter.convertToEntityAttribute(null)

            // Then
            assertNull(result)
        }

        @Test
        fun `should convert blank database column to null SalonId`() {
            // When
            val result = converter.convertToEntityAttribute("")

            // Then
            assertNull(result)
        }
    }
}
