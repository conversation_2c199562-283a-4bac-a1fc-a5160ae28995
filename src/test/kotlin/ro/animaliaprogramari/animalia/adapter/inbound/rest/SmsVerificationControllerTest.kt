package ro.animaliaprogramari.animalia.adapter.inbound.rest

import kotlin.test.Test
import kotlin.test.assertEquals

class SmsVerificationControllerTest {
    private val controller = SmsVerificationController()

    @Test
    fun `should send sms verification`() {
        val response = controller.sendSmsVerification(SendSmsRequest("+40123456789"))
        assertEquals(true, response.success)
        assertEquals("+40123456789", response.data?.get("phoneNumber"))
    }
}
