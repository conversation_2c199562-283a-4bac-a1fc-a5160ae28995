package ro.animaliaprogramari.animalia.adapter.inbound.rest

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.KotlinModule
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.DisplayName
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.RescheduleAppointmentRequest
import java.time.LocalDateTime
import kotlin.test.assertEquals
import kotlin.test.assertNotNull

/**
 * Test for reschedule appointment JSON parsing
 * Tests that the datetime format issue is resolved
 */
@DisplayName("Reschedule Appointment JSON Parsing Tests")
class RescheduleAppointmentIntegrationTest {

    private val objectMapper = ObjectMapper().apply {
        registerModule(JavaTimeModule())
        registerModule(KotlinModule.Builder().build())
    }

    @Test
    fun `should parse reschedule request with milliseconds in datetime`() {
        // Given - JSON with milliseconds (the problematic format that was failing)
        val requestJson = """
            {
                "startTime": "2025-06-11T10:45:00.000",
                "endTime": "2025-06-11T11:45:00.000",
                "reason": "Client requested time change"
            }
        """.trimIndent()

        // When
        val request = objectMapper.readValue(requestJson, RescheduleAppointmentRequest::class.java)

        // Then
        assertNotNull(request)
        assertEquals(LocalDateTime.of(2025, 6, 11, 10, 45, 0, 0), request.startTime)
        assertEquals(LocalDateTime.of(2025, 6, 11, 11, 45, 0, 0), request.endTime)
        assertEquals("Client requested time change", request.reason)
    }

    @Test
    fun `should parse reschedule request without milliseconds in datetime`() {
        // Given - JSON without milliseconds (standard format)
        val requestJson = """
            {
                "startTime": "2025-06-11T10:45:00",
                "endTime": "2025-06-11T11:45:00",
                "reason": "Standard format"
            }
        """.trimIndent()

        // When
        val request = objectMapper.readValue(requestJson, RescheduleAppointmentRequest::class.java)

        // Then
        assertNotNull(request)
        assertEquals(LocalDateTime.of(2025, 6, 11, 10, 45, 0), request.startTime)
        assertEquals(LocalDateTime.of(2025, 6, 11, 11, 45, 0), request.endTime)
        assertEquals("Standard format", request.reason)
    }

    @Test
    fun `should parse reschedule request with different millisecond values`() {
        // Given - JSON with various millisecond values
        val requestJson = """
            {
                "startTime": "2025-06-11T10:45:00.123",
                "endTime": "2025-06-11T11:45:00.456",
                "reason": "Different milliseconds"
            }
        """.trimIndent()

        // When
        val request = objectMapper.readValue(requestJson, RescheduleAppointmentRequest::class.java)

        // Then
        assertNotNull(request)
        assertEquals(LocalDateTime.of(2025, 6, 11, 10, 45, 0, 123_000_000), request.startTime)
        assertEquals(LocalDateTime.of(2025, 6, 11, 11, 45, 0, 456_000_000), request.endTime)
        assertEquals("Different milliseconds", request.reason)
    }
}
