package ro.animaliaprogramari.animalia.adapter.inbound.rest

import io.mockk.every
import io.mockk.mockkObject
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Primary
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity
import org.springframework.security.config.annotation.web.builders.HttpSecurity
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.security.web.SecurityFilterChain
import ro.animaliaprogramari.animalia.adapter.inbound.security.SecurityUtils
import ro.animaliaprogramari.animalia.domain.model.*

/**
 * Test security configuration that provides proper AuthenticatedUser objects for integration tests
 */
@TestConfiguration
@EnableWebSecurity
@EnableMethodSecurity(prePostEnabled = true)
class TestSecurityConfig {

    @Bean
    @Primary
    fun testSecurityFilterChain(http: HttpSecurity): SecurityFilterChain {
        http
            .csrf { it.disable() }
            .authorizeHttpRequests { auth ->
                auth.anyRequest().permitAll()
            }
            .headers { headers ->
                headers.frameOptions().disable()
            }

        return http.build()
    }

    init {
        // Mock SecurityUtils to return proper AuthenticatedUser objects based on the test context
        mockkObject(SecurityUtils)
        every { SecurityUtils.getCurrentUser() } answers {
            val authentication = SecurityContextHolder.getContext().authentication
            when (authentication?.name) {
                "chief_groomer_firebase_uid" -> createChiefGroomerUser()
                "groomer_firebase_uid" -> createGroomerUser()
                "client_firebase_uid" -> createClientUser()
                else -> null
            }
        }

        // Also mock the authentication principal to return AuthenticatedUser objects
        every { SecurityUtils.getCurrentUser() } answers {
            val authentication = SecurityContextHolder.getContext().authentication
            // If the principal is already an AuthenticatedUser, return it
            if (authentication?.principal is AuthenticatedUser) {
                authentication.principal as AuthenticatedUser
            } else {
                // Otherwise, create based on the username
                when (authentication?.name) {
                    "chief_groomer_firebase_uid" -> createChiefGroomerUser()
                    "groomer_firebase_uid" -> createGroomerUser()
                    "client_firebase_uid" -> createClientUser()
                    else -> null
                }
            }
        }
    }

    private fun createChiefGroomerUser(): AuthenticatedUser {
        val userId = UserId.generate()

        return AuthenticatedUser(
            userId = userId,
            firebaseUid = "chief_groomer_firebase_uid",
            email = Email.of("<EMAIL>"),
            phoneNumber = "+40731446895",
            name = "Chief Groomer",
            role = UserRole.STAFF,
            currentSalonId = null, // Will be set by the test
            isActive = true,
            staffAssociations = emptyList()
        )
    }

    private fun createGroomerUser(): AuthenticatedUser {
        val userId = UserId.generate()

        return AuthenticatedUser(
            userId = userId,
            firebaseUid = "groomer_firebase_uid",
            email = Email.of("<EMAIL>"),
            phoneNumber = "+40712345679",
            name = "Regular Groomer",
            role = UserRole.STAFF,
            currentSalonId = null, // Will be set by the test
            isActive = true,
            staffAssociations = emptyList()
        )
    }

    private fun createClientUser(): AuthenticatedUser {
        val userId = UserId.generate()

        return AuthenticatedUser(
            userId = userId,
            firebaseUid = "client_firebase_uid",
            email = Email.of("<EMAIL>"),
            phoneNumber = "+40731446895",
            name = "Test Client",
            role = UserRole.USER,
            currentSalonId = null,
            isActive = true,
            staffAssociations = emptyList()
        )
    }
}
