package ro.animaliaprogramari.animalia

import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.boot.runApplication
import org.springframework.transaction.annotation.EnableTransactionManagement
import ro.animaliaprogramari.animalia.config.BusinessRulesConfig

@SpringBootApplication
@EnableConfigurationProperties(BusinessRulesConfig::class)
@EnableTransactionManagement
class AnimaliaProgramariBackendApplication

fun main(args: Array<String>) {
    runApplication<AnimaliaProgramariBackendApplication>(*args)
}
