package ro.animaliaprogramari.animalia.config

import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Profile
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity
import org.springframework.security.config.annotation.web.builders.HttpSecurity
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity
import org.springframework.security.config.http.SessionCreationPolicy
import org.springframework.security.web.SecurityFilterChain
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter
import org.springframework.web.cors.CorsConfiguration
import org.springframework.web.cors.CorsConfigurationSource
import org.springframework.web.cors.UrlBasedCorsConfigurationSource
import ro.animaliaprogramari.animalia.adapter.inbound.security.JwtAuthenticationFilter

/**
 * Spring Security configuration for Firebase Auth integration
 */
@Configuration
@EnableWebSecurity
@EnableMethodSecurity(prePostEnabled = true)
@Profile("!test") // Don't apply in test profile
class SecurityConfig(
    private val jwtAuthenticationFilter: JwtAuthenticationFilter
) {

    @Bean
    fun securityFilterChain(http: HttpSecurity): SecurityFilterChain {
        http
            .csrf { it.disable() }
            .cors { it.configurationSource(corsConfigurationSource()) }
            .sessionManagement { it.sessionCreationPolicy(SessionCreationPolicy.STATELESS) }
            .authorizeHttpRequests { auth ->
                auth
                    // Public endpoints
                    .requestMatchers("/auth/**").permitAll()
                    .requestMatchers("/actuator/**").permitAll()
                    .requestMatchers("/health/**").permitAll()
                    .requestMatchers("/health").permitAll()
                    .requestMatchers("/h2-console/**").permitAll() // For development

                    // Admin endpoints
                    .requestMatchers("/admin/**").hasRole("ADMIN")

                    // Groomer endpoints - require ADMIN or GROOMER role
                    .requestMatchers("/groomers/**").hasAnyRole("ADMIN", "STAFF", "USER")
                    .requestMatchers("/appointments/**").hasAnyRole("ADMIN", "STAFF", "USER")

                    // Permission management endpoints - require ADMIN or STAFF role
                    .requestMatchers("/permissions/**").hasAnyRole("ADMIN", "STAFF")
                    .requestMatchers("/salons/**").hasAnyRole("ADMIN", "STAFF", "USER")

                    // All other endpoints require authentication
                    .anyRequest().authenticated()
            }
            .addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter::class.java)

        // For H2 console in development
        http.headers { headers ->
            headers.frameOptions { it.sameOrigin() }
        }

        return http.build()
    }

    @Bean
    fun corsConfigurationSource(): CorsConfigurationSource {
        val configuration = CorsConfiguration()

        // Allow Firebase authentication domains and development
        val allowedOrigins = System.getenv("ALLOWED_ORIGINS")?.split(",")
            ?: listOf(
                "http://localhost:3000",
                "http://localhost:8080",
                "http://127.0.0.1:3000",
                "http://127.0.0.1:8080",
                "https://animalia-app.com",
                "https://www.animalia-app.com",
                "https://animalia-de0f1.firebaseapp.com",
                "https://animalia-de0f1.web.app"
            )

        configuration.allowedOrigins = allowedOrigins
        configuration.allowedMethods = listOf("GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH")
        configuration.allowedHeaders = listOf(
            "*",
            "Authorization",
            "Content-Type",
            "X-Requested-With",
            "Accept",
            "Origin",
            "Access-Control-Request-Method",
            "Access-Control-Request-Headers",
            "Firebase-Instance-ID-Token",
            "X-Firebase-Auth"
        )
        configuration.exposedHeaders = listOf("X-Total-Count", "X-Page-Count", "Authorization")
        configuration.allowCredentials = true
        configuration.maxAge = 3600L

        val source = UrlBasedCorsConfigurationSource()
        source.registerCorsConfiguration("/**", configuration)
        return source
    }
}
