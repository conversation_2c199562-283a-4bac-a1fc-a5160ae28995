package ro.animaliaprogramari.animalia.adapter.inbound.rest.dto

import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.constraints.NotBlank
import java.time.LocalDateTime

/**
 * Response DTO for user salon association
 */
@Schema(description = "User's association with a salon")
data class UserSalonAssociationResponse(
    @Schema(description = "Association ID")
    @JsonProperty("id")
    val id: String,
    
    @Schema(description = "User ID")
    @JsonProperty("userId")
    val userId: String,
    
    @Schema(description = "Salon ID")
    @JsonProperty("salonId")
    val salonId: String,
    
    @Schema(description = "Salon details")
    @JsonProperty("salon")
    val salon: SalonResponse,
    
    @Schema(description = "Role of the groomer")
    @JsonProperty("groomerRole")
    val groomerRole: String,
    
    @Schema(description = "Permission level for client data")
    @JsonProperty("clientDataPermission")
    val clientDataPermission: String,
    
    @Schema(description = "Whether this is the active salon")
    @JsonProperty("isCurrentSalon")
    val isCurrentSalon: Boolean,
    
    @Schema(description = "Number of clients in the salon")
    @JsonProperty("clientCount")
    val clientCount: Int,
    
    @Schema(description = "Association creation timestamp")
    @JsonProperty("createdAt")
    val createdAt: LocalDateTime,
    
    @Schema(description = "Last update timestamp")
    @JsonProperty("updatedAt")
    val updatedAt: LocalDateTime
)

/**
 * Request DTO for switching salon
 */
@Schema(description = "Request to change active salon")
data class SwitchSalonRequest(
    @field:NotBlank(message = "Salon ID is required")
    @Schema(description = "Target salon ID")
    @JsonProperty("salonId")
    val salonId: String
)

/**
 * Response DTO for switch salon operation
 */
@Schema(description = "Response after switching salon")
data class SwitchSalonResponse(
    @Schema(description = "Operation successful")
    @JsonProperty("success")
    val success: Boolean,
    
    @Schema(description = "New current salon ID")
    @JsonProperty("currentSalonId")
    val currentSalonId: String?,
    
    @Schema(description = "Informational message")
    @JsonProperty("message")
    val message: String
)
