package ro.animaliaprogramari.animalia.adapter.inbound.rest

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.responses.ApiResponse as SwaggerApiResponse
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.validation.Valid
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.*
import ro.animaliaprogramari.animalia.adapter.inbound.security.SecurityUtils
import ro.animaliaprogramari.animalia.application.port.inbound.SalonStaffManagementUseCase
import ro.animaliaprogramari.animalia.application.port.inbound.InvitationManagementUseCase
import ro.animaliaprogramari.animalia.application.port.outbound.UserRepository
import ro.animaliaprogramari.animalia.application.command.SendSalonInvitationCommand
import ro.animaliaprogramari.animalia.config.AnimaliaConstants
import ro.animaliaprogramari.animalia.domain.model.*

/**
 * REST controller for salon staff management operations
 */
@RestController
@RequestMapping("/salons/{salonId}/staff")
@CrossOrigin(origins = ["http://localhost:3000", "http://localhost:8080"])
@Tag(name = "Salon Staff Management", description = "Operations for managing staff within salons")
class SalonStaffController(
    private val salonStaffManagementUseCase: SalonStaffManagementUseCase,
    private val invitationManagementUseCase: InvitationManagementUseCase,
    private val userRepository: UserRepository
) {

    private val logger = LoggerFactory.getLogger(SalonStaffController::class.java)

    /**
     * GET /api/salons/{salonId}/staff
     * Get all staff for a salon
     */
    @GetMapping
    @Operation(summary = "Get salon staff", description = "Get all staff members for a specific salon")
    @SwaggerApiResponse(responseCode = "200", description = "Staff list retrieved successfully")
    @SwaggerApiResponse(responseCode = "401", description = AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR)
    @SwaggerApiResponse(responseCode = "403", description = "Access denied")
    fun getSalonStaff(
        @PathVariable salonId: String,
        @RequestParam(required = false, defaultValue = "true") activeOnly: Boolean,
        @RequestParam(required = false) search: String?
    ): ResponseEntity<ApiResponse<List<SalonStaffResponse>>> {
        logger.info("=== GET SALON STAFF REQUEST ===")
        logger.info("Salon ID: $salonId")
        logger.info("Active only: $activeOnly")
        logger.info("Search: $search")

        return try {
            val currentUser = SecurityUtils.getCurrentUser()
                ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            logger.info("Current user: ${currentUser.userId.value}")

            val salon = SalonId.of(salonId)

            // Authorization: User must have access to this salon
            if (!currentUser.hasClientDataAccessInSalon(salon) && !currentUser.isAdmin()) {
                logger.warn("User ${currentUser.userId.value} denied access to salon $salonId")
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error("Nu aveți permisiunea să accesați această resursă"))
            }

            val staffList = salonStaffManagementUseCase.getSalonStaff(salon, activeOnly, search)
            val responses = staffList.map { staffInfo ->
                SalonStaffResponse(staffInfo.staff, staffInfo)
            }

            logger.info("Found ${responses.size} staff members")
            ResponseEntity.ok(ApiResponse.success(responses))

        } catch (e: Exception) {
            logger.error("Error getting salon staff", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Eroare la obținerea angajaților: ${e.message}"))
        }
    }

    /**
     * POST /api/salons/{salonId}/staff
     * Add staff to salon by phone number (existing user) or send invitation (new user)
     */
    @PostMapping
    @Operation(
        summary = "Add staff to salon",
        description = "Add staff to salon by phone number. If user exists, adds them directly. If user doesn't exist, sends an invitation."
    )
    @SwaggerApiResponse(responseCode = "201", description = "Staff added successfully (existing user)")
    @SwaggerApiResponse(responseCode = "202", description = "Invitation sent successfully (new user)")
    @SwaggerApiResponse(responseCode = "400", description = "Invalid request data or user already staff")
    @SwaggerApiResponse(responseCode = "401", description = AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR)
    @SwaggerApiResponse(responseCode = "403", description = "Access denied")
    fun addStaffToSalon(
        @PathVariable salonId: String,
        @Valid @RequestBody request: AddStaffToSalonRequest
    ): ResponseEntity<ApiResponse<Any>> {
        logger.info("=== ADD STAFF TO SALON REQUEST ===")
        logger.info("Salon ID: $salonId")
        logger.info("Phone Number: ${request.phoneNumber}")
        logger.info("Groomer Role: ${request.groomerRole}")
        logger.info("Client Data Permission: ${request.clientDataPermission}")
        logger.info("Notes: ${request.notes}")

        return try {
            val currentUser = SecurityUtils.getCurrentUser()
                ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            logger.info("Current user: ${currentUser.userId.value}")

            val salon = SalonId.of(salonId)

            // Authorization: Only chief groomers or admins can add staff
            if (!currentUser.isChiefGroomerInSalon(salon) && !currentUser.isAdmin()) {
                logger.warn("User ${currentUser.userId.value} denied permission to add staff to salon $salonId")
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error("Nu aveți permisiunea să adăugați angajați în acest salon"))
            }

            // Validate phone number format
            if (!PhoneNumber.isValidRomanianFormat(request.phoneNumber)) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Formatul numărului de telefon nu este valid. Folosiți formatul +40XXXXXXXXX"))
            }

            // Validate and map groomer role
            val staffRole = try {
                when (request.groomerRole.uppercase()) {
                    "CHIEF_GROOMER" -> StaffRole.CHIEF_GROOMER
                    "REGULAR_GROOMER", "GROOMER" -> StaffRole.GROOMER
                    "SENIOR_GROOMER" -> StaffRole.SENIOR_GROOMER
                    "ASSISTANT" -> StaffRole.ASSISTANT
                    else -> throw IllegalArgumentException("Invalid groomer role: ${request.groomerRole}")
                }
            } catch (e: IllegalArgumentException) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Rolul specificat nu este valid: ${request.groomerRole}"))
            }

            // Validate and map client data permission
            val clientDataAccess = try {
                when (request.clientDataPermission.uppercase()) {
                    "FULL_ACCESS", "FULL" -> ClientDataAccess.FULL
                    "READ_ONLY", "LIMITED_ACCESS", "LIMITED" -> ClientDataAccess.LIMITED
                    "NO_ACCESS", "NONE" -> ClientDataAccess.NONE
                    else -> throw IllegalArgumentException("Invalid client data permission: ${request.clientDataPermission}")
                }
            } catch (e: IllegalArgumentException) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Permisiunea pentru datele clientului nu este validă: ${request.clientDataPermission}"))
            }

            // Create permissions based on role and client data access
            val permissions = when (staffRole) {
                StaffRole.CHIEF_GROOMER -> StaffPermissions.fullAccess()
                else -> StaffPermissions(
                    clientDataAccess = clientDataAccess,
                    canManageAppointments = true,
                    canManageServices = false,
                    canViewReports = clientDataAccess == ClientDataAccess.FULL,
                    canManageSchedule = true
                )
            }

            // Check if user already exists and is already staff
            logger.info("Checking if user exists with phone number: ${request.phoneNumber}")
            val existingUser = userRepository.findByPhoneNumber(request.phoneNumber)

            if (existingUser != null) {
                // Check if user is already staff in this salon
                val existingStaff = salonStaffManagementUseCase.getSalonStaff(salon, false, null)
                    .find { it.staff.userId == existingUser.id }

                if (existingStaff != null && existingStaff.staff.isActive) {
                    return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(ApiResponse.error("Utilizatorul este deja angajat în acest salon"))
                }

                // User exists but not staff - add directly
                logger.info("User exists, adding directly as staff: ${existingUser.id.value}")
                val staff = salonStaffManagementUseCase.addUserToSalon(
                    salonId = salon,
                    userId = existingUser.id,
                    role = staffRole,
                    permissions = permissions
                )

                // Get user details for response
                val staffInfo = salonStaffManagementUseCase.getSalonStaff(salon, false, null)
                    .find { it.staff.id == staff.id }
                    ?: throw RuntimeException("Staff not found after creation")

                val response = SalonStaffResponse(staff, staffInfo)

                logger.info("Staff added successfully: ${staff.id.value}")
                return ResponseEntity.status(HttpStatus.CREATED)
                    .body(ApiResponse.success(response))
            } else {
                // User doesn't exist - send invitation
                logger.info("User doesn't exist, sending invitation to: ${request.phoneNumber}")

                val invitationMessage = request.notes ?: "Ați fost invitat să vă alăturați echipei noastre!"

                val command = SendSalonInvitationCommand(
                    salonId = salon,
                    inviterUserId = currentUser.userId,
                    invitedUserPhone = request.phoneNumber,
                    proposedRole = staffRole,
                    proposedPermissions = permissions,
                    message = invitationMessage
                )

                val invitation = invitationManagementUseCase.sendInvitation(command)

                logger.info("Invitation sent successfully: ${invitation.id.value}")

                // Return a different response indicating invitation was sent
                return ResponseEntity.status(HttpStatus.ACCEPTED)
                    .body(ApiResponse.success(
                        mapOf(
                            "type" to "invitation_sent",
                            "invitationId" to invitation.id.value,
                            "phoneNumber" to invitation.invitedUserPhone,
                            "proposedRole" to invitation.proposedRole.name,
                            "message" to "Invitația a fost trimisă cu succes",
                            "expiresAt" to invitation.expiresAt.toString()
                        )
                    ))
            }



        } catch (e: IllegalArgumentException) {
            logger.warn("Invalid request: ${e.message}")
            ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error(e.message ?: "Cerere invalidă"))
        } catch (e: Exception) {
            logger.error("Error adding staff to salon", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Eroare la adăugarea angajatului: ${e.message}"))
        }
    }

    /**
     * PUT /api/salons/{salonId}/staff/{userId}
     * Update staff role and permissions
     */
    @PutMapping("/{staffId}")
    @Operation(summary = "Update staff", description = "Update staff role and permissions")
    @SwaggerApiResponse(responseCode = "200", description = "Staff updated successfully")
    @SwaggerApiResponse(responseCode = "400", description = "Invalid request data")
    @SwaggerApiResponse(responseCode = "401", description = AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR)
    @SwaggerApiResponse(responseCode = "403", description = "Access denied")
    @SwaggerApiResponse(responseCode = "404", description = "Staff not found")
    fun updateStaff(
        @PathVariable salonId: String,
        @PathVariable staffId: String,
        @Valid @RequestBody request: UpdateStaffRequest
    ): ResponseEntity<ApiResponse<SalonStaffResponse>> {
        logger.info("=== UPDATE STAFF REQUEST ===")
        logger.info("Salon ID: $salonId")
        logger.info("User ID: $staffId")
        logger.info("New Role: ${request.groomerRole}")
        logger.info("Client Data Permission: ${request.clientDataPermission}")
        logger.info("Notes: ${request.notes}")

        return try {
            val currentUser = SecurityUtils.getCurrentUser()
                ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            val salon = SalonId.of(salonId)

            // Authorization: Only chief groomers or admins can update staff
            if (!currentUser.isChiefGroomerInSalon(salon) && !currentUser.isAdmin()) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error("Nu aveți permisiunea să modificați angajații din acest salon"))
            }

            val staffRole = try {
                StaffRole.valueOf(request.groomerRole.uppercase())
            } catch (e: IllegalArgumentException) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Rolul specificat nu este valid: ${request.groomerRole}"))
            }

            val permissions = StaffPermissions.fromClientDataPermission(request.clientDataPermission)

            val updatedStaff = salonStaffManagementUseCase.updateStaffRole(
                salonId = salon,
                userId = UserId.of(staffId),
                role = staffRole,
                permissions = permissions,
                nickName = request.nickname
            )

            // Get user details for response
            val staffInfo = salonStaffManagementUseCase.getSalonStaff(salon, false, null)
                .find { it.staff.id == updatedStaff.id }
                ?: throw RuntimeException("Staff not found after update")

            val response = SalonStaffResponse(updatedStaff, staffInfo)

            logger.info("Staff updated successfully: ${updatedStaff.id.value}")
            ResponseEntity.ok(ApiResponse.success(response))

        } catch (e: IllegalArgumentException) {
            logger.warn("Invalid request: ${e.message}")
            ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error(e.message ?: "Cerere invalidă"))
        } catch (e: Exception) {
            logger.error("Error updating staff", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Eroare la actualizarea angajatului: ${e.message}"))
        }
    }

    /**
     * PATCH /api/salons/{salonId}/staff/{staffId}/toggle-status
     * Toggle staff status (active/inactive) within a salon
     */
    @PatchMapping("/{staffId}/toggle-status")
    @Operation(summary = "Toggle staff status", description = "Toggle staff status (active/inactive) within a salon")
    @SwaggerApiResponse(responseCode = "200", description = "Staff status toggled successfully")
    @SwaggerApiResponse(responseCode = "401", description = AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR)
    @SwaggerApiResponse(responseCode = "403", description = "Access denied")
    @SwaggerApiResponse(responseCode = "404", description = "Staff not found")
    fun toggleStaffStatus(
        @PathVariable salonId: String,
        @PathVariable staffId: String
    ): ResponseEntity<ApiResponse<SalonStaffResponse>> {
        logger.info("=== TOGGLE STAFF STATUS REQUEST ===")
        logger.info("Salon ID: $salonId")
        logger.info("Staff ID: $staffId")

        return try {
            val currentUser = SecurityUtils.getCurrentUser()
                ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            logger.info("Current user: ${currentUser.userId.value}")

            val salon = SalonId.of(salonId)
            val targetStaffId = StaffId.of(staffId)

            // Authorization: Only chief groomers or admins can toggle staff status
            if (!currentUser.isChiefGroomerInSalon(salon) && !currentUser.isAdmin()) {
                logger.warn("User ${currentUser.userId.value} denied permission to toggle staff status in salon $salonId")
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error("Nu aveți permisiunea să modificați statusul angajaților din acest salon"))
            }

            // Toggle staff status using the new staffId-based method
            val updatedStaff = salonStaffManagementUseCase.toggleStaffStatusByStaffId(
                salonId = salon,
                staffId = targetStaffId
            )

            // Get user details for response
            val staffInfo = salonStaffManagementUseCase.getSalonStaff(salon, false, null)
                .find { it.staff.id == updatedStaff.id }
                ?: throw RuntimeException("Staff not found after status toggle")

            val response = SalonStaffResponse(updatedStaff, staffInfo)

            logger.info("Staff status toggled successfully: ${updatedStaff.id.value}")
            logger.info("New status: ${if (updatedStaff.isActive) "Active" else "Inactive"}")
            ResponseEntity.ok(ApiResponse.success(response))

        } catch (e: IllegalArgumentException) {
            logger.warn("Staff not found: ${e.message}")
            ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(ApiResponse.error(e.message ?: "Angajatul nu a fost găsit"))
        } catch (e: Exception) {
            logger.error("Error toggling staff status", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Eroare la modificarea statusului angajatului: ${e.message}"))
        }
    }
}
