package ro.animaliaprogramari.animalia.adapter.inbound.rest.dto

import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.Size
import java.time.LocalDateTime

/**
 * Request DTO for adding a client to a salon
 * Supports both creating new clients and adding existing clients
 */
@Schema(description = "Request to add a client to a salon")
data class AddClientToSalonRequest(
    @JsonProperty("id")
    val id: String? = null,

    @field:NotBlank(message = "Name is required")
    @field:Size(max = 255, message = "Name must not exceed 255 characters")
    @JsonProperty("name")
    val name: String,

    @field:Size(max = 50, message = "Phone must not exceed 50 characters")
    @JsonProperty("phone")
    val phone: String? = null,

    @field:Size(max = 255, message = "Email must not exceed 255 characters")
    @JsonProperty("email")
    val email: String? = null,

    @JsonProperty("address")
    val address: String? = null,

    @JsonProperty("notes")
    val notes: String? = null,

    @JsonProperty("registrationDate")
    val registrationDate: LocalDateTime? = null,

    @JsonProperty("petIds")
    val petIds: List<String>? = null,

    @JsonProperty("isActive")
    val isActive: Boolean? = null,

    @JsonProperty("petCount")
    val petCount: Int? = null,

    @JsonProperty("updatedAt")
    val updatedAt: LocalDateTime? = null
) {
    /**
     * Check if this is a request to create a new client
     */
    fun isNewClient(): Boolean = id.isNullOrBlank()

    /**
     * Validate that either phone or email is provided for new clients
     */
    fun hasContactInfo(): Boolean = !phone.isNullOrBlank() || !email.isNullOrBlank()
}

/**
 * Response DTO for adding a client to a salon
 */
data class AddClientToSalonResponse(
    @JsonProperty("success")
    val success: Boolean,

    @JsonProperty("salonId")
    val salonId: String,

    @JsonProperty("clientId")
    val clientId: String,

    @JsonProperty("message")
    val message: String,

    @JsonProperty("clientCount")
    val clientCount: Int
)

/**
 * Response DTO for removing a client from a salon
 */
data class RemoveClientFromSalonResponse(
    @JsonProperty("success")
    val success: Boolean,

    @JsonProperty("salonId")
    val salonId: String,

    @JsonProperty("clientId")
    val clientId: String,

    @JsonProperty("message")
    val message: String,

    @JsonProperty("clientCount")
    val clientCount: Int
)
