package ro.animaliaprogramari.animalia.adapter.inbound.rest

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.responses.ApiResponse as SwaggerApiResponse
import io.swagger.v3.oas.annotations.tags.Tag
import io.swagger.v3.oas.annotations.media.Schema
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.ApiResponse

@RestController
@RequestMapping("/auth")
@Tag(name = "SMS Verification", description = "Operations for verifying phone numbers")
class SmsVerificationController {

    @PostMapping("/send-sms-verification")
    @Operation(summary = "Send SMS verification", description = "Send a verification code via SMS")
    @SwaggerApiResponse(responseCode = "200", description = "Verification code sent")
    fun sendSmsVerification(@RequestBody request: SendSmsRequest): ApiResponse<Map<String, Any>> {
        val response = mapOf(
            "message" to "SMS verification code sent successfully",
            "expiresIn" to 300,
            "phoneNumber" to request.phoneNumber,
            "canResendAfter" to 60
        )
        
        return ApiResponse.success(response)
    }

    @PostMapping("/verify-sms")
    @Operation(summary = "Verify SMS code", description = "Verify a phone number using the received code")
    @SwaggerApiResponse(responseCode = "200", description = "Phone number verified")
    fun verifySms(@RequestBody request: VerifySmsRequest): ApiResponse<Map<String, Any>> {
        val response = mapOf(
            "verified" to true,
            "message" to "Phone number verified successfully",
            "phoneNumber" to request.phoneNumber
        )
        
        return ApiResponse.success(response)
    }
}


@Schema(description = "Request to send a verification SMS")
data class SendSmsRequest(
    @Schema(description = "Phone number to verify")
    val phoneNumber: String
)

@Schema(description = "Request to verify an SMS code")
data class VerifySmsRequest(
    @Schema(description = "Phone number that received the code")
    val phoneNumber: String,
    @Schema(description = "Verification code")
    val code: String
)
