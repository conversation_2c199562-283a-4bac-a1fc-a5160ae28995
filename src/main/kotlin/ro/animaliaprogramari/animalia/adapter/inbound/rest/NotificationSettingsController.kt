package ro.animaliaprogramari.animalia.adapter.inbound.rest

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.responses.ApiResponse as SwaggerApiResponse
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.validation.Valid
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.*
import ro.animaliaprogramari.animalia.adapter.inbound.rest.mapper.NotificationSettingsDtoMapper
import ro.animaliaprogramari.animalia.adapter.inbound.security.SecurityUtils
import ro.animaliaprogramari.animalia.application.port.inbound.NotificationSettingsManagementUseCase
import ro.animaliaprogramari.animalia.application.query.GetNotificationSettingsQuery
import ro.animaliaprogramari.animalia.config.AnimaliaConstants
import ro.animaliaprogramari.animalia.domain.model.SalonId

/**
 * REST controller for notification settings management operations
 * This is an inbound adapter that translates HTTP requests to use case calls
 */
@RestController
@RequestMapping("/salons")
@CrossOrigin(origins = ["*"])
@Tag(name = "Notification Settings", description = "Operations for managing notification preferences")
class NotificationSettingsController(
    private val notificationSettingsManagementUseCase: NotificationSettingsManagementUseCase,
    private val dtoMapper: NotificationSettingsDtoMapper
) {

    private val logger = LoggerFactory.getLogger(NotificationSettingsController::class.java)

    /**
     * GET /api/salons/{salonId}/notification-settings
     * Get notification settings for a salon
     */
    @GetMapping("/{salonId}/notification-settings")
    @Operation(
        summary = "Get notification settings",
        description = "Retrieve notification settings for a specific salon"
    )
    @SwaggerApiResponse(responseCode = "200", description = "Notification settings retrieved successfully")
    @SwaggerApiResponse(responseCode = "401", description = AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR)
    @SwaggerApiResponse(responseCode = "403", description = "Access denied")
    fun getNotificationSettings(@PathVariable salonId: String): ResponseEntity<ApiResponse<NotificationSettingsResponse>> {
        logger.debug("REST request to get notification settings for salon: $salonId")

        return try {
            val currentUser = SecurityUtils.getCurrentUser()
                ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            val salon = SalonId.of(salonId)

            // Authorization: Only chief groomers can view notification settings
            if (!currentUser.isChiefGroomerInSalon(salon)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error("Nu aveți permisiunea să accesați setările de notificare ale acestui salon"))
            }

            val query = GetNotificationSettingsQuery(salonId = salon)
            val settings = notificationSettingsManagementUseCase.getNotificationSettings(query)
            val response = dtoMapper.toResponse(settings)

            ResponseEntity.ok(ApiResponse.success(response))

        } catch (e: IllegalArgumentException) {
            logger.warn("Invalid salon ID format: $salonId", e)
            ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error("ID-ul salonului nu este valid"))
        } catch (e: Exception) {
            logger.error("Error getting notification settings for salon: $salonId", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Eroare la obținerea setărilor de notificare"))
        }
    }

    /**
     * PUT /api/salons/{salonId}/notification-settings
     * Update notification settings for a salon
     */
    @PutMapping("/{salonId}/notification-settings")
    @Operation(
        summary = "Update notification settings",
        description = "Modify notification settings for a specific salon"
    )
    @SwaggerApiResponse(responseCode = "200", description = "Notification settings updated successfully")
    @SwaggerApiResponse(responseCode = "401", description = AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR)
    @SwaggerApiResponse(responseCode = "403", description = "Access denied")
    @SwaggerApiResponse(responseCode = "400", description = "Invalid request data")
    fun updateNotificationSettings(
        @PathVariable salonId: String,
        @Valid @RequestBody request: UpdateNotificationSettingsRequest
    ): ResponseEntity<ApiResponse<NotificationSettingsResponse>> {
        logger.debug("REST request to update notification settings for salon: $salonId")

        return try {
            val currentUser = SecurityUtils.getCurrentUser()
                ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            val salon = SalonId.of(salonId)

            // Authorization: Only chief groomers can modify notification settings
            if (!currentUser.isChiefGroomerInSalon(salon)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error("Nu aveți permisiunea să modificați setările de notificare ale acestui salon"))
            }

            // Validate sound preference
            try {
                ro.animaliaprogramari.animalia.domain.model.SoundPreference.fromString(request.soundPreference)
            } catch (e: IllegalArgumentException) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Preferința de sunet nu este validă: ${request.soundPreference}"))
            }

            // Validate notification priority
            try {
                ro.animaliaprogramari.animalia.domain.model.NotificationPriority.fromString(request.notificationRules.defaultPriority)
            } catch (e: IllegalArgumentException) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Prioritatea de notificare nu este validă: ${request.notificationRules.defaultPriority}"))
            }

            val command = dtoMapper.toCommand(salon, request)
            val updatedSettings = notificationSettingsManagementUseCase.updateNotificationSettings(command)
            val response = dtoMapper.toResponse(updatedSettings)

            ResponseEntity.ok(ApiResponse.success(response))

        } catch (e: IllegalArgumentException) {
            logger.warn("Invalid request data for salon: $salonId", e)
            ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error(e.message ?: "Datele cererii nu sunt valide"))
        } catch (e: Exception) {
            logger.error("Error updating notification settings for salon: $salonId", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Eroare la actualizarea setărilor de notificare"))
        }
    }
}
