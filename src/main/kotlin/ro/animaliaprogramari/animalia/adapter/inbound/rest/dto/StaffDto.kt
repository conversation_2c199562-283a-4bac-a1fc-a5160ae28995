package ro.animaliaprogramari.animalia.adapter.inbound.rest.dto

import com.fasterxml.jackson.annotation.JsonFormat
import com.fasterxml.jackson.annotation.JsonInclude
import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDate
import java.time.LocalTime

@JsonInclude(JsonInclude.Include.NON_NULL)
data class StaffResponse(
    @Schema(description = "Staff ID", example = "123e4567-e89b-12d3-a456-426614174000")
    val id: String,

    @Schema(description = "User ID", example = "123e4567-e89b-12d3-a456-426614174001")
    val userId: String,

    @Schema(description = "Salon ID", example = "123e4567-e89b-12d3-a456-426614174002")
    val salonId: String,

    @Schema(description = "Staff name", example = "<PERSON>")
    val name: String,

    @Schema(description = "Staff role", example = "GROOMER")
    val role: String,

    @Schema(description = "Specializations", example = "[\"DOG_GROOMING\", \"CAT_GROOMING\"]")
    val specializations: List<String>,

    @Schema(description = "Working hours")
    val workingHours: WorkingHoursDto,

    @Schema(description = "Hire date", example = "2022-01-15")
    @JsonFormat(pattern = "yyyy-MM-dd")
    val hireDate: LocalDate,

    @Schema(description = "Is active", example = "true")
    val isActive: Boolean,

    @Schema(description = "Performance metrics")
    val performance: StaffPerformanceDto? = null
)

data class WorkingHoursDto(
    val regularHours: Map<String, DayScheduleDto>,
    val exceptions: Map<String, DayScheduleDto?>,
    val holidays: List<String>
)

data class DayScheduleDto(
    @JsonFormat(pattern = "HH:mm")
    val startTime: LocalTime,

    @JsonFormat(pattern = "HH:mm")
    val endTime: LocalTime,

    val lunchBreak: TimeSlotDto?
)

data class TimeSlotDto(
    @JsonFormat(pattern = "HH:mm")
    val startTime: LocalTime,

    @JsonFormat(pattern = "HH:mm")
    val endTime: LocalTime
)

data class StaffPerformanceDto(
    val averageRating: Double,
    val completionRate: Double,
    val clientRetentionRate: Double,
    val appointmentsPerDay: Double,
    val revenueGenerated: Double,
    val clientSatisfactionScore: Double
)
