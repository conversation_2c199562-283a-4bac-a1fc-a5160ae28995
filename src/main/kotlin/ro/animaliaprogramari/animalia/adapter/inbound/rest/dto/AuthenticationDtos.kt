package ro.animaliaprogramari.animalia.adapter.inbound.rest.dto

import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.v3.oas.annotations.media.Schema

/**
 * Request DTO for Firebase authentication
 */
@Schema(description = "Firebase authentication request")
data class FirebaseAuthRequest(
    @Schema(description = "Firebase token")
    @JsonProperty("firebaseToken")
    val firebaseToken: String,

    @Schema(description = "Client platform")
    @JsonProperty("platform")
    val platform: String? = null,

    @Schema(description = "App version")
    @JsonProperty("appVersion")
    val appVersion: String? = null
)

/**
 * Request DTO for token refresh
 */
@Schema(description = "JWT refresh request")
data class RefreshTokenRequest(
    @Schema(description = "Refresh token")
    @JsonProperty("refreshToken")
    val refreshToken: String
)

/**
 * Response DTO for authentication
 */
@Schema(description = "Authentication response wrapper")
data class AuthenticationResponse(
    @Schema(description = "Whether authentication succeeded")
    @JsonProperty("success")
    val success: <PERSON><PERSON><PERSON>,

    @Schema(description = "Authentication data")
    @JsonProperty("data")
    val data: AuthenticationData? = null,

    @Schema(description = "Error message if authentication failed")
    @JsonProperty("error")
    val error: String? = null
)

/**
 * Authentication data DTO
 */
@Schema(description = "Authentication details")
data class AuthenticationData(
    @Schema(description = "JWT access token")
    @JsonProperty("accessToken")
    val accessToken: String,

    @Schema(description = "Authenticated user ID")
    @JsonProperty("userId")
    val userId: String,

    @Schema(description = "User phone number")
    @JsonProperty("userPhone")
    val userPhone: String?,

    @Schema(description = "User display name")
    @JsonProperty("userName")
    val userName: String,

    @Schema(description = "User role")
    @JsonProperty("userRole")
    val userRole: String,

    @Schema(description = "Active salon ID")
    @JsonProperty("salonId")
    val salonId: String?,

    @Schema(description = "Granted groomer permissions")
    @JsonProperty("groomerPermissions")
    val groomerPermissions: GroomerPermissionsDto?,

    @Schema(description = "Token expiration time in seconds")
    @JsonProperty("expiresIn")
    val expiresIn: Long,

    @Schema(description = "Whether account is active")
    @JsonProperty("isActive")
    val isActive: Boolean
)

/**
 * Groomer permissions DTO
 */
@Schema(description = "Permissions granted to a groomer")
data class GroomerPermissionsDto(
    @Schema(description = "Groomer role")
    @JsonProperty("groomerRole")
    val groomerRole: String,

    @Schema(description = "Client data access level")
    @JsonProperty("clientDataPermission")
    val clientDataPermission: String
)

/**
 * User profile DTO
 */
@Schema(description = "User profile data")
data class UserProfileDto(
    @Schema(description = "User ID")
    @JsonProperty("id")
    val id: String,

    @Schema(description = "Firebase UID")
    @JsonProperty("firebaseUid")
    val firebaseUid: String,

    @Schema(description = "User email address")
    @JsonProperty("email")
    val email: String,

    @Schema(description = "User full name")
    @JsonProperty("name")
    val name: String,

    @Schema(description = "User role")
    @JsonProperty("role")
    val role: String,

    @Schema(description = "Whether the account is active")
    @JsonProperty("isActive")
    val isActive: Boolean,

    @Schema(description = "Creation timestamp")
    @JsonProperty("createdAt")
    val createdAt: String,

    @Schema(description = "Last update timestamp")
    @JsonProperty("updatedAt")
    val updatedAt: String
)

/**
 * Request DTO for updating user name
 * Validation is now handled by ValidationService
 */
@Schema(description = "Request to update user name")
data class UpdateUserNameRequest(
    @Schema(description = "New display name")
    @JsonProperty("name")
    val name: String
)

/**
 * Token response DTO
 */
@Schema(description = "Authentication tokens")
data class TokenResponse(
    @Schema(description = "Access token")
    @JsonProperty("accessToken")
    val accessToken: String,

    @Schema(description = "Refresh token")
    @JsonProperty("refreshToken")
    val refreshToken: String,

    @Schema(description = "Token expiration in seconds")
    @JsonProperty("expiresIn")
    val expiresIn: Long
)

/**
 * Request DTO for deleting user account
 * Requires confirmation text "confirm" to proceed with account deletion
 */
@Schema(description = "Request to delete the current account")
data class DeleteAccountRequest(
    @Schema(description = "Confirmation text, must be 'confirm'")
    @JsonProperty("confirmationText")
    val confirmationText: String
)


