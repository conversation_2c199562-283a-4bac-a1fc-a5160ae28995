package ro.animaliaprogramari.animalia.adapter.inbound.rest.dto

import com.fasterxml.jackson.annotation.JsonInclude
import io.swagger.v3.oas.annotations.media.Schema

@JsonInclude(JsonInclude.Include.NON_NULL)
data class ApiResponse<T>(
    @Schema(description = "Success status", example = "true")
    val success: <PERSON><PERSON><PERSON>,

    @Schema(description = "Response data")
    val data: T? = null,

    @Schema(description = "Success or error message")
    val message: String? = null,

    @Schema(description = "Error message if request failed", example = "Resource not found")
    val error: String? = null,

    @Schema(description = "Error code if request failed", example = "NOT_FOUND")
    val errorCode: String? = null,

    @Schema(description = "Additional details for the response")
    val details: Any? = null
) {
    companion object {
        fun <T> success(data: T): ApiResponse<T> {
            return ApiResponse(success = true, data = data)
        }

        fun <T> success(data: T, message: String): ApiResponse<T> {
            return ApiResponse(success = true, data = data, message = message)
        }

        fun <T> error(message: String, code: String? = null, details: Any? = null): ApiResponse<T> {
            return ApiResponse(success = false, error = message, errorCode = code, details = details)
        }
    }
}