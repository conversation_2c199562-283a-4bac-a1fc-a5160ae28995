package ro.animaliaprogramari.animalia.adapter.inbound.rest.mapper

import org.springframework.stereotype.Component
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.*
import ro.animaliaprogramari.animalia.application.command.UpdateWorkingHoursCommand
import ro.animaliaprogramari.animalia.domain.model.*
import java.time.DayOfWeek
import java.time.LocalTime
import java.time.format.DateTimeFormatter

/**
 * Mapper for converting between working hours domain models and DTOs
 */
@Component
class WorkingHoursDtoMapper {

    private val timeFormatter = DateTimeFormatter.ofPattern("HH:mm")

    /**
     * Convert domain model to response DTO
     */
    fun toResponse(workingHours: WorkingHoursSettings): WorkingHoursResponse {
        val weeklyScheduleResponse = workingHours.weeklySchedule.mapKeys { (dayOfWeek, _) ->
            dayOfWeek.name.lowercase()
        }.mapValues { (_, daySchedule) ->
            toDayScheduleResponse(daySchedule)
        }

        val holidaysResponse = workingHours.holidays.map { holiday ->
            HolidayResponse(
                name = holiday.name,
                date = holiday.date,
                isWorkingDay = holiday.isWorkingDay,
                type = holiday.type.name
            )
        }

        val customClosuresResponse = workingHours.customClosures.map { closure ->
            CustomClosureResponse(
                reason = closure.reason,
                date = closure.date,
                description = closure.description
            )
        }

        return WorkingHoursResponse(
            salonId = workingHours.salonId.value,
            weeklySchedule = weeklyScheduleResponse,
            holidays = holidaysResponse,
            customClosures = customClosuresResponse,
            updatedAt = workingHours.updatedAt
        )
    }

    /**
     * Convert request DTO to domain command parameters
     */
    fun toUpdateCommand(
        salonId: SalonId,
        updaterUserId: UserId,
        request: UpdateWorkingHoursRequest
    ): UpdateWorkingHoursCommand {
        val weeklySchedule = request.weeklySchedule.mapKeys { (dayName, _) ->
            DayOfWeek.valueOf(dayName.uppercase())
        }.mapValues { (_, dayScheduleRequest) ->
            toDayScheduleDomain(dayScheduleRequest)
        }

        val holidays = request.holidays.map { holidayRequest ->
            Holiday.create(
                salonId = salonId,
                name = holidayRequest.name,
                date = holidayRequest.date,
                isWorkingDay = holidayRequest.isWorkingDay,
                type = HolidayType.fromString(holidayRequest.type)
            )
        }

        val customClosures = request.customClosures.map { closureRequest ->
            CustomClosure.create(
                salonId = salonId,
                reason = closureRequest.reason,
                date = closureRequest.date,
                description = closureRequest.description
            )
        }

        return UpdateWorkingHoursCommand(
            salonId = salonId,
            updaterUserId = updaterUserId,
            weeklySchedule = weeklySchedule,
            holidays = holidays,
            customClosures = customClosures
        )
    }

    /**
     * Convert domain day schedule to response DTO
     */
    private fun toDayScheduleResponse(daySchedule: DaySchedule): DayScheduleResponse {
        return DayScheduleResponse(
            startTime = daySchedule.startTime?.format(timeFormatter),
            endTime = daySchedule.endTime?.format(timeFormatter),
            isWorkingDay = daySchedule.isWorkingDay,
            breakStart = daySchedule.breakStart?.format(timeFormatter),
            breakEnd = daySchedule.breakEnd?.format(timeFormatter)
        )
    }

    /**
     * Convert request DTO to domain day schedule
     */
    private fun toDayScheduleDomain(dayScheduleRequest: DayScheduleRequest): DaySchedule {
        val startTime = dayScheduleRequest.startTime?.let { LocalTime.parse(it, timeFormatter) }
        val endTime = dayScheduleRequest.endTime?.let { LocalTime.parse(it, timeFormatter) }
        val breakStart = dayScheduleRequest.breakStart?.let { LocalTime.parse(it, timeFormatter) }
        val breakEnd = dayScheduleRequest.breakEnd?.let { LocalTime.parse(it, timeFormatter) }

        return if (dayScheduleRequest.isWorkingDay) {
            DaySchedule.workingDay(startTime!!, endTime!!, breakStart, breakEnd)
        } else {
            DaySchedule.dayOff()
        }
    }
}
