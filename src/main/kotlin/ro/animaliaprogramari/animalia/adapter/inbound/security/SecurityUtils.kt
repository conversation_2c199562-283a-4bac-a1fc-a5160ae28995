package ro.animaliaprogramari.animalia.adapter.inbound.security

import org.springframework.security.core.context.SecurityContextHolder
import ro.animaliaprogramari.animalia.domain.model.AuthenticatedUser

/**
 * Utility class for accessing security context
 */
object SecurityUtils {

    /**
     * Get the currently authenticated user from Spring Security context
     */
    fun getCurrentUser(): AuthenticatedUser? {
        val logger = org.slf4j.LoggerFactory.getLogger(SecurityUtils::class.java)

        logger.info("=== SECURITY UTILS GET CURRENT USER ===")
        val authentication = SecurityContextHolder.getContext().authentication

        logger.info("Authentication object: ${authentication != null}")
        logger.info("Authentication class: ${authentication?.javaClass?.simpleName}")
        logger.info("Authentication principal class: ${authentication?.principal?.javaClass?.simpleName}")
        logger.info("Is authenticated: ${authentication?.isAuthenticated}")

        return if (authentication?.principal is AuthenticatedUser) {
            val user = authentication.principal as AuthenticatedUser
            logger.info("Found authenticated user: ${user.userId.value}")
            user
        } else {
            logger.error("No authenticated user found in security context")
            logger.error("Principal type: ${authentication?.principal?.javaClass?.simpleName}")
            null
        }
    }

    /**
     * Check if current user is authenticated
     */
    fun isAuthenticated(): Boolean {
        val authentication = SecurityContextHolder.getContext().authentication
        return authentication != null && authentication.isAuthenticated && authentication.principal is AuthenticatedUser
    }

    /**
     * Check if current user has admin role
     */
    fun isAdmin(): Boolean {
        return getCurrentUser()?.isAdmin() ?: false
    }

    /**
     * Check if current user has groomer role
     */
    fun isGroomer(): Boolean {
        return getCurrentUser()?.isGroomer() ?: false
    }

    /**
     * Check if current user is a regular user
     */
    fun isRegularUser(): Boolean {
        return getCurrentUser()?.isRegularUser() ?: false
    }

    /**
     * Get current user ID
     */
    fun getCurrentUserId(): String? {
        return getCurrentUser()?.userId?.value
    }

    /**
     * Get current user Firebase UID
     */
    fun getCurrentUserFirebaseUid(): String? {
        return getCurrentUser()?.firebaseUid
    }
}
