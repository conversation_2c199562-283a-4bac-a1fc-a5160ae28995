package ro.animaliaprogramari.animalia.adapter.inbound.rest.dto

import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDateTime

/**
 * Response DTO for client statistics
 */
@Schema(description = "Aggregated statistics for a client")
data class ClientStatisticsResponse(
    @JsonProperty("totalAppointments")
    val totalAppointments: Int,
    
    @JsonProperty("completedAppointments")
    val completedAppointments: Int,
    
    @JsonProperty("cancelledAppointments")
    val cancelledAppointments: Int,
    
    @JsonProperty("noShowAppointments")
    val noShowAppointments: Int,
    
    @JsonProperty("upcomingAppointments")
    val upcomingAppointments: Int,
    
    @JsonProperty("lastVisitDate")
    val lastVisitDate: LocalDateTime?,
    
    @JsonProperty("totalRevenue")
    val totalRevenue: Double,
    
    @JsonProperty("averageAppointmentValue")
    val averageAppointmentValue: Double,
    
    @JsonProperty("totalPets")
    val totalPets: Int,
    
    @JsonProperty("loyaltyScore")
    val loyaltyScore: Double,
    
    @JsonProperty("favoriteServices")
    val favoriteServices: List<String>,
    
    @JsonProperty("averageRating")
    val averageRating: Double?,
    
    @JsonProperty("totalReviews")
    val totalReviews: Int,
    
    @JsonProperty("activeSubscriptions")
    val activeSubscriptions: Int,
    
    @JsonProperty("completionRate")
    val completionRate: Double,
    
    @JsonProperty("cancellationRate")
    val cancellationRate: Double,
    
    @JsonProperty("noShowRate")
    val noShowRate: Double,
    
    @JsonProperty("clientTier")
    val clientTier: String,
    
    @JsonProperty("isVip")
    val isVip: Boolean
)

/**
 * Response DTO for client reviews
 */
data class ReviewResponse(
    @JsonProperty("id")
    val id: String,
    
    @JsonProperty("clientId")
    val clientId: String,
    
    @JsonProperty("appointmentId")
    val appointmentId: String,
    
    @JsonProperty("petId")
    val petId: String,
    
    @JsonProperty("petName")
    val petName: String,
    
    @JsonProperty("service")
    val service: String,
    
    @JsonProperty("rating")
    val rating: Int,
    
    @JsonProperty("comment")
    val comment: String,
    
    @JsonProperty("reviewDate")
    val reviewDate: LocalDateTime,
    
    @JsonProperty("photoUrls")
    val photoUrls: List<String>,
    
    @JsonProperty("isVerified")
    val isVerified: Boolean,
    
    @JsonProperty("groomerId")
    val groomerId: String,
    
    @JsonProperty("groomerName")
    val groomerName: String
)

/**
 * Response DTO for client subscriptions
 */
data class SubscriptionResponse(
    @JsonProperty("id")
    val id: String,
    
    @JsonProperty("clientId")
    val clientId: String,
    
    @JsonProperty("name")
    val name: String,
    
    @JsonProperty("description")
    val description: String,
    
    @JsonProperty("price")
    val price: Double,
    
    @JsonProperty("frequency")
    val frequency: String,
    
    @JsonProperty("startDate")
    val startDate: LocalDateTime,
    
    @JsonProperty("endDate")
    val endDate: LocalDateTime?,
    
    @JsonProperty("isActive")
    val isActive: Boolean,
    
    @JsonProperty("sessionsIncluded")
    val sessionsIncluded: Int,
    
    @JsonProperty("sessionsUsed")
    val sessionsUsed: Int,
    
    @JsonProperty("includedServices")
    val includedServices: List<String>,
    
    @JsonProperty("notes")
    val notes: String
)