package ro.animaliaprogramari.animalia.adapter.inbound.rest.dto

import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.constraints.NotBlank
import java.time.LocalDateTime

/**
 * Response DTO for SMS reminder settings
 */
@Schema(description = "SMS reminder settings response")
data class SmsReminderSettingsResponse(
    @Schema(description = "Salon ID")
    @JsonProperty("salonId")
    val salonId: String,

    @Schema(description = "Send confirmation messages")
    @<PERSON>sonProperty("appointmentConfirmations")
    val appointmentConfirmations: <PERSON><PERSON><PERSON>,

    @Schema(description = "Send day-before reminders")
    @JsonProperty("dayBeforeReminders")
    val dayBeforeReminders: <PERSON><PERSON><PERSON>,

    @Schema(description = "Send follow-up messages")
    @JsonProperty("followUpMessages")
    val followUpMessages: <PERSON><PERSON><PERSON>,

    @Schema(description = "Selected SMS provider")
    @JsonProperty("selectedProvider")
    val selectedProvider: String,

    @Schema(description = "Last update timestamp")
    @JsonProperty("updatedAt")
    val updatedAt: LocalDateTime
)

/**
 * Request DTO for updating SMS reminder settings
 */
@Schema(description = "Update SMS reminder settings request")
data class UpdateSmsReminderSettingsRequest(
    @Schema(description = "Send confirmation messages")
    @JsonProperty("appointmentConfirmations")
    val appointmentConfirmations: Boolean,

    @Schema(description = "Send day-before reminders")
    @JsonProperty("dayBeforeReminders")
    val dayBeforeReminders: Boolean,

    @Schema(description = "Send follow-up messages")
    @JsonProperty("followUpMessages")
    val followUpMessages: Boolean,

    @field:NotBlank(message = "Selected provider is required")
    @Schema(description = "Selected SMS provider")
    @JsonProperty("selectedProvider")
    val selectedProvider: String
) {
    init {
        require(selectedProvider.isNotBlank()) { "Selected provider cannot be blank" }
        require(selectedProvider in listOf("orange", "vodafone", "telekom", "digi")) { 
            "Selected provider must be one of: orange, vodafone, telekom, digi" 
        }
    }
}
