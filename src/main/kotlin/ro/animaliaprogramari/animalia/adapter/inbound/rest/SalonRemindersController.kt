package ro.animaliaprogramari.animalia.adapter.inbound.rest

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.responses.ApiResponse as SwaggerApiResponse
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.validation.Valid
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.*
import ro.animaliaprogramari.animalia.adapter.inbound.security.SecurityUtils
import ro.animaliaprogramari.animalia.application.command.UpdateSmsReminderSettingsCommand
import ro.animaliaprogramari.animalia.application.port.inbound.SmsReminderManagementUseCase
import ro.animaliaprogramari.animalia.application.query.GetSmsReminderSettingsQuery
import ro.animaliaprogramari.animalia.config.AnimaliaConstants
import ro.animaliaprogramari.animalia.domain.model.*

/**
 * REST controller for salon SMS reminder management operations
 * This is an inbound adapter that translates HTTP requests to use case calls
 */
@RestController
@RequestMapping("/salons")
@CrossOrigin(origins = ["*"])
@Tag(name = "SMS Reminders", description = "Manage SMS reminder settings")
class SalonRemindersController(
    private val smsReminderManagementUseCase: SmsReminderManagementUseCase
) {

    private val logger = LoggerFactory.getLogger(SalonRemindersController::class.java)

    /**
     * GET /api/salons/{salonId}/sms-reminders
     * Get SMS reminder settings for a salon
     */
    @Operation(summary = "Get SMS reminder settings")
    @SwaggerApiResponse(responseCode = "200", description = "Settings retrieved")
    @SwaggerApiResponse(responseCode = "401", description = AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR)
    @SwaggerApiResponse(responseCode = "403", description = "Access denied")
    @GetMapping("/{salonId}/sms-reminders")
    fun getSmsReminderSettings(@PathVariable salonId: String): ResponseEntity<ApiResponse<SmsReminderSettingsResponse>> {
        logger.debug("REST request to get SMS reminder settings for salon: $salonId")

        return try {
            val currentUser = SecurityUtils.getCurrentUser()
                ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            val salon = SalonId.of(salonId)

            // Authorization: Only chief groomers can view SMS settings
            if (!currentUser.isChiefGroomerInSalon(salon)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error("Nu aveți permisiunea să accesați setările SMS ale acestui salon"))
            }

            val query = GetSmsReminderSettingsQuery(salon)
            val settings = smsReminderManagementUseCase.getSmsReminderSettings(query)

            val response = SmsReminderSettingsResponse(
                salonId = settings.salonId.value,
                appointmentConfirmations = settings.appointmentConfirmations,
                dayBeforeReminders = settings.dayBeforeReminders,
                followUpMessages = settings.followUpMessages,
                selectedProvider = settings.selectedProvider.displayName,
                updatedAt = settings.updatedAt
            )

            ResponseEntity.ok(ApiResponse.success(response))

        } catch (e: Exception) {
            logger.error("Error getting SMS reminder settings: ${e.message}", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Failed to get SMS reminder settings: ${e.message}"))
        }
    }

    /**
     * PUT /api/salons/{salonId}/sms-reminders
     * Update SMS reminder settings for a salon
     */
    @Operation(summary = "Update SMS reminder settings")
    @SwaggerApiResponse(responseCode = "200", description = "Settings updated")
    @SwaggerApiResponse(responseCode = "401", description = AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR)
    @SwaggerApiResponse(responseCode = "403", description = "Access denied")
    @SwaggerApiResponse(responseCode = "400", description = "Invalid request data")
    @PutMapping("/{salonId}/sms-reminders")
    fun updateSmsReminderSettings(
        @PathVariable salonId: String,
        @Valid @RequestBody request: UpdateSmsReminderSettingsRequest
    ): ResponseEntity<ApiResponse<SmsReminderSettingsResponse>> {
        logger.debug("REST request to update SMS reminder settings for salon: $salonId")

        return try {
            val currentUser = SecurityUtils.getCurrentUser()
                ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            val salon = SalonId.of(salonId)

            // Authorization: Only chief groomers can update SMS settings
            if (!currentUser.isChiefGroomerInSalon(salon)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error("Nu aveți permisiunea să modificați setările SMS ale acestui salon"))
            }

            // Parse SMS provider
            val smsProvider = try {
                SmsProvider.fromString(request.selectedProvider)
            } catch (e: IllegalArgumentException) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Furnizorul SMS nu este valid: ${request.selectedProvider}"))
            }

            val command = UpdateSmsReminderSettingsCommand(
                salonId = salon,
                appointmentConfirmations = request.appointmentConfirmations,
                dayBeforeReminders = request.dayBeforeReminders,
                followUpMessages = request.followUpMessages,
                selectedProvider = smsProvider,
                updaterUserId = currentUser.userId
            )

            val updatedSettings = smsReminderManagementUseCase.updateSmsReminderSettings(command)

            val response = SmsReminderSettingsResponse(
                salonId = updatedSettings.salonId.value,
                appointmentConfirmations = updatedSettings.appointmentConfirmations,
                dayBeforeReminders = updatedSettings.dayBeforeReminders,
                followUpMessages = updatedSettings.followUpMessages,
                selectedProvider = updatedSettings.selectedProvider.displayName,
                updatedAt = updatedSettings.updatedAt
            )

            ResponseEntity.ok(ApiResponse.success(response))

        } catch (e: Exception) {
            logger.error("Error updating SMS reminder settings: ${e.message}", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Failed to update SMS reminder settings: ${e.message}"))
        }
    }
}
