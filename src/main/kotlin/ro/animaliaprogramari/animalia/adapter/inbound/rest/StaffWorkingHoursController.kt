package ro.animaliaprogramari.animalia.adapter.inbound.rest

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.responses.ApiResponse as SwaggerApiResponse
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.validation.Valid
import org.slf4j.LoggerFactory
import org.springframework.format.annotation.DateTimeFormat
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.*
import ro.animaliaprogramari.animalia.adapter.inbound.rest.mapper.StaffWorkingHoursDtoMapper
import ro.animaliaprogramari.animalia.adapter.inbound.security.SecurityUtils
import ro.animaliaprogramari.animalia.application.port.inbound.StaffWorkingHoursManagementUseCase
import ro.animaliaprogramari.animalia.config.AnimaliaConstants
import ro.animaliaprogramari.animalia.domain.exception.BusinessRuleViolationException
import ro.animaliaprogramari.animalia.domain.exception.DomainException
import ro.animaliaprogramari.animalia.domain.exception.EntityNotFoundException
import ro.animaliaprogramari.animalia.domain.model.StaffId
import ro.animaliaprogramari.animalia.domain.model.SalonId
import java.time.LocalDate

/**
 * REST controller for staff working hours management operations
 * This is an inbound adapter that translates HTTP requests to use case calls
 */
@RestController
@RequestMapping("/salons/{salonId}/staff/{staffId}/working-hours")
@CrossOrigin(origins = ["*"])
@Tag(name = "Staff Working Hours", description = "Operations for managing staff working hours")
class StaffWorkingHoursController(
    private val staffWorkingHoursManagementUseCase: StaffWorkingHoursManagementUseCase,
    private val staffWorkingHoursDtoMapper: StaffWorkingHoursDtoMapper
) {

    private val logger = LoggerFactory.getLogger(StaffWorkingHoursController::class.java)

    /**
     * Get working hours settings for a staff member
     * Authorization: CHIEF_GROOMER or self
    */
    @GetMapping
    @Operation(summary = "Get staff working hours", description = "Retrieve working hours for a staff member")
    @SwaggerApiResponse(responseCode = "200", description = "Working hours retrieved successfully")
    @SwaggerApiResponse(responseCode = "401", description = AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR)
    @SwaggerApiResponse(responseCode = "403", description = "Access denied")
    fun getStaffWorkingHours(
        @PathVariable salonId: String,
        @PathVariable staffId: String
    ): ResponseEntity<ApiResponse<StaffWorkingHoursResponse>> {
        logger.debug("Getting working hours for staff: $staffId in salon: $salonId")

        return try {
            val currentUser = SecurityUtils.getCurrentUser()
                ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            val query = staffWorkingHoursDtoMapper.toGetWorkingHoursQuery(staffId, salonId, currentUser.userId)
            val workingHours = staffWorkingHoursManagementUseCase.getStaffWorkingHours(query)
            val response = staffWorkingHoursDtoMapper.toResponse(workingHours)

            ResponseEntity.ok(ApiResponse.success(response))

        } catch (e: BusinessRuleViolationException) {
            logger.warn("Business rule violation: ${e.message}")
            ResponseEntity.status(HttpStatus.FORBIDDEN)
                .body(ApiResponse.error(e.message ?: "Operațiunea nu este permisă"))

        } catch (e: EntityNotFoundException) {
            logger.warn("Entity not found: ${e.message}")
            ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(ApiResponse.error(e.message ?: "Angajatul nu a fost găsit"))

        } catch (e: Exception) {
            logger.error("Unexpected error getting staff working hours", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error(AnimaliaConstants.INTERNAL_SERVER_ERROR))
        }
    }

    /**
     * Update working hours settings for a staff member
     * Authorization: CHIEF_GROOMER or self
    */
    @PutMapping
    @Operation(summary = "Update staff working hours", description = "Modify working hours for a staff member")
    @SwaggerApiResponse(responseCode = "200", description = "Working hours updated successfully")
    @SwaggerApiResponse(responseCode = "401", description = AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR)
    @SwaggerApiResponse(responseCode = "403", description = "Access denied")
    @SwaggerApiResponse(responseCode = "400", description = "Invalid request data")
    fun updateStaffWorkingHours(
        @PathVariable salonId: String,
        @PathVariable staffId: String,
        @RequestBody @Valid request: UpdateStaffWorkingHoursRequest
    ): ResponseEntity<ApiResponse<StaffWorkingHoursResponse>> {
        logger.debug("Updating working hours for staff: $staffId in salon: $salonId")

        return try {
            val currentUser = SecurityUtils.getCurrentUser()
                ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            // Validate weekly schedule has all 7 days
            val validationError = validateWeeklySchedule(request.weeklySchedule)
            if (validationError != null) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error(validationError))
            }

            val command = staffWorkingHoursDtoMapper.toUpdateCommand(
                StaffId.of(staffId),
                SalonId.of(salonId),
                currentUser.userId,
                request
            )
            val updatedWorkingHours = staffWorkingHoursManagementUseCase.updateStaffWorkingHours(command)
            val response = staffWorkingHoursDtoMapper.toResponse(updatedWorkingHours)

            ResponseEntity.ok(ApiResponse.success(response))

        } catch (e: BusinessRuleViolationException) {
            logger.warn("Business rule violation: ${e.message}")
            ResponseEntity.status(HttpStatus.FORBIDDEN)
                .body(ApiResponse.error(e.message ?: "Operațiunea nu este permisă"))

        } catch (e: EntityNotFoundException) {
            logger.warn("Entity not found: ${e.message}")
            ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(ApiResponse.error(e.message ?: "Angajatul nu a fost găsit"))

        } catch (e: DomainException) {
            logger.warn("Domain error: ${e.message}")
            ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error(e.message ?: "Date invalide"))

        } catch (e: Exception) {
            logger.error("Unexpected error updating staff working hours", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error(AnimaliaConstants.INTERNAL_SERVER_ERROR))
        }
    }

    /**
     * Reset staff working hours to default values
     * Authorization: CHIEF_GROOMER only
    */
    @PostMapping("/reset")
    @Operation(summary = "Reset staff working hours", description = "Reset working hours to default values")
    @SwaggerApiResponse(responseCode = "200", description = "Working hours reset successfully")
    @SwaggerApiResponse(responseCode = "401", description = AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR)
    @SwaggerApiResponse(responseCode = "403", description = "Access denied")
    fun resetStaffWorkingHours(
        @PathVariable salonId: String,
        @PathVariable staffId: String
    ): ResponseEntity<ApiResponse<StaffWorkingHoursResponse>> {
        logger.debug("Resetting working hours for staff: $staffId in salon: $salonId")

        return try {
            val currentUser = SecurityUtils.getCurrentUser()
                ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            val resetWorkingHours = staffWorkingHoursManagementUseCase.resetStaffWorkingHours(
                staffId, salonId, currentUser.userId.value
            )
            val response = staffWorkingHoursDtoMapper.toResponse(resetWorkingHours)

            ResponseEntity.ok(ApiResponse.success(response))

        } catch (e: BusinessRuleViolationException) {
            logger.warn("Business rule violation: ${e.message}")
            ResponseEntity.status(HttpStatus.FORBIDDEN)
                .body(ApiResponse.error(e.message ?: "Operațiunea nu este permisă"))

        } catch (e: EntityNotFoundException) {
            logger.warn("Entity not found: ${e.message}")
            ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(ApiResponse.error(e.message ?: "Angajatul nu a fost găsit"))

        } catch (e: Exception) {
            logger.error("Unexpected error resetting staff working hours", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error(AnimaliaConstants.INTERNAL_SERVER_ERROR))
        }
    }

    /**
     * Validate weekly schedule has all 7 days and proper time format
     */
    private fun validateWeeklySchedule(weeklySchedule: Map<String, DayScheduleRequest>): String? {
        val requiredDays = setOf("monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday")
        val providedDays = weeklySchedule.keys.map { it.lowercase() }.toSet()

        if (providedDays != requiredDays) {
            return "Programul săptămânal trebuie să conțină toate cele 7 zile"
        }

        // Validate each day schedule
        weeklySchedule.forEach { (dayName, daySchedule) ->
            if (daySchedule.isWorkingDay) {
                // Validate time format and logic
                try {
                    val startTime = daySchedule.startTime?.let { java.time.LocalTime.parse(it) }
                    val endTime = daySchedule.endTime?.let { java.time.LocalTime.parse(it) }

                    if (startTime == null || endTime == null) {
                        return "Ora de început și ora de sfârșit sunt obligatorii pentru zilele de lucru ($dayName)"
                    }

                    if (!startTime.isBefore(endTime)) {
                        return "Ora de început trebuie să fie înainte de ora de sfârșit pentru $dayName"
                    }

                    // Validate break times if provided
                    if (daySchedule.breakStart != null && daySchedule.breakEnd != null) {
                        val breakStart = java.time.LocalTime.parse(daySchedule.breakStart)
                        val breakEnd = java.time.LocalTime.parse(daySchedule.breakEnd)

                        if (!breakStart.isBefore(breakEnd)) {
                            return "Ora de început a pauzei trebuie să fie înainte de ora de sfârșit pentru $dayName"
                        }

                        if (breakStart.isBefore(startTime) || breakEnd.isAfter(endTime)) {
                            return "Pauza trebuie să fie în intervalul de lucru pentru $dayName"
                        }
                    }

                } catch (e: Exception) {
                    return "Format invalid pentru timpul de lucru în ziua $dayName. Folosiți formatul HH:MM"
                }
            }
        }

        return null
    }
}
