package ro.animaliaprogramari.animalia.adapter.inbound.rest.mapper

import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.*
import ro.animaliaprogramari.animalia.application.port.inbound.AppointmentSummary
import ro.animaliaprogramari.animalia.application.port.inbound.AvailabilityResult
import ro.animaliaprogramari.animalia.application.port.inbound.TimeSlot
import ro.animaliaprogramari.animalia.application.port.outbound.*
import ro.animaliaprogramari.animalia.domain.model.*

/**
 * Mapper between Appointment domain model and salon-specific DTOs
 */
@Component
class SalonAppointmentDtoMapper(
    private val clientRepository: ClientRepository,
    private val petRepository: PetRepository,
    private val userRepository: UserRepository,
    private val salonServiceRepository: SalonServiceRepository,
    private val staffRepository: StaffRepository
) {

    private val logger = LoggerFactory.getLogger(SalonAppointmentDtoMapper::class.java)

    /**
     * Convert domain model to response DTO
     */
    fun toResponse(appointment: Appointment): AppointmentResponse {
        // Load related entities with graceful handling of missing data
        val client = clientRepository.findById(appointment.clientId)
        if (client == null) {
            logger.warn("Client not found for appointment {}: {}", appointment.id.value, appointment.clientId.value)
        }

        val pet = petRepository.findById(appointment.petId)
        if (pet == null) {
            logger.warn("Pet not found for appointment {}: {}", appointment.id.value, appointment.petId.value)
        }

        val staff = staffRepository.findById(appointment.staffId)
        if (staff == null) {
            logger.warn("Staff member not found for appointment {}: {}", appointment.id.value, appointment.staffId.value)
        }

        return AppointmentResponse(
            id = appointment.id.value,
            salonId = appointment.salonId.value,
            client = client?.let {
                ClientSummary(
                    id = it.id.value,
                    name = it.name,
                    phone = it.phone?.value,
                    email = it.email?.value
                )
            } ?: ClientSummary(
                id = appointment.clientId.value,
                name = "Unknown Client",
                phone = null,
                email = null
            ),
            pet = pet?.let {
                PetSummary(
                    id = it.id.value,
                    name = it.name,
                    breed = it.breed,
                    age = it.age,
                    weight = it.weight?.toDouble()
                )
            } ?: PetSummary(
                id = appointment.petId.value,
                name = "Unknown Pet",
                breed = "Unknown",
                age = null,
                weight = null
            ),
            staff = staff?.let {
                StaffSummary(
                    id = it.id.value,
                    name = it.nickname ?: "Unknown Staff",
                    role = it.role.name,
                    specialties = emptyList() // TODO: Add specialties from staff profile
                )
            } ?: StaffSummary(
                id = appointment.staffId.value,
                name = "Unknown Staff",
                role = "UNKNOWN",
                specialties = emptyList()
            ),
            appointmentDate = appointment.appointmentDate,
            startTime = appointment.startTime,
            endTime = appointment.endTime,
            services = appointment.serviceIds.map { serviceId -> createServiceSummary(appointment) },
            status = appointment.status.name,
            notes = appointment.notes,
            repetitionFrequency = appointment.repetitionFrequency?.name,
            totalPrice = appointment.totalPrice.amount,
            totalDuration = appointment.totalDuration.minutes,
            createdAt = appointment.createdAt,
            updatedAt = appointment.updatedAt
        )
    }

    /**
     * Convert multiple appointments to response DTOs
     */
    fun toResponseList(appointments: List<Appointment>): List<AppointmentResponse> {
        return appointments.map { toResponse(it) }
    }

    /**
     * Convert availability result to response DTO
     */
    fun toAvailabilityResponse(result: AvailabilityResult): AvailabilityResponse {
        return AvailabilityResponse(
            isAvailable = result.isAvailable,
            reason = result.reason,
            conflictingAppointments = result.conflictingAppointments.map { it.value },
            suggestedTimes = result.suggestedTimes.map { it.toString() }
        )
    }

    /**
     * Convert time slots to response DTOs
     */
    fun toTimeSlotResponses(timeSlots: List<TimeSlot>): List<TimeSlotResponse> {
        return timeSlots.map { timeSlot ->
            TimeSlotResponse(
                startTime = timeSlot.startTime,
                endTime = timeSlot.endTime,
                isAvailable = timeSlot.isAvailable,
                staffId = timeSlot.staffId?.value
            )
        }
    }

    /**
     * Convert appointment summary to response DTO
     */
    fun toSummaryResponse(summary: AppointmentSummary): AppointmentSummaryResponse {
        return AppointmentSummaryResponse(
            totalAppointments = summary.totalAppointments,
            completedAppointments = summary.completedAppointments,
            cancelledAppointments = summary.cancelledAppointments,
            noShowAppointments = summary.noShowAppointments,
            totalRevenue = summary.totalRevenue.amount,
            averageAppointmentDuration = summary.averageAppointmentDuration,
            busyDays = summary.busyDays,
            topServices = summary.topServices.map { (serviceId, count) ->
                val service = salonServiceRepository.findById(serviceId)
                ServiceUsageResponse(
                    serviceId = serviceId.value,
                    serviceName = service?.name ?: "Unknown Service",
                    count = count,
                    revenue = service?.basePrice?.amount?.multiply(count.toBigDecimal()) 
                        ?: java.math.BigDecimal.ZERO
                )
            },
            staffUtilization = summary.staffUtilization.mapKeys { it.key.value }
        )
    }

    /**
     * Helper method to create client summary from client entity
     */
    private fun createClientSummary(client: Client): ClientSummary {
        return ClientSummary(
            id = client.id.value,
            name = client.name,
            phone = client.phone?.value,
            email = client.email?.value
        )
    }

    /**
     * Helper method to create pet summary from pet entity
     */
    private fun createPetSummary(pet: Pet): PetSummary {
        return PetSummary(
            id = pet.id.value,
            name = pet.name,
            breed = pet.breed,
            age = pet.age,
            weight = pet.weight?.toDouble()
        )
    }

    /**
     * Helper method to create staff summary from user and association
     */
    private fun createStaffSummary(
        user: User,
        staff: Staff
    ): StaffSummary {
        return StaffSummary(
            id = staff.id.value, // ✅ FIXED: Use staffId instead of userId
            name = user.name,
            role = staff.role.name,
            specialties = staff.specializations.map { it.name } // Use staff specializations
        )
    }

    /**
     * Helper method to create service summary from appointment service
     */
    private fun createServiceSummary(appointment: Appointment): ServiceSummary {
        val serviceId = appointment.serviceIds.first()
        val service = salonServiceRepository.findById(serviceId)

        if (service == null) {
            logger.warn("Service not found for appointment {}: {}", appointment.id.value, serviceId.value)
        }

        return ServiceSummary(
            id = serviceId.value,
            name = service?.name ?: "Unknown Service",
            price = appointment.totalPrice.amount,
            duration = appointment.totalDuration.minutes
        )
    }
}
