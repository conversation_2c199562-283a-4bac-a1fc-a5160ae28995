package ro.animaliaprogramari.animalia.adapter.inbound.security

import jakarta.servlet.FilterChain
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import org.slf4j.LoggerFactory
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken
import org.springframework.security.core.authority.SimpleGrantedAuthority
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource
import org.springframework.stereotype.Component
import org.springframework.web.filter.OncePerRequestFilter
import ro.animaliaprogramari.animalia.application.port.inbound.AuthenticationUseCase

/**
 * JWT authentication filter for Spring Security
 * This is an inbound adapter that handles JWT token validation for incoming requests
 */
@Component
class JwtAuthenticationFilter(
    private val authenticationUseCase: AuthenticationUseCase
) : OncePerRequestFilter() {

    private val logger = LoggerFactory.getLogger(JwtAuthenticationFilter::class.java)

    override fun doFilterInternal(
        request: HttpServletRequest,
        response: HttpServletResponse,
        filterChain: FilterChain
    ) {
        try {
            val requestUri = request.requestURI
            val method = request.method

            logger.info("=== JWT AUTHENTICATION FILTER ===")
            logger.info("Request: $method $requestUri")

            val token = extractTokenFromRequest(request)

            if (token != null) {
                logger.info("JWT Token found in request")
                logger.info("Token length: ${token.length}")
                logger.info("Token preview: ${token.take(50)}...")
                logger.info("Token suffix: ...${token.takeLast(20)}")

                if (SecurityContextHolder.getContext().authentication == null) {
                    logger.info("No existing authentication, validating token...")
                    val authenticatedUser = authenticationUseCase.validateToken(token)

                    if (authenticatedUser != null) {
                        logger.info("=== JWT VALIDATION SUCCESS ===")
                        logger.info("User ID: ${authenticatedUser.userId.value}")
                        logger.info("User Role: ${authenticatedUser.role.name}")
                        logger.info("User Active: ${authenticatedUser.isActive}")

                        val authorities = listOf(SimpleGrantedAuthority("ROLE_${authenticatedUser.role.name}"))

                        val authentication = UsernamePasswordAuthenticationToken(
                            authenticatedUser,
                            null,
                            authorities
                        )

                        authentication.details = WebAuthenticationDetailsSource().buildDetails(request)
                        SecurityContextHolder.getContext().authentication = authentication

                        logger.info("Authentication set in SecurityContext")
                    } else {
                        logger.error("=== JWT VALIDATION FAILED ===")
                        logger.error("Invalid JWT token for request: $method $requestUri")
                    }
                } else {
                    logger.info("Authentication already exists in SecurityContext")
                }
            } else {
                logger.info("No JWT token found in request headers")
            }
        } catch (e: Exception) {
            logger.error("=== JWT AUTHENTICATION ERROR ===")
            logger.error("Exception during JWT authentication for ${request.method} ${request.requestURI}")
            logger.error("Exception type: ${e.javaClass.simpleName}")
            logger.error("Exception message: ${e.message}")
            logger.error("Stack trace: ", e)
        }

        filterChain.doFilter(request, response)
    }

    private fun extractTokenFromRequest(request: HttpServletRequest): String? {
        val bearerToken = request.getHeader("Authorization")
        return if (bearerToken != null && bearerToken.startsWith("Bearer ")) {
            bearerToken.substring(7)
        } else {
            null
        }
    }
}
