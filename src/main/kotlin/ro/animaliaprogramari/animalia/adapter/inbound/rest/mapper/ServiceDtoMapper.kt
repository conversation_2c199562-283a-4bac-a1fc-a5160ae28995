package ro.animaliaprogramari.animalia.adapter.inbound.rest.mapper

import org.springframework.stereotype.Component
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.ServiceResponse
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.CreateServiceRequest
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.UpdateServiceRequest
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.ServiceListResponse
import ro.animaliaprogramari.animalia.domain.model.*

/**
 * Mapper between GroomingService domain model and DTOs
 */
@Component
class ServiceDtoMapper {

    /**
     * Convert domain model to response DTO
     */
    fun toResponse(service: SalonService): ServiceResponse {
        return ServiceResponse(
            id = service.id.value,
            name = service.name,
            description = service.description,
            price = service.basePrice.amount.toDouble(),
            duration = service.duration.minutes,
            category = service.category.name,
            isActive = service.isActive,
            displayOrder = service.displayOrder,
            requirements = service.requirements,
            formattedPrice = service.getFormattedPrice(),
            formattedDuration = service.getFormattedDuration(),
            // Variable pricing fields
            sizePrices = service.sizePrices.takeIf { it.isNotEmpty() }?.mapValues { it.value.amount.toDouble() },
            sizeDurations = service.sizeDurations.takeIf { it.isNotEmpty() }?.mapValues { it.value.minutes },
            minPrice = service.minPrice?.amount?.toDouble(),
            maxPrice = service.maxPrice?.amount?.toDouble(),
            hasVariablePricing = service.hasVariablePricing(),
            hasMinMaxPricing = service.hasMinMaxPricing(),
            // Min-max pricing fields
            sizeMinPrices = service.sizeMinPrices?.mapValues { it.value.amount.toDouble() },
            sizeMaxPrices = service.sizeMaxPrices?.mapValues { it.value.amount.toDouble() },
            hasSizeBasedMinMaxPricing = service.hasSizeBasedMinMaxPricing(),
            createdAt = service.createdAt,
            updatedAt = service.updatedAt
        )
    }

    /**
     * Convert create request to domain parameters
     */
    fun fromCreateRequest(
        request: CreateServiceRequest,
        salonId: SalonId
    ): ServiceCreationParams {
        return ServiceCreationParams(
            salonId = salonId,
            name = request.name,
            description = request.description,
            price = Money.of(request.price.toBigDecimal()),
            duration = Duration.ofMinutes(request.duration),
            category = ServiceCategory.valueOf(request.category),
            displayOrder = request.displayOrder,
            requirements = request.requirements,
            // Variable pricing fields
            sizePrices = request.sizePrices?.mapValues { Money.of(it.value.toBigDecimal()) } ?: emptyMap(),
            sizeDurations = request.sizeDurations?.mapValues { Duration.ofMinutes(it.value) } ?: emptyMap(),
            minPrice = request.minPrice?.let { Money.of(it.toBigDecimal()) },
            maxPrice = request.maxPrice?.let { Money.of(it.toBigDecimal()) },
            sizeMinPrices = request.sizeMinPrices?.mapValues { Money.of(it.value.toBigDecimal()) },
            sizeMaxPrices = request.sizeMaxPrices?.mapValues { Money.of(it.value.toBigDecimal()) }
        )
    }

    /**
     * Convert update request to domain parameters
     */
    fun fromUpdateRequest(
        request: UpdateServiceRequest
    ): ServiceUpdateParams {
        return ServiceUpdateParams(
            name = request.name,
            description = request.description,
            price = request.price?.let { Money.of(it.toBigDecimal()) },
            duration = request.duration?.let { Duration.ofMinutes(it) },
            category = request.category?.let { ServiceCategory.valueOf(it) },
            displayOrder = request.displayOrder,
            requirements = request.requirements,
            // Variable pricing fields
            sizePrices = request.sizePrices?.mapValues { Money.of(it.value.toBigDecimal()) },
            sizeDurations = request.sizeDurations?.mapValues { Duration.ofMinutes(it.value) },
            minPrice = request.minPrice?.let { Money.of(it.toBigDecimal()) },
            maxPrice = request.maxPrice?.let { Money.of(it.toBigDecimal()) },
            sizeMinPrices = request.sizeMinPrices?.mapValues { Money.of(it.value.toBigDecimal()) },
            sizeMaxPrices = request.sizeMaxPrices?.mapValues { Money.of(it.value.toBigDecimal()) }
        )
    }

    /**
     * Convert list of services to list response with counts
     */
    fun toListResponse(services: List<SalonService>): ServiceListResponse {
        val serviceResponses = services.map { toResponse(it) }
        val activeCount = services.count { it.isActive }
        val inactiveCount = services.count { !it.isActive }
        
        return ServiceListResponse(
            services = serviceResponses,
            totalCount = services.size,
            activeCount = activeCount,
            inactiveCount = inactiveCount
        )
    }
}

/**
 * Data class for service creation parameters
 */
data class ServiceCreationParams(
    val salonId: SalonId,
    val name: String,
    val description: String?,
    val price: Money,
    val duration: Duration,
    val category: ServiceCategory,
    val displayOrder: Int,
    val requirements: List<String>,
    // Variable pricing fields
    val sizePrices: Map<String, Money> = emptyMap(),
    val sizeDurations: Map<String, Duration> = emptyMap(),
    val minPrice: Money? = null,
    val maxPrice: Money? = null,
    val sizeMinPrices: Map<String, Money>? = null,
    val sizeMaxPrices: Map<String, Money>? = null
)

/**
 * Data class for service update parameters
 */
data class ServiceUpdateParams(
    val name: String?,
    val description: String?,
    val price: Money?,
    val duration: Duration?,
    val category: ServiceCategory?,
    val displayOrder: Int?,
    val requirements: List<String>?,
    // Variable pricing fields
    val sizePrices: Map<String, Money>? = null,
    val sizeDurations: Map<String, Duration>? = null,
    val minPrice: Money? = null,
    val maxPrice: Money? = null,
    val sizeMinPrices: Map<String, Money>? = null,
    val sizeMaxPrices: Map<String, Money>? = null
)
