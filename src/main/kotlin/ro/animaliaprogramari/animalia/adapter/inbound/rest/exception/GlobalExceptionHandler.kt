package ro.animaliaprogramari.animalia.adapter.inbound.rest.exception

import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.MethodArgumentNotValidException
import org.springframework.web.bind.annotation.ControllerAdvice
import org.springframework.web.bind.annotation.ExceptionHandler
import org.springframework.web.context.request.WebRequest
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.ApiResponse
import ro.animaliaprogramari.animalia.domain.exception.*
import ro.animaliaprogramari.animalia.domain.model.*

/**
 * Global exception handler to provide consistent error responses
 * Eliminates the need for try-catch blocks in every controller method
 */
@ControllerAdvice
class GlobalExceptionHandler {

    private val logger = LoggerFactory.getLogger(GlobalExceptionHandler::class.java)

    @ExceptionHandler(BusinessRuleViolationException::class)
    fun handleBusinessRuleViolation(
        ex: BusinessRuleViolationException,
        request: WebRequest
    ): ResponseEntity<ApiResponse<Nothing>> {
        logger.warn("Business rule violation: ${ex.message}")
        
        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
            .body(ApiResponse.error(
                message = ex.message ?: "Business rule violation",
                code = "BUSINESS_RULE_VIOLATION"
            ))
    }

    @ExceptionHandler(EntityNotFoundException::class)
    fun handleEntityNotFound(
        ex: EntityNotFoundException,
        request: WebRequest
    ): ResponseEntity<ApiResponse<Nothing>> {
        logger.warn("Entity not found: ${ex.message}")
        
        return ResponseEntity.status(HttpStatus.NOT_FOUND)
            .body(ApiResponse.error(
                message = ex.message ?: "Entity not found",
                code = "ENTITY_NOT_FOUND"
            ))
    }

    @ExceptionHandler(UnauthorizedException::class)
    fun handleUnauthorized(
        ex: UnauthorizedException,
        request: WebRequest
    ): ResponseEntity<ApiResponse<Nothing>> {
        logger.warn("Unauthorized access: ${ex.message}")
        
        return ResponseEntity.status(HttpStatus.FORBIDDEN)
            .body(ApiResponse.error(
                message = ex.message ?: "Access denied",
                code = "UNAUTHORIZED"
            ))
    }

    @ExceptionHandler(TimeSlotUnavailableException::class)
    fun handleTimeSlotUnavailable(
        ex: TimeSlotUnavailableException,
        request: WebRequest
    ): ResponseEntity<ApiResponse<Nothing>> {
        logger.warn("Time slot unavailable: ${ex.message}")

        return ResponseEntity.status(HttpStatus.CONFLICT)
            .body(ApiResponse.error(
                message = ex.message ?: "Time slot is not available",
                code = "TIME_SLOT_UNAVAILABLE"
            ))
    }

    @ExceptionHandler(AppointmentSchedulingConflictException::class)
    fun handleAppointmentSchedulingConflict(
        ex: AppointmentSchedulingConflictException,
        request: WebRequest
    ): ResponseEntity<ApiResponse<Nothing>> {
        logger.warn("Appointment scheduling conflict: ${ex.message}")

        val conflictType = when {
            ex.conflicts.all { it.type == ConflictItemType.APPOINTMENT } -> "APPOINTMENT"
            ex.conflicts.all { it.type == ConflictItemType.BLOCK_TIME } -> "BLOCK_TIME"
            else -> "BOTH"
        }

        val details = mapOf(
            "conflictType" to conflictType,
            "conflictingItems" to ex.conflicts.map {
                mapOf(
                    "id" to it.id,
                    "staffId" to it.staffId.value,
                    "staffName" to it.staffName,
                    "type" to it.type.name,
                    "date" to it.date,
                    "startTime" to it.startTime,
                    "endTime" to it.endTime
                )
            },
            "alternatives" to ex.alternatives.map { alt ->
                mapOf(
                    "type" to alt.type.name,
                    "priority" to alt.priority,
                    "suggestion" to mapOf(
                        "date" to alt.suggestion.date,
                        "startTime" to alt.suggestion.startTime,
                        "endTime" to alt.suggestion.endTime,
                        "staffId" to alt.suggestion.staffId,
                        "staffName" to alt.suggestion.staffName
                    ),
                    "reason" to alt.reason,
                    "confidence" to alt.confidence
                )
            }
        )

        val body = ResponseEntity.status(HttpStatus.CONFLICT)
            .body<ApiResponse<Nothing>>(
                ApiResponse.error(
                    message = ex.message ?: "Scheduling conflict",
                    code = "SCHEDULING_CONFLICT",
                    details = details
                )
            )
        println("DEBUG: Response body: $body")
        return body
    }

    @ExceptionHandler(AppointmentStateException::class)
    fun handleAppointmentState(
        ex: AppointmentStateException,
        request: WebRequest
    ): ResponseEntity<ApiResponse<Nothing>> {
        logger.warn("Invalid appointment state: ${ex.message}")
        
        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
            .body(ApiResponse.error(
                message = ex.message ?: "Invalid appointment state",
                code = "INVALID_APPOINTMENT_STATE"
            ))
    }

    @ExceptionHandler(StaffUnavailableException::class)
    fun handleStaffUnavailable(
        ex: StaffUnavailableException,
        request: WebRequest
    ): ResponseEntity<ApiResponse<Nothing>> {
        logger.warn("Staff unavailable: ${ex.message}")
        
        return ResponseEntity.status(HttpStatus.CONFLICT)
            .body(ApiResponse.error(
                message = ex.message ?: "Staff member is not available",
                code = "STAFF_UNAVAILABLE"
            ))
    }

    @ExceptionHandler(ClientOperationException::class)
    fun handleClientOperation(
        ex: ClientOperationException,
        request: WebRequest
    ): ResponseEntity<ApiResponse<Nothing>> {
        logger.warn("Client operation error: ${ex.message}")
        
        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
            .body(ApiResponse.error(
                message = ex.message ?: "Client operation failed",
                code = "CLIENT_OPERATION_ERROR"
            ))
    }

    @ExceptionHandler(PetOperationException::class)
    fun handlePetOperation(
        ex: PetOperationException,
        request: WebRequest
    ): ResponseEntity<ApiResponse<Nothing>> {
        logger.warn("Pet operation error: ${ex.message}")
        
        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
            .body(ApiResponse.error(
                message = ex.message ?: "Pet operation failed",
                code = "PET_OPERATION_ERROR"
            ))
    }

    @ExceptionHandler(MethodArgumentNotValidException::class)
    fun handleValidationErrors(
        ex: MethodArgumentNotValidException,
        request: WebRequest
    ): ResponseEntity<ApiResponse<Nothing>> {
        logger.info("Validation error occurred for request: ${request.getDescription(false)}")

        val errors = ex.bindingResult.fieldErrors.map { error ->
            error.defaultMessage ?: "Invalid value for ${error.field}"
        }

        // Use the first error message as the main message for better UX
        val mainMessage = errors.firstOrNull() ?: "Validation failed"

        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
            .body(ApiResponse.error(
                message = mainMessage,
                code = "VALIDATION_ERROR"
            ))
    }

    @ExceptionHandler(IllegalArgumentException::class)
    fun handleIllegalArgument(
        ex: IllegalArgumentException,
        request: WebRequest
    ): ResponseEntity<ApiResponse<Nothing>> {
        logger.warn("Illegal argument: ${ex.message}")
        
        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
            .body(ApiResponse.error(
                message = ex.message ?: "Invalid argument provided",
                code = "INVALID_ARGUMENT"
            ))
    }

    @ExceptionHandler(Exception::class)
    fun handleGenericException(
        ex: Exception,
        request: WebRequest
    ): ResponseEntity<ApiResponse<Nothing>> {
        logger.error("Unexpected error occurred", ex)
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
            .body(ApiResponse.error(
                message = "An unexpected error occurred",
                code = "INTERNAL_SERVER_ERROR"
            ))
    }

    @ExceptionHandler(PastTimeBlockException::class)
    fun handleNotImplemented(
        ex: PastTimeBlockException,
        request: WebRequest
    ): ResponseEntity<ApiResponse<Nothing>> {
        logger.error("PastTimeBlock operation error: ${ex.message}")

        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
            .body(ApiResponse.error(
                message = ex.message ?: "Cannot block time in the past",
                code = "PAST_TIME_BLOCK"
            ))
    }
}
