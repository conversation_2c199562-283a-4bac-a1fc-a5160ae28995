package ro.animaliaprogramari.animalia.adapter.inbound.rest.mapper

import org.springframework.stereotype.Component
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.*
import ro.animaliaprogramari.animalia.application.command.*
import ro.animaliaprogramari.animalia.application.port.inbound.*
import ro.animaliaprogramari.animalia.application.query.*
import ro.animaliaprogramari.animalia.domain.model.*
import java.time.DayOfWeek

/**
 * Mapper for converting between block time DTOs and domain models/commands/queries
 */
@Component
class BlockTimeDtoMapper {

    /**
     * Convert create request to command
     */
    fun toCreateCommand(
        request: CreateBlockTimeRequest,
        salonId: SalonId,
        createdBy: UserId
    ): CreateBlockTimeCommand {
        return CreateBlockTimeCommand(
            salonId = salonId,
            startTime = request.startTime,
            endTime = request.endTime,
            reason = BlockReason.fromString(request.reason),
            customReason = request.customReason,
            staffIds = request.staffIds.map { StaffId.of(it) }.toSet(),
            isRecurring = request.isRecurring,
            recurrencePattern = request.recurrencePattern?.let { toRecurrencePattern(it) },
            notes = request.notes,
            createdBy = createdBy
        )
    }

    /**
     * Convert update request to command
     */
    fun toUpdateCommand(
        request: UpdateBlockTimeRequest,
        salonId: SalonId,
        blockId: BlockTimeId,
        updatedBy: UserId
    ): UpdateBlockTimeCommand {
        return UpdateBlockTimeCommand(
            salonId = salonId,
            blockId = blockId,
            startTime = request.startTime,
            endTime = request.endTime,
            reason = request.reason?.let { BlockReason.fromString(it) },
            customReason = request.customReason,
            staffIds = request.staffIds?.map { StaffId.of(it) }?.toSet(),
            notes = request.notes,
            updatedBy = updatedBy
        )
    }

    /**
     * Convert bulk request to command
     */
    fun toBulkCommand(
        request: BulkBlockTimeOperationsRequest,
        salonId: SalonId,
        performedBy: UserId
    ): BulkBlockTimeOperationsCommand {
        return BulkBlockTimeOperationsCommand(
            salonId = salonId,
            operation = BulkOperation.fromString(request.operation),
            blocks = request.blocks.map { toBulkItem(it) },
            performedBy = performedBy
        )
    }

    /**
     * Convert availability request to query
     */
    fun toAvailabilityQuery(
        request: CheckTimeAvailabilityRequest,
        salonId: SalonId
    ): CheckTimeAvailabilityQuery {
        return CheckTimeAvailabilityQuery(
            salonId = salonId,
            startTime = request.startTime,
            endTime = request.endTime,
            staffIds = request.staffIds.map { StaffId.of(it) }.toSet()
        )
    }

    /**
     * Convert block time result to response
     */
    fun toResponse(result: BlockTimeResult): BlockTimeResponse {
        return toResponse(result.blockTime)
    }

    /**
     * Convert block time to response
     */
    fun toResponse(blockTime: BlockTime): BlockTimeResponse {
        return BlockTimeResponse(
            blockId = blockTime.id.value,
            salonId = blockTime.salonId.value,
            startTime = blockTime.startTime,
            endTime = blockTime.endTime,
            duration = blockTime.getDurationMinutes(),
            reason = blockTime.reason.displayName,
            customReason = blockTime.customReason,
            staffIds = blockTime.staffIds.map { it.value },
            createdBy = blockTime.createdBy.value,
            createdAt = blockTime.createdAt,
            updatedAt = blockTime.updatedAt,
            isRecurring = blockTime.isRecurring,
            recurrencePattern = blockTime.recurrencePattern?.let { toRecurrencePatternDto(it) },
            notes = blockTime.notes,
            status = blockTime.status.displayName
        )
    }

    /**
     * Convert block time with staff names to response
     */
    fun toResponse(blockTimeWithStaff: BlockTimeWithStaffNames): BlockTimeResponse {
        val baseResponse = toResponse(blockTimeWithStaff.blockTime)
        return baseResponse.copy(
            staffNames = blockTimeWithStaff.staffNames,
            createdByName = blockTimeWithStaff.createdByName
        )
    }

    /**
     * Convert block time list result to response
     */
    fun toListResponse(result: BlockTimeListResult): BlockTimeListResponse {
        return BlockTimeListResponse(
            blocks = result.blocks.map { toResponse(it) },
            pagination = toPaginationDto(result.pagination),
            summary = toSummaryDto(result.summary)
        )
    }

    /**
     * Convert block time details to response
     */
    fun toDetailsResponse(details: BlockTimeDetails): BlockTimeDetailsResponse {
        return BlockTimeDetailsResponse(
            blockTime = toResponse(details.blockTime).copy(
                createdByName = details.createdByName
            ),
            staffDetails = details.staffDetails.map { toStaffInfoDto(it) },
            affectedAppointments = details.affectedAppointments.map { toAffectedAppointmentDto(it) },
            history = details.history.map { toHistoryDto(it) }
        )
    }

    /**
     * Convert availability check result to response
     */
    fun toAvailabilityResponse(result: AvailabilityCheckResult): Map<String, Any> {
        return mapOf(
            "available" to result.available,
            "conflicts" to result.conflicts.map { toConflictDto(it) },
            "availableStaff" to result.availableStaff.map { toAvailableStaffDto(it) },
            "suggestions" to result.suggestions.map { toSuggestionDto(it) }
        )
    }

    // Helper methods
    private fun toRecurrencePattern(dto: RecurrencePatternDto): RecurrencePattern {
        return RecurrencePattern(
            type = RecurrenceType.fromString(dto.type),
            interval = dto.interval,
            daysOfWeek = dto.daysOfWeek?.map { DayOfWeek.valueOf(it) }?.toSet(),
            dayOfMonth = dto.dayOfMonth,
            endDate = dto.endDate,
            occurrences = dto.occurrences
        )
    }

    private fun toRecurrencePatternDto(pattern: RecurrencePattern): RecurrencePatternDto {
        return RecurrencePatternDto(
            type = pattern.type.name,
            interval = pattern.interval,
            daysOfWeek = pattern.daysOfWeek?.map { it.name },
            dayOfMonth = pattern.dayOfMonth,
            endDate = pattern.endDate,
            occurrences = pattern.occurrences
        )
    }

    private fun toBulkItem(dto: BulkBlockTimeItemDto): BulkBlockTimeItem {
        return BulkBlockTimeItem(
            blockId = dto.blockId?.let { BlockTimeId.of(it) },
            startTime = dto.startTime,
            endTime = dto.endTime,
            reason = dto.reason?.let { BlockReason.fromString(it) },
            customReason = dto.customReason,
            staffIds = dto.staffIds?.map { StaffId.of(it) }?.toSet(),
            notes = dto.notes
        )
    }

    private fun toPaginationDto(pagination: PaginationInfo): PaginationDto {
        return PaginationDto(
            currentPage = pagination.currentPage,
            totalPages = pagination.totalPages,
            totalItems = pagination.totalItems,
            itemsPerPage = pagination.itemsPerPage,
            hasNextPage = pagination.hasNextPage,
            hasPreviousPage = pagination.hasPreviousPage
        )
    }

    private fun toSummaryDto(summary: BlockTimeSummary): BlockTimeSummaryDto {
        return BlockTimeSummaryDto(
            totalActiveBlocks = summary.totalActiveBlocks,
            totalHoursBlocked = summary.totalHoursBlocked,
            mostCommonReason = summary.mostCommonReason,
            staffWithMostBlocks = summary.staffWithMostBlocks?.let {
                StaffBlockSummaryDto(it.staffId, it.staffName, it.blockCount)
            }
        )
    }

    private fun toStaffInfoDto(staffInfo: StaffInfo): StaffInfoDto {
        return StaffInfoDto(
            staffId = staffInfo.staffId,
            name = staffInfo.name,
            nickname = staffInfo.nickname,
            role = staffInfo.role
        )
    }

    private fun toAffectedAppointmentDto(appointment: AffectedAppointment): AffectedAppointmentDto {
        return AffectedAppointmentDto(
            appointmentId = appointment.appointmentId,
            clientName = appointment.clientName,
            conflictType = appointment.conflictType,
            suggestedAction = appointment.suggestedAction
        )
    }

    private fun toHistoryDto(history: BlockTimeHistoryEntry): BlockTimeHistoryDto {
        return BlockTimeHistoryDto(
            action = history.action,
            performedBy = history.performedBy,
            performedByName = history.performedByName,
            timestamp = history.timestamp,
            details = history.details
        )
    }

    private fun toConflictDto(conflict: BlockTimeConflict): Map<String, Any> {
        return mapOf(
            "staffId" to conflict.staffId.value,
            "staffName" to conflict.staffName,
            "conflictType" to conflict.conflictType.displayName,
            "conflictDetails" to mapOf(
                "id" to conflict.conflictDetails.id,
                "startTime" to conflict.conflictDetails.startTime,
                "endTime" to conflict.conflictDetails.endTime,
                "description" to conflict.conflictDetails.description
            )
        )
    }

    private fun toAvailableStaffDto(staff: AvailableStaffInfo): Map<String, Any> {
        return mapOf(
            "staffId" to staff.staffId,
            "staffName" to staff.staffName,
            "available" to staff.available
        )
    }

    private fun toSuggestionDto(suggestion: ro.animaliaprogramari.animalia.domain.service.TimeSlotSuggestion): Map<String, Any> {
        return mapOf(
            "type" to suggestion.type.name,
            "description" to suggestion.description,
            "startTime" to suggestion.startTime,
            "endTime" to suggestion.endTime,
            "staffIds" to suggestion.staffIds.map { it.value }
        )
    }
}
