package ro.animaliaprogramari.animalia.adapter.inbound.rest

import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.responses.ApiResponse as SwaggerApiResponse
import io.swagger.v3.oas.annotations.tags.Tag
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.*
import ro.animaliaprogramari.animalia.adapter.inbound.security.SecurityUtils
import ro.animaliaprogramari.animalia.config.AnimaliaConstants
import ro.animaliaprogramari.animalia.domain.model.*
import java.math.BigDecimal
import java.time.LocalDateTime

/**
 * REST controller for client details operations
 * Handles client statistics, reviews, subscriptions, and appointments
 */
@RestController
@RequestMapping("/salons")
@CrossOrigin(origins = ["*"])
@Tag(name = "Client Details", description = "Operations for retrieving client information")
class ClientDetailsController {

    private val logger = LoggerFactory.getLogger(ClientDetailsController::class.java)

    /**
     * GET /api/salons/{salonId}/clients/{clientId}/stats
     * Get client statistics for a specific salon
    */
    @GetMapping("/{salonId}/clients/{clientId}/stats")
    @Operation(summary = "Client statistics", description = "Retrieve statistics for a salon client")
    @SwaggerApiResponse(responseCode = "200", description = "Statistics retrieved successfully")
    @SwaggerApiResponse(responseCode = "401", description = AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR)
    @SwaggerApiResponse(responseCode = "403", description = AnimaliaConstants.NU_AVETI_PERMISIUNEA_SA_ACCESATI_ACEASTA_RESURSA)
    fun getClientStats(
        @PathVariable salonId: String,
        @PathVariable clientId: String
    ): ResponseEntity<ApiResponse<ClientStatisticsResponse>> {
        logger.debug("REST request to get stats for client $clientId in salon $salonId")

        return try {
            val currentUser = SecurityUtils.getCurrentUser()
                ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            val salon = SalonId.of(salonId)

            // Authorization: User must have client data access in this salon
            if (!currentUser.hasClientDataAccessInSalon(salon)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error(AnimaliaConstants.NU_AVETI_PERMISIUNEA_SA_ACCESATI_ACEASTA_RESURSA))
            }

            // For now, return mock statistics
            // TODO: Implement actual statistics calculation from appointments, reviews, etc.
            val stats = ClientStatistics.empty(ClientId.of(clientId), salon).copy(
                totalAppointments = 12,
                completedAppointments = 10,
                cancelledAppointments = 1,
                noShowAppointments = 1,
                upcomingAppointments = 2,
                lastVisitDate = LocalDateTime.now().minusDays(7),
                totalRevenue = BigDecimal("450.00"),
                averageAppointmentValue = BigDecimal("45.00"),
                totalPets = 2,
                loyaltyScore = BigDecimal("75.5"),
                favoriteServices = listOf("Tuns", "Baie", "Manichiură"),
                averageRating = BigDecimal("4.5"),
                totalReviews = 8,
                activeSubscriptions = 1
            )

            val response = ClientStatisticsResponse(
                totalAppointments = stats.totalAppointments,
                completedAppointments = stats.completedAppointments,
                cancelledAppointments = stats.cancelledAppointments,
                noShowAppointments = stats.noShowAppointments,
                upcomingAppointments = stats.upcomingAppointments,
                lastVisitDate = stats.lastVisitDate,
                totalRevenue = stats.totalRevenue.toDouble(),
                averageAppointmentValue = stats.averageAppointmentValue.toDouble(),
                totalPets = stats.totalPets,
                loyaltyScore = stats.loyaltyScore.toDouble(),
                favoriteServices = stats.favoriteServices,
                averageRating = stats.averageRating?.toDouble(),
                totalReviews = stats.totalReviews,
                activeSubscriptions = stats.activeSubscriptions,
                completionRate = stats.completionRate().toDouble(),
                cancellationRate = stats.cancellationRate().toDouble(),
                noShowRate = stats.noShowRate().toDouble(),
                clientTier = stats.getClientTier().name,
                isVip = stats.isVip()
            )

            ResponseEntity.ok(ApiResponse.success(response))

        } catch (e: Exception) {
            logger.error("Error getting client stats", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error(AnimaliaConstants.INTERNAL_SERVER_ERROR))
        }
    }

    /**
     * GET /api/salons/{salonId}/clients/{clientId}/reviews
     * Get client reviews for a specific salon
     */
    @GetMapping("/{salonId}/clients/{clientId}/reviews")
    fun getClientReviews(
        @PathVariable salonId: String,
        @PathVariable clientId: String,
        @RequestParam(required = false, defaultValue = "10") limit: Int,
        @RequestParam(required = false, defaultValue = "0") offset: Int
    ): ResponseEntity<ApiResponse<List<ReviewResponse>>> {
        logger.debug("REST request to get reviews for client $clientId in salon $salonId")

        return try {
            val currentUser = SecurityUtils.getCurrentUser()
                ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            val salon = SalonId.of(salonId)

            // Authorization: User must have client data access in this salon
            if (!currentUser.hasClientDataAccessInSalon(salon)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error(AnimaliaConstants.NU_AVETI_PERMISIUNEA_SA_ACCESATI_ACEASTA_RESURSA))
            }

            // For now, return mock reviews
            // TODO: Implement actual review retrieval from database
            val mockReviews = listOf(
                ReviewResponse(
                    id = "review_1",
                    clientId = clientId,
                    appointmentId = "appointment_1",
                    petId = "pet_1",
                    petName = "Max",
                    service = "Tuns complet",
                    rating = 5,
                    comment = "Serviciu excelent! Max arată fantastic.",
                    reviewDate = LocalDateTime.now().minusDays(3),
                    photoUrls = emptyList(),
                    isVerified = true,
                    groomerId = currentUser.userId.value,
                    groomerName = currentUser.name
                ),
                ReviewResponse(
                    id = "review_2",
                    clientId = clientId,
                    appointmentId = "appointment_2",
                    petId = "pet_2",
                    petName = "Bella",
                    service = "Baie și uscăt",
                    rating = 4,
                    comment = "Foarte mulțumit de serviciu.",
                    reviewDate = LocalDateTime.now().minusDays(10),
                    photoUrls = emptyList(),
                    isVerified = true,
                    groomerId = currentUser.userId.value,
                    groomerName = currentUser.name
                )
            ).drop(offset).take(limit)

            ResponseEntity.ok(ApiResponse.success(mockReviews))

        } catch (e: Exception) {
            logger.error("Error getting client reviews", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error(AnimaliaConstants.INTERNAL_SERVER_ERROR))
        }
    }

    /**
     * GET /api/salons/{salonId}/clients/{clientId}/subscriptions
     * Get client subscriptions for a specific salon
     */
    @GetMapping("/{salonId}/clients/{clientId}/subscriptions")
    fun getClientSubscriptions(
        @PathVariable salonId: String,
        @PathVariable clientId: String
    ): ResponseEntity<ApiResponse<List<SubscriptionResponse>>> {
        logger.debug("REST request to get subscriptions for client $clientId in salon $salonId")

        return try {
            val currentUser = SecurityUtils.getCurrentUser()
                ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            val salon = SalonId.of(salonId)

            // Authorization: User must have client data access in this salon
            if (!currentUser.hasClientDataAccessInSalon(salon)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error(AnimaliaConstants.NU_AVETI_PERMISIUNEA_SA_ACCESATI_ACEASTA_RESURSA))
            }

            // For now, return mock subscriptions
            // TODO: Implement actual subscription retrieval from database
            val mockSubscriptions = listOf(
                SubscriptionResponse(
                    id = "subscription_1",
                    clientId = clientId,
                    name = "Pachet Premium Lunar",
                    description = "Tuns complet + baie + manichiură pentru 2 animale",
                    price = 200.0,
                    frequency = "MONTHLY",
                    startDate = LocalDateTime.now().minusMonths(2),
                    endDate = LocalDateTime.now().plusMonths(10),
                    isActive = true,
                    sessionsIncluded = 4,
                    sessionsUsed = 1,
                    includedServices = listOf("Tuns", "Baie", "Manichiură"),
                    notes = "Reducere 20% pentru clienți fideli"
                )
            )

            ResponseEntity.ok(ApiResponse.success(mockSubscriptions))

        } catch (e: Exception) {
            logger.error("Error getting client subscriptions", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error(AnimaliaConstants.INTERNAL_SERVER_ERROR))
        }
    }
}