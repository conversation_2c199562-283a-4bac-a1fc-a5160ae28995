package ro.animaliaprogramari.animalia.adapter.inbound.rest.mapper

import org.springframework.stereotype.Component
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.*
import ro.animaliaprogramari.animalia.application.command.UpdateStaffWorkingHoursCommand
import ro.animaliaprogramari.animalia.application.query.GetStaffWorkingHoursQuery
import ro.animaliaprogramari.animalia.application.query.GetStaffAvailabilityQuery
import ro.animaliaprogramari.animalia.domain.model.*
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.LocalTime
import java.time.format.DateTimeFormatter

/**
 * Mapper between Staff Working Hours DTOs and domain models
 */
@Component
class StaffWorkingHoursDtoMapper {

    private val timeFormatter = DateTimeFormatter.ofPattern("HH:mm")

    /**
     * Convert domain model to response DTO
     */
    fun toResponse(workingHours: StaffWorkingHoursSettings): StaffWorkingHoursResponse {
        val weeklyScheduleResponse = workingHours.weeklySchedule.mapKeys { (dayOfWeek, _) ->
            dayOfWeek.name.lowercase()
        }.mapValues { (_, daySchedule) ->
            toDayScheduleResponse(daySchedule)
        }

        val holidaysResponse = workingHours.holidays.map { holiday ->
            StaffHolidayResponse(
                name = holiday.name,
                date = holiday.date,
                isWorkingDay = holiday.isWorkingDay,
                type = holiday.type.name
            )
        }

        val customClosuresResponse = workingHours.customClosures.map { closure ->
            StaffCustomClosureResponse(
                reason = closure.reason,
                date = closure.date,
                description = closure.description
            )
        }

        return StaffWorkingHoursResponse(
            staffId = workingHours.staffId.value,
            salonId = workingHours.salonId.value,
            weeklySchedule = weeklyScheduleResponse,
            holidays = holidaysResponse,
            customClosures = customClosuresResponse,
            updatedAt = workingHours.updatedAt
        )
    }

    /**
     * Convert request DTO to domain command
     */
    fun toUpdateCommand(
        staffId: StaffId,
        salonId: SalonId,
        updaterUserId: UserId,
        request: UpdateStaffWorkingHoursRequest
    ): UpdateStaffWorkingHoursCommand {
        val weeklySchedule = request.weeklySchedule.mapKeys { (dayName, _) ->
            DayOfWeek.valueOf(dayName.uppercase())
        }.mapValues { (_, dayScheduleRequest) ->
            toDayScheduleDomain(dayScheduleRequest)
        }

        val holidays = request.holidays.map { holidayRequest ->
            StaffHoliday.create(
                salonId = salonId,
                name = holidayRequest.name,
                date = holidayRequest.date,
                isWorkingDay = holidayRequest.isWorkingDay,
                type = HolidayType.fromString(holidayRequest.type)
            )
        }

        val customClosures = request.customClosures.map { closureRequest ->
            StaffCustomClosure.create(
                salonId = salonId,
                reason = closureRequest.reason,
                date = closureRequest.date,
                description = closureRequest.description
            )
        }

        return UpdateStaffWorkingHoursCommand(
            staffId = staffId,
            salonId = salonId,
            updaterUserId = updaterUserId,
            weeklySchedule = weeklySchedule,
            holidays = holidays,
            customClosures = customClosures
        )
    }

    /**
     * Convert query parameters to domain query
     */
    fun toGetWorkingHoursQuery(
        staffId: String,
        salonId: String,
        requesterId: UserId
    ): GetStaffWorkingHoursQuery {
        return GetStaffWorkingHoursQuery(
            staffId = StaffId.of(staffId),
            salonId = SalonId.of(salonId),
            requesterId = requesterId
        )
    }

    /**
     * Convert availability query parameters to domain query
     */
    fun toGetAvailabilityQuery(
        salonId: String,
        date: LocalDate,
        startTime: String?,
        endTime: String?,
        requesterId: UserId
    ): GetStaffAvailabilityQuery {
        return GetStaffAvailabilityQuery(
            salonId = SalonId.of(salonId),
            date = date,
            startTime = startTime?.let { LocalTime.parse(it, timeFormatter) },
            endTime = endTime?.let { LocalTime.parse(it, timeFormatter) },
            requesterId = requesterId
        )
    }

    /**
     * Convert availability report to response DTO
     */
    fun toAvailabilityResponse(report: StaffAvailabilityReport): StaffAvailabilityResponse {
        val staffAvailability = report.staffAvailability.map { staffAvailability ->
            StaffMemberAvailabilityResponse(
                staffId = staffAvailability.staff.userId.value,
                staffName = "${staffAvailability.staff.userId.value}", // TODO: Get actual name from User entity
                isAvailable = staffAvailability.isAvailable,
                workingHours = staffAvailability.workingHours?.let { toDayScheduleResponse(it) },
                conflictingAppointments = staffAvailability.conflictingAppointments.map { appointment ->
                    ConflictingAppointmentResponse(
                        appointmentId = appointment.id.value,
                        startTime = appointment.startTime.format(timeFormatter),
                        endTime = appointment.endTime.format(timeFormatter),
                        clientName = "Client ${appointment.clientId.value}" // TODO: Get actual client name
                    )
                },
                reason = staffAvailability.reason
            )
        }

        return StaffAvailabilityResponse(
            date = report.date,
            staffAvailability = staffAvailability
        )
    }

    private fun toDayScheduleResponse(daySchedule: DaySchedule): DayScheduleResponse {
        return DayScheduleResponse(
            startTime = daySchedule.startTime?.format(timeFormatter),
            endTime = daySchedule.endTime?.format(timeFormatter),
            isWorkingDay = daySchedule.isWorkingDay,
            breakStart = daySchedule.breakStart?.format(timeFormatter),
            breakEnd = daySchedule.breakEnd?.format(timeFormatter)
        )
    }

    private fun toDayScheduleDomain(dayScheduleRequest: DayScheduleRequest): DaySchedule {
        return if (dayScheduleRequest.isWorkingDay) {
            DaySchedule.workingDay(
                startTime = LocalTime.parse(dayScheduleRequest.startTime!!, timeFormatter),
                endTime = LocalTime.parse(dayScheduleRequest.endTime!!, timeFormatter),
                breakStart = dayScheduleRequest.breakStart?.let { LocalTime.parse(it, timeFormatter) },
                breakEnd = dayScheduleRequest.breakEnd?.let { LocalTime.parse(it, timeFormatter) }
            )
        } else {
            DaySchedule.dayOff()
        }
    }
}
