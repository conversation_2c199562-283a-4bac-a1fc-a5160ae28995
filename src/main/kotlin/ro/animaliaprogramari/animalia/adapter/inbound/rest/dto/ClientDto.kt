package ro.animaliaprogramari.animalia.adapter.inbound.rest.dto

import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.Size
import java.time.LocalDateTime

/**
 * DTO for client registration request
 */
@Schema(description = "Client registration request")
data class RegisterClientRequest(
    @Schema(description = "Client name")
    @field:NotBlank(message = "Name is required")
    @field:Size(max = 255, message = "Name must not exceed 255 characters")
    val name: String,

    @Schema(description = "Client phone number")
    @field:Size(max = 50, message = "Phone must not exceed 50 characters")
    val phone: String? = null,

    @Schema(description = "Client email address")
    @field:Email(message = "Email should be valid")
    @field:Size(max = 255, message = "Email must not exceed 255 characters")
    val email: String? = null,

    @Schema(description = "Client address")
    val address: String? = null,

    @Schema(description = "Additional notes")
    val notes: String? = null
)

/**
 * DTO for client update request
 */
@Schema(description = "Client update request")
data class UpdateClientRequest(
    @Schema(description = "Client name")
    @field:NotBlank(message = "Name is required")
    @field:Size(max = 255, message = "Name must not exceed 255 characters")
    val name: String,

    @Schema(description = "Client phone number")
    @field:Size(max = 50, message = "Phone must not exceed 50 characters")
    val phone: String? = null,

    @Schema(description = "Client email address")
    @field:Email(message = "Email should be valid")
    @field:Size(max = 255, message = "Email must not exceed 255 characters")
    val email: String? = null,

    @Schema(description = "Client address")
    val address: String? = null,

    @Schema(description = "Additional notes")
    val notes: String? = null
)

/**
 * DTO for client response
 */
@Schema(description = "Client response details")
data class ClientResponse(
    @Schema(description = "Client ID")
    val id: String,

    @Schema(description = "Client name")
    val name: String,

    @Schema(description = "Client phone number")
    val phone: String?,

    @Schema(description = "Client email address")
    val email: String?,

    @Schema(description = "Client address")
    val address: String?,

    @Schema(description = "Additional notes")
    val notes: String?,

    @Schema(description = "Whether the client is active")
    val isActive: Boolean,

    @Schema(description = "Creation timestamp")
    val createdAt: LocalDateTime,

    @Schema(description = "Last update timestamp")
    val updatedAt: LocalDateTime,

    @Schema(description = "Number of pets owned")
    val petCount: Int = 0
)