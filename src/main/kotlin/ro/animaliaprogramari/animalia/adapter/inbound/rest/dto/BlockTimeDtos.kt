package ro.animaliaprogramari.animalia.adapter.inbound.rest.dto

import com.fasterxml.jackson.annotation.JsonFormat
import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotEmpty
import jakarta.validation.constraints.NotNull
import java.time.LocalDateTime
import java.time.ZonedDateTime

/**
 * Request DTO for creating a block time
 */
data class CreateBlockTimeRequest(
    @Schema(description = "Start time of the blocked period", example = "2024-06-15T09:00:00Z")
    @field:NotNull(message = "Start time is required")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss[.SSS]XXX")
    val startTime: ZonedDateTime,

    @Schema(description = "End time of the blocked period", example = "2024-06-15T10:00:00Z")
    @field:NotNull(message = "End time is required")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss[.SSS]XXX")
    val endTime: ZonedDateTime,

    @Schema(description = "Predefined reason category", example = "Pauză")
    @field:NotBlank(message = "Reason is required")
    val reason: String,

    @Schema(description = "Custom reason when reason is 'Altele'", example = "Întâlnire cu furnizorul")
    val customReason: String? = null,

    @Schema(description = "List of staff member IDs to block time for")
    @field:NotEmpty(message = "At least one staff member must be specified")
    val staffIds: List<String>,

    @Schema(description = "Whether this is a recurring block", example = "false")
    val isRecurring: Boolean = false,

    @Schema(description = "Recurrence configuration")
    val recurrencePattern: RecurrencePatternDto? = null,

    @Schema(description = "Additional notes about the block")
    val notes: String? = null
)

/**
 * Request DTO for updating a block time
 */
data class UpdateBlockTimeRequest(
    @Schema(description = "Start time of the blocked period")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss[.SSS]XXX")
    val startTime: ZonedDateTime? = null,

    @Schema(description = "End time of the blocked period")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss[.SSS]XXX")
    val endTime: ZonedDateTime? = null,

    @Schema(description = "Predefined reason category")
    val reason: String? = null,

    @Schema(description = "Custom reason when reason is 'Altele'")
    val customReason: String? = null,

    @Schema(description = "List of staff member IDs to block time for")
    val staffIds: List<String>? = null,

    @Schema(description = "Additional notes about the block")
    val notes: String? = null
)

/**
 * DTO for recurrence pattern
 */
data class RecurrencePatternDto(
    @Schema(description = "Recurrence type", example = "WEEKLY")
    @field:NotBlank(message = "Recurrence type is required")
    val type: String,

    @Schema(description = "Interval between recurrences", example = "1")
    val interval: Int,

    @Schema(description = "Days of week for weekly recurrence")
    val daysOfWeek: List<String>? = null,

    @Schema(description = "Day of month for monthly recurrence")
    val dayOfMonth: Int? = null,

    @Schema(description = "End date for recurrence")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss[.SSS]XXX")
    val endDate: ZonedDateTime? = null,

    @Schema(description = "Number of occurrences")
    val occurrences: Int? = null
)

/**
 * Response DTO for block time
 */
data class BlockTimeResponse(
    @Schema(description = "Block time ID", example = "block-789")
    val blockId: String,

    @Schema(description = "Salon ID", example = "salon-123")
    val salonId: String,

    @Schema(description = "Start time of the blocked period")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss[.SSS]XXX")
    val startTime: ZonedDateTime,

    @Schema(description = "End time of the blocked period")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss[.SSS]XXX")
    val endTime: ZonedDateTime,

    @Schema(description = "Duration in minutes")
    val duration: Long,

    @Schema(description = "Reason for blocking")
    val reason: String,

    @Schema(description = "Custom reason if applicable")
    val customReason: String? = null,

    @Schema(description = "List of affected staff IDs")
    val staffIds: List<String>,

    @Schema(description = "List of affected staff names")
    val staffNames: List<String>? = null,

    @Schema(description = "User who created the block")
    val createdBy: String,

    @Schema(description = "Name of user who created the block")
    val createdByName: String? = null,

    @Schema(description = "Creation timestamp")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss[.SSS]")
    val createdAt: LocalDateTime,

    @Schema(description = "Last update timestamp")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss[.SSS]")
    val updatedAt: LocalDateTime,

    @Schema(description = "Whether this is a recurring block")
    val isRecurring: Boolean,

    @Schema(description = "Recurrence pattern if applicable")
    val recurrencePattern: RecurrencePatternDto? = null,

    @Schema(description = "Additional notes")
    val notes: String? = null,

    @Schema(description = "Block status")
    val status: String
)

/**
 * Response DTO for detailed block time information
 */
data class BlockTimeDetailsResponse(
    val blockTime: BlockTimeResponse,
    val staffDetails: List<StaffInfoDto>,
    val affectedAppointments: List<AffectedAppointmentDto>,
    val history: List<BlockTimeHistoryDto>
)

/**
 * DTO for staff information
 */
data class StaffInfoDto(
    val staffId: String,
    val name: String,
    val nickname: String?,
    val role: String
)

/**
 * DTO for affected appointment
 */
data class AffectedAppointmentDto(
    val appointmentId: String,
    val clientName: String,
    val conflictType: String,
    val suggestedAction: String
)

/**
 * DTO for block time history entry
 */
data class BlockTimeHistoryDto(
    val action: String,
    val performedBy: String,
    val performedByName: String,
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss[.SSS]")
    val timestamp: LocalDateTime,
    val details: String
)

/**
 * Response DTO for block time list
 */
data class BlockTimeListResponse(
    val blocks: List<BlockTimeResponse>,
    val pagination: PaginationDto,
    val summary: BlockTimeSummaryDto
)

/**
 * DTO for pagination information
 */
data class PaginationDto(
    val currentPage: Int,
    val totalPages: Int,
    val totalItems: Int,
    val itemsPerPage: Int,
    val hasNextPage: Boolean,
    val hasPreviousPage: Boolean
)

/**
 * DTO for block time summary
 */
data class BlockTimeSummaryDto(
    val totalActiveBlocks: Int,
    val totalHoursBlocked: Double,
    val mostCommonReason: String,
    val staffWithMostBlocks: StaffBlockSummaryDto?
)

/**
 * DTO for staff block summary
 */
data class StaffBlockSummaryDto(
    val staffId: String,
    val staffName: String,
    val blockCount: Int
)

/**
 * Request DTO for bulk operations
 */
data class BulkBlockTimeOperationsRequest(
    @Schema(description = "Operation type", example = "CREATE")
    @field:NotBlank(message = "Operation is required")
    val operation: String,

    @Schema(description = "List of block time items")
    @field:NotEmpty(message = "At least one block must be specified")
    val blocks: List<BulkBlockTimeItemDto>
)

/**
 * DTO for bulk operation item
 */
data class BulkBlockTimeItemDto(
    val blockId: String? = null,
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss[.SSS]XXX")
    val startTime: ZonedDateTime? = null,
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss[.SSS]XXX")
    val endTime: ZonedDateTime? = null,
    val reason: String? = null,
    val customReason: String? = null,
    val staffIds: List<String>? = null,
    val notes: String? = null
)

/**
 * Request DTO for checking time availability
 */
data class CheckTimeAvailabilityRequest(
    @Schema(description = "Start time to check")
    @field:NotNull(message = "Start time is required")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss[.SSS]XXX")
    val startTime: ZonedDateTime,

    @Schema(description = "End time to check")
    @field:NotNull(message = "End time is required")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss[.SSS]XXX")
    val endTime: ZonedDateTime,

    @Schema(description = "Staff IDs to check availability for")
    @field:NotEmpty(message = "At least one staff member must be specified")
    val staffIds: List<String>
)
