package ro.animaliaprogramari.animalia.adapter.outbound.persistence

import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa.SpringUserRepository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.mapper.UserEntityMapper
import ro.animaliaprogramari.animalia.application.port.outbound.UserRepository
import ro.animaliaprogramari.animalia.domain.model.*

/**
 * JPA adapter implementing the UserRepository port
 * This adapter translates between domain models and JPA entities
 */
@Repository
class JpaUserRepository(
    private val springRepository: SpringUserRepository,
    private val userMapper: UserEntityMapper
) : UserRepository {

    override fun save(user: User): User {
        val entity = userMapper.toEntity(user)
        val savedEntity = springRepository.save(entity)
        return userMapper.toDomain(savedEntity)
    }

    override fun findById(id: UserId): User? {
        return springRepository.findById(id.value)
            .map { userMapper.toDomain(it) }
            .orElse(null)
    }

    override fun findByFirebaseUid(firebaseUid: String): User? {
        return springRepository.findByFirebaseUid(firebaseUid)
            ?.let { userMapper.toDomain(it) }
    }

    override fun findByEmail(email: Email): User? {
        return springRepository.findByEmail(email.value)
            ?.let { userMapper.toDomain(it) }
    }

    override fun findByPhoneNumber(phoneNumber: String): User? {
        return springRepository.findByPhoneNumber(phoneNumber)
            ?.let { userMapper.toDomain(it) }
    }

    override fun existsByFirebaseUid(firebaseUid: String): Boolean {
        return springRepository.existsByFirebaseUid(firebaseUid)
    }

    override fun existsByEmail(email: Email): Boolean {
        return springRepository.existsByEmail(email.value)
    }

    override fun findActiveUsers(): List<User> {
        return springRepository.findByIsActiveTrue()
            .map { userMapper.toDomain(it) }
    }

    override fun findAll(
        search: String?,
        role: UserRole?,
        isActive: Boolean?,
        limit: Int?,
        offset: Int?
    ): List<User> {
        // For now, implement basic filtering
        // In a real implementation, you would use JPA Criteria API or custom queries
        var users = springRepository.findAll()

        // Apply filters
        if (search != null) {
            users = users.filter {
                it.name.contains(search, ignoreCase = true) ||
                        it.email?.contains(search, ignoreCase = true) ?: false
            }
        }

        if (role != null) {
            users = users.filter { it.role == role.name }
        }

        if (isActive != null) {
            users = users.filter { it.isActive == isActive }
        }

        // Apply pagination
        val startIndex = offset ?: 0
        val endIndex = if (limit != null) {
            minOf(startIndex + limit, users.size)
        } else {
            users.size
        }

        return users.subList(startIndex, endIndex)
            .map { userMapper.toDomain(it) }
    }

    override fun deleteById(id: UserId): Boolean {
        return if (springRepository.existsById(id.value)) {
            springRepository.deleteById(id.value)
            true
        } else {
            false
        }
    }
}
