package ro.animaliaprogramari.animalia.adapter.outbound.persistence.converter

import jakarta.persistence.AttributeConverter
import jakarta.persistence.Converter
import ro.animaliaprogramari.animalia.domain.model.StaffId

/**
 * JPA converter for StaffId value object
 * Converts between StaffId and its String representation in the database
 */
@Converter(autoApply = true)
class StaffIdConverter : AttributeConverter<StaffId, String> {
    
    override fun convertToDatabaseColumn(attribute: StaffId?): String? {
        return attribute?.value
    }
    
    override fun convertToEntityAttribute(dbData: String?): StaffId? {
        return dbData?.let { 
            if (it.isBlank()) null else StaffId.of(it) 
        }
    }
}
