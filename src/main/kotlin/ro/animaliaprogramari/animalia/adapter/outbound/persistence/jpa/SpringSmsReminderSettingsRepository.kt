package ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa

import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SmsReminderSettings

@Repository
interface SpringSmsReminderSettingsRepository : JpaRepository<SmsReminderSettings, String> {

    fun findBySalonId(salonId: String): SmsReminderSettings?

    fun existsBySalonId(salonId: String): <PERSON><PERSON><PERSON>
}
