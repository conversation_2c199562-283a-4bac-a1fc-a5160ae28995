package ro.animaliaprogramari.animalia.adapter.outbound.persistence

import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.application.port.outbound.PetRepository
import ro.animaliaprogramari.animalia.domain.model.Pet
import ro.animaliaprogramari.animalia.domain.model.PetId
import ro.animaliaprogramari.animalia.domain.model.ClientId
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa.SpringPetRepository

/**
 * JPA adapter implementing the PetRepository port
 */
@Repository
class JpaPetRepository(
    private val springRepository: SpringPetRepository,
    private val petMapper: PetEntityMapper
) : PetRepository {
    
    override fun save(pet: Pet): Pet {
        val entity = petMapper.toEntity(pet)
        val savedEntity = springRepository.save(entity)
        return petMapper.toDomain(savedEntity)
    }
    
    override fun findById(id: PetId): Pet? {
        return springRepository.findById(id.value)
            .map { petMapper.toDomain(it) }
            .orElse(null)
    }
    
    override fun findByClientId(clientId: ClientId, activeOnly: Boolean): List<Pet> {
        val entities = if (activeOnly) {
            springRepository.findByClientIdAndIsActiveTrue(clientId.value)
        } else {
            springRepository.findByClientId(clientId.value)
        }
        return entities.map { petMapper.toDomain(it) }
    }
    
    override fun findAll(
        search: String?,
        clientId: ClientId?,
        isActive: Boolean?,
        limit: Int?,
        offset: Int?
    ): List<Pet> {
        // Simplified implementation
        var pets = springRepository.findAll()
        
        return pets
            .filter { clientId == null || it.clientId == clientId.value }
            .filter { isActive == null || it.isActive == isActive }
            .filter { search == null || it.name.contains(search, ignoreCase = true) }
            .let { if (offset != null) it.drop(offset) else it }
            .let { if (limit != null) it.take(limit) else it }
            .map { petMapper.toDomain(it) }
    }
    
    override fun existsById(id: PetId): Boolean {
        return springRepository.existsById(id.value)
    }
    
    override fun deleteById(id: PetId): Boolean {
        return if (springRepository.existsById(id.value)) {
            springRepository.deleteById(id.value)
            true
        } else {
            false
        }
    }
    
    override fun count(
        search: String?,
        clientId: ClientId?,
        isActive: Boolean?
    ): Long {
        return findAll(search, clientId, isActive).size.toLong()
    }
}
