package ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa

import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.Holiday
import java.time.LocalDate

/**
 * Spring Data JPA repository for holidays
 */
@Repository
interface SpringHolidayRepository : JpaRepository<Holiday, String> {

    /**
     * Find holidays by salon ID
     */
    fun findBySalonIdOrderByDate(salonId: String): List<Holiday>

    /**
     * Find holidays by salon ID and date range
     */
    fun findBySalonIdAndDateBetweenOrderByDate(
        salonId: String,
        startDate: LocalDate,
        endDate: LocalDate
    ): List<Holiday>

    /**
     * Delete holidays by salon ID
     */
    fun deleteBySalonId(salonId: String)
}
