package ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa

import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.Service

@Repository
interface SpringServiceRepository : JpaRepository<Service, String> {

    // Salon-scoped queries
    fun findBySalonId(salonId: String): List<Service>

    fun findBySalonIdAndIsActiveOrderByDisplayOrderAscNameAsc(salonId: String, isActive: Boolean): List<Service>

    fun existsByIdAndSalonId(id: String, salonId: String): Boolean

    // Global queries (for admin purposes)
    fun findByIsActiveTrue(): List<Service>

    @Query("SELECT s FROM Service s WHERE " +
           "s.salonId = :salonId AND " +
           "(:search IS NULL OR :search = '' OR " +
           "(s.name IS NOT NULL AND LOWER(s.name) LIKE LOWER(CONCAT('%', :search, '%'))) OR " +
           "(s.description IS NOT NULL AND LOWER(s.description) LIKE LOWER(CONCAT('%', :search, '%')))) AND " +
           "(:category IS NULL OR s.category = :category) AND " +
           "(:isActive IS NULL OR s.isActive = :isActive) " +
           "ORDER BY s.displayOrder ASC, s.name ASC")
    fun findBySalonIdAndSearchAndFilters(
        @Param("salonId") salonId: String,
        @Param("search") search: String?,
        @Param("category") category: String?,
        @Param("isActive") isActive: Boolean?
    ): List<Service>

}
