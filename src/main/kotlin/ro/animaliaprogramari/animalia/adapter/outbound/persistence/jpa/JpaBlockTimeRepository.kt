package ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa

import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.BlockTimeEntity
import java.time.LocalDate
import java.time.ZonedDateTime

/**
 * JPA repository interface for BlockTime entities
 * Provides optimized queries for block time management
 */
@Repository
interface JpaBlockTimeRepository : JpaRepository<BlockTimeEntity, String> {

    /**
     * Find block time by ID and salon ID
     */
    fun findByIdAndSalonId(id: String, salonId: String): BlockTimeEntity?

    /**
     * Find all block times for a salon with filtering
     */
    // Simple query without complex conditional logic
    fun findBySalonIdOrderByStartTimeDesc(salonId: String, pageable: Pageable): List<BlockTimeEntity>
    // Additional specific queries for filtering
    fun findBySalonIdAndReasonOrderByStartTimeDesc(salonId: String, reason: String, pageable: Pageable): List<BlockTimeEntity>

    fun findBySalonIdAndStatusOrderByStartTimeDesc(salonId: String, status: String, pageable: Pageable): List<BlockTimeEntity>

    fun findBySalonIdAndReasonAndStatusOrderByStartTimeDesc(salonId: String, reason: String, status: String, pageable: Pageable): List<BlockTimeEntity>

    /**
     * Count block times for a salon with filtering
     */
    // Count methods
    fun countBySalonId(salonId: String): Long

    fun countBySalonIdAndReason(salonId: String, reason: String): Long

    fun countBySalonIdAndStatus(salonId: String, status: String): Long

    fun countBySalonIdAndReasonAndStatus(salonId: String, reason: String, status: String): Long


    /**
     * Find overlapping block times for specific staff members
     * Uses JPQL instead of native SQL for better H2 compatibility
     */
    @Query("""
        SELECT b FROM BlockTimeEntity b
        WHERE b.salonId = :salonId
        AND b.status = 'ACTIVE'
        AND b.startTime < :endTime
        AND b.endTime > :startTime
        AND (:excludeId IS NULL OR b.id != CAST(:excludeId AS string))
    """)
    fun findOverlappingBlocksForSalon(
        @Param("salonId") salonId: String,
        @Param("startTime") startTime: ZonedDateTime,
        @Param("endTime") endTime: ZonedDateTime,
        @Param("excludeId") excludeId: String?
    ): List<BlockTimeEntity>

    /**
     * Find active block times for specific staff members in date range
     * Uses H2-compatible syntax for array operations
     */
    @Query(value = """
        SELECT * FROM block_times b
        WHERE b.salon_id = :salonId
        AND b.status = 'ACTIVE'
        AND DATE(b.start_time) >= :startDate
        AND DATE(b.end_time) <= :endDate
        AND (
            :staffId1 IS NULL OR :staffId1 = ANY(b.staff_ids)
        )
        ORDER BY b.start_time
    """, nativeQuery = true)
    fun findActiveBlocksForStaff(
        @Param("salonId") salonId: String,
        @Param("staffId1") staffId1: String?,
        @Param("startDate") startDate: LocalDate,
        @Param("endDate") endDate: LocalDate
    ): List<BlockTimeEntity>

    /**
     * Find all block times for a salon (for debugging)
     */
    fun findBySalonId(salonId: String): List<BlockTimeEntity>

    /**
     * Find active block times for a salon
     */
    @Query(value = """
        SELECT * FROM block_times b
        WHERE b.salon_id = :salonId
        AND b.status = 'ACTIVE'
    """, nativeQuery = true)
    fun findActiveBySalonId(
        @Param("salonId") salonId: String
    ): List<BlockTimeEntity>

    /**
     * Find block times by date range for statistics
     */
    @Query("""
        SELECT b FROM BlockTimeEntity b
        WHERE b.salonId = :salonId
        AND DATE(b.startTime) >= :startDate
        AND DATE(b.endTime) <= :endDate
        ORDER BY b.startTime
    """)
    fun findByDateRange(
        @Param("salonId") salonId: String,
        @Param("startDate") startDate: LocalDate,
        @Param("endDate") endDate: LocalDate,
        @Param("staffId") staffId: String?
    ): List<BlockTimeEntity>

    /**
     * Find expired block times that need status update
     */
    @Query("""
        SELECT b FROM BlockTimeEntity b 
        WHERE b.status = 'ACTIVE'
        AND b.endTime < :currentTime
    """)
    fun findExpiredBlocks(@Param("currentTime") currentTime: ZonedDateTime): List<BlockTimeEntity>

    /**
     * Check if block time exists by ID and salon ID
     */
    fun existsByIdAndSalonId(id: String, salonId: String): Boolean

    /**
     * Find block times by salon and status
     */
    fun findBySalonIdAndStatus(salonId: String, status: String): List<BlockTimeEntity>

    /**
     * Get statistics data for a salon in date range
     */
    @Query(value = """
        SELECT
            COUNT(b.*) as totalBlocks,
            COALESCE(SUM(EXTRACT(EPOCH FROM (b.end_time - b.start_time)) / 60), 0) as totalMinutes,
            b.reason as reason,
            COUNT(b.*) as reasonCount
        FROM block_times b
        WHERE b.salon_id = :salonId
        AND DATE(b.start_time) >= :startDate
        AND DATE(b.end_time) <= :endDate
        GROUP BY b.reason
    """, nativeQuery = true)
    fun getStatisticsData(
        @Param("salonId") salonId: String,
        @Param("startDate") startDate: LocalDate,
        @Param("endDate") endDate: LocalDate,
        @Param("staffId") staffId: String?
    ): List<Array<Any>>

    /**
     * Get daily block counts for statistics
     */
    @Query(value = """
        SELECT
            DATE(b.start_time) as blockDate,
            COUNT(b.*) as blockCount
        FROM block_times b
        WHERE b.salon_id = :salonId
        AND DATE(b.start_time) >= :startDate
        AND DATE(b.end_time) <= :endDate
        GROUP BY DATE(b.start_time)
        ORDER BY blockDate
    """, nativeQuery = true)
    fun getDailyBlockCounts(
        @Param("salonId") salonId: String,
        @Param("startDate") startDate: LocalDate,
        @Param("endDate") endDate: LocalDate
    ): List<Array<Any>>

    /**
     * Get hourly block counts for statistics
     */
    @Query(value = """
        SELECT
            EXTRACT(HOUR FROM b.start_time) as blockHour,
            COUNT(b.*) as blockCount
        FROM block_times b
        WHERE b.salon_id = :salonId
        AND DATE(b.start_time) >= :startDate
        AND DATE(b.end_time) <= :endDate
        GROUP BY EXTRACT(HOUR FROM b.start_time)
        ORDER BY blockHour
    """, nativeQuery = true)
    fun getHourlyBlockCounts(
        @Param("salonId") salonId: String,
        @Param("startDate") startDate: LocalDate,
        @Param("endDate") endDate: LocalDate
    ): List<Array<Any>>
}
