package ro.animaliaprogramari.animalia.adapter.outbound.storage

import com.cloudinary.Cloudinary
import com.cloudinary.utils.ObjectUtils
import org.springframework.stereotype.Service
import org.springframework.web.multipart.MultipartFile

@Service
class CloudinaryService(private val cloudinary: Cloudinary) {

    fun uploadImage(file: MultipartFile): String {
        val result = cloudinary.uploader().upload(file.bytes, ObjectUtils.emptyMap())
        return result["secure_url"] as String
    }
}
