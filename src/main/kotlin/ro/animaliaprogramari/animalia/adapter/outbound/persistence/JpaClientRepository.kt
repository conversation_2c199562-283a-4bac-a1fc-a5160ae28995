package ro.animaliaprogramari.animalia.adapter.outbound.persistence

import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.application.port.outbound.ClientRepository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa.SpringClientRepository
import ro.animaliaprogramari.animalia.domain.model.*

/**
 * JPA adapter implementing the ClientRepository port
 * This adapter translates between domain models and JPA entities
 */
@Repository
class JpaClientRepository(
    private val springRepository: SpringClientRepository,  // This extends JpaRepository
    private val clientMapper: ClientEntityMapper
) : ClientRepository {  // This implements our domain port

    override fun save(client: Client): Client {
        // Check if client already exists to preserve salonId
        val existingEntity = springRepository.findById(client.id.value).orElse(null)
        val salonId = existingEntity?.salonId // Keep existing salon ID or null for new clients

        // 1. Convert domain model to JPA entity
        val entity = clientMapper.toEntity(client, salonId)

        // 2. Save using Spring Data JPA
        val savedEntity = springRepository.save(entity)

        // 3. Convert back to domain model
        return clientMapper.toDomain(savedEntity)
    }

    override fun findById(id: ClientId): Client? {
        return springRepository.findById(id.value)
            .map { clientMapper.toDomain(it) }
            .orElse(null)
    }

    override fun findByEmail(email: Email): Client? {
        return springRepository.findByEmailIgnoreCase(email.value)
            ?.let { clientMapper.toDomain(it) }
    }

    override fun findAll(
        search: String?,
        isActive: Boolean?,
        userId: UserId?,
        limit: Int?,
        offset: Int?
    ): List<Client> {
        // Use the existing Spring repository method - userId is now treated as salonId
        val entities = springRepository.findBySearchAndActiveStatus(
            search,
            isActive,
            userId?.value // This is now interpreted as salonId for salon-scoped queries
        )

        return entities
            .let { if (offset != null) it.drop(offset) else it }
            .let { if (limit != null) it.take(limit) else it }
            .map { clientMapper.toDomain(it) }
    }

    override fun findByGroomer(
        userId: UserId,
        isActive: Boolean?,
        limit: Int?,
        offset: Int?
    ): List<Client> {
        // Since we removed the groomer relationship, we need to find clients by salon
        // The userId here should be mapped to a salon through the Staff system
        // For now, return empty list as this method needs to be redesigned
        return emptyList()
    }

    override fun existsById(id: ClientId): Boolean {
        return springRepository.existsById(id.value)
    }

    override fun existsByEmail(email: Email): Boolean {
        return springRepository.findByEmailIgnoreCase(email.value) != null
    }

    override fun deleteById(id: ClientId): Boolean {
        return if (springRepository.existsById(id.value)) {
            springRepository.deleteById(id.value)
            true
        } else {
            false
        }
    }

    override fun count(search: String?, isActive: Boolean?): Long {
        // Simplified implementation - in real app, you'd have a proper count query
        return findAll(search, isActive).size.toLong()
    }

    override fun findBySalonIds(
        salonIds: List<SalonId>,
        search: String?,
        isActive: Boolean?,
        limit: Int?,
        offset: Int?
    ): List<Client> {
        TODO("Not yet implemented")
    }

    override fun countBySalonIds(salonIds: List<SalonId>, search: String?, isActive: Boolean?): Long {
        TODO("Not yet implemented")
    }
}
