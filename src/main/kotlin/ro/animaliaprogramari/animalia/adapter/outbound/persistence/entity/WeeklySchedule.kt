package ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity

import jakarta.persistence.*
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull
import java.time.LocalDateTime
import java.time.LocalTime

/**
 * JPA entity for weekly schedule
 * This is an infrastructure concern - data representation for persistence
 */
@Entity
@Table(
    name = "weekly_schedule",
    uniqueConstraints = [UniqueConstraint(columnNames = ["salon_id", "day_of_week"])]
)
data class WeeklySchedule(
    @Id
    val id: String,

    @field:NotBlank(message = "Salon ID is required")
    @Column(name = "salon_id", nullable = false)
    val salonId: String,

    @field:NotBlank(message = "Day of week is required")
    @Column(name = "day_of_week", nullable = false)
    val dayOfWeek: String,

    @field:NotNull(message = "Start time is required")
    @Column(name = "start_time", nullable = false)
    val startTime: LocalTime,

    @field:NotNull(message = "End time is required")
    @Column(name = "end_time", nullable = false)
    val endTime: LocalTime,

    @Column(name = "is_day_off")
    val isDayOff: Boolean = false,

    @Column(name = "lunch_break_enabled")
    val lunchBreakEnabled: Boolean = false,

    @Column(name = "lunch_break_start_time")
    val lunchBreakStartTime: LocalTime? = null,

    @Column(name = "lunch_break_end_time")
    val lunchBreakEndTime: LocalTime? = null,

    @Column(name = "created_at")
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @Column(name = "updated_at")
    var updatedAt: LocalDateTime = LocalDateTime.now()
) {
    // Default constructor for JPA
    constructor() : this(
        "", "", "", LocalTime.of(9, 0), LocalTime.of(17, 0), false, false,
        null, null, LocalDateTime.now(), LocalDateTime.now()
    )
}
