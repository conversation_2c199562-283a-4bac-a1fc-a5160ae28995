package ro.animaliaprogramari.animalia.adapter.outbound.auth

import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.FirebaseAuthException
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import ro.animaliaprogramari.animalia.application.port.outbound.FirebaseTokenValidator
import ro.animaliaprogramari.animalia.application.port.outbound.FirebaseTokenValidationResult
import ro.animaliaprogramari.animalia.domain.model.FirebaseToken

/**
 * Firebase token validator implementation using Firebase Admin SDK
 * This is an outbound adapter that implements the FirebaseTokenValidator port
 */
@Component
class FirebaseTokenValidatorImpl(
    private val firebaseAuth: FirebaseAuth
) : FirebaseTokenValidator {

    private val logger = LoggerFactory.getLogger(FirebaseTokenValidatorImpl::class.java)

    override fun validateToken(token: FirebaseToken): FirebaseTokenValidationResult {
        try {
            logger.info("=== FIREBASE TOKEN VALIDATION START ===")
            logger.info("Token length: ${token.value.length}")
            logger.info("Token preview: ${token.value.take(50)}...")

            // Verify the Firebase ID token using Firebase Admin SDK
            logger.info("Calling Firebase Admin SDK verifyIdToken...")
            val decodedToken = firebaseAuth.verifyIdToken(token.value)

            logger.info("=== FIREBASE TOKEN VALIDATION SUCCESS ===")
            logger.info("Firebase UID: ${decodedToken.uid}")
            logger.info("Email: ${decodedToken.email}")
            logger.info("Name: ${decodedToken.name}")
            logger.info("Email verified: ${decodedToken.isEmailVerified}")
            logger.info("Claims keys: ${decodedToken.claims.keys}")

            // For phone authentication, email might be null
            val email = decodedToken.email
            val name = decodedToken.name ?:
                       decodedToken.email ?:
                       "Phone User"
            val phoneNumber = decodedToken.claims["phone_number"]?.toString()

            logger.info("Extracted phone number: $phoneNumber")
            logger.info("Final name: $name")

            return FirebaseTokenValidationResult.success(
                firebaseUid = decodedToken.uid,
                email = email ?: "", // Can be empty for phone auth
                name = name,
                phoneNumber = phoneNumber,
                emailVerified = decodedToken.isEmailVerified
            )

        } catch (e: FirebaseAuthException) {
            logger.error("=== FIREBASE TOKEN VALIDATION FAILED ===")
            logger.error("FirebaseAuthException type: ${e.javaClass.simpleName}")
            logger.error("Error code: ${e.errorCode}")
            logger.error("Error message: ${e.message}")
            logger.error("Stack trace: ", e)
            return FirebaseTokenValidationResult.failure("Invalid Firebase token: ${e.message}")

        } catch (e: IllegalArgumentException) {
            logger.error("=== FIREBASE TOKEN FORMAT ERROR ===")
            logger.error("IllegalArgumentException message: ${e.message}")
            logger.error("Stack trace: ", e)
            return FirebaseTokenValidationResult.failure("Invalid token format: ${e.message}")

        } catch (e: Exception) {
            logger.error("=== FIREBASE TOKEN VALIDATION UNEXPECTED ERROR ===")
            logger.error("Exception type: ${e.javaClass.simpleName}")
            logger.error("Exception message: ${e.message}")
            logger.error("Stack trace: ", e)
            return FirebaseTokenValidationResult.failure("Token validation failed: ${e.message}")
        }
    }
}
