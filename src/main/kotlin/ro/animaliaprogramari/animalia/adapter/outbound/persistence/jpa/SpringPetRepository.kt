package ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa

import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.Pet

@Repository
interface SpringPetRepository : JpaRepository<Pet, String> {

    fun findByClientId(clientId: String): List<Pet>

    fun findByClientIdAndIsActiveTrue(clientId: String): List<Pet>

}
