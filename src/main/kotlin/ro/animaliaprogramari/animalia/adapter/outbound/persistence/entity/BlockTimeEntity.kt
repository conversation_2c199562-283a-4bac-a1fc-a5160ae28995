package ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity

import jakarta.persistence.*
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull
import org.hibernate.annotations.JdbcTypeCode
import org.hibernate.type.SqlTypes
import java.time.LocalDateTime
import java.time.ZonedDateTime

/**
 * JPA entity for block time persistence
 * Maps to the block_times table in the database
 */
@Entity
@Table(name = "block_times")
data class BlockTimeEntity(
    @Id
    val id: String,

    @field:NotBlank(message = "Salon ID is required")
    @Column(name = "salon_id", nullable = false)
    val salonId: String,

    @field:NotNull(message = "Start time is required")
    @Column(name = "start_time", nullable = false)
    val startTime: ZonedDateTime,

    @field:NotNull(message = "End time is required")
    @Column(name = "end_time", nullable = false)
    val endTime: ZonedDateTime,

    @field:NotBlank(message = "Reason is required")
    @Column(name = "reason", nullable = false, length = 50)
    val reason: String,

    @Column(name = "custom_reason", columnDefinition = "TEXT")
    val customReason: String? = null,

    @JdbcTypeCode(SqlTypes.ARRAY)
    @Column(name = "staff_ids", nullable = false, columnDefinition = "text[]")
    val staffIds: Array<String>,

    @field:NotBlank(message = "Created by is required")
    @Column(name = "created_by", nullable = false)
    val createdBy: String,

    @field:NotNull(message = "Created at is required")
    @Column(name = "created_at", nullable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @Column(name = "updated_by")
    val updatedBy: String? = null,

    @field:NotNull(message = "Updated at is required")
    @Column(name = "updated_at", nullable = false)
    val updatedAt: LocalDateTime = LocalDateTime.now(),

    @Column(name = "is_recurring", nullable = false)
    val isRecurring: Boolean = false,

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "recurrence_pattern", columnDefinition = "jsonb")
    val recurrencePattern: String? = null, // JSON representation

    @Column(name = "notes", columnDefinition = "TEXT")
    val notes: String? = null,

    @field:NotBlank(message = "Status is required")
    @Column(name = "status", nullable = false, length = 20)
    val status: String = "ACTIVE"
) {
    // Default constructor for JPA
    constructor() : this(
        id = "",
        salonId = "",
        startTime = ZonedDateTime.now(),
        endTime = ZonedDateTime.now().plusHours(1),
        reason = "",
        customReason = null,
        staffIds = emptyArray(),
        createdBy = "",
        createdAt = LocalDateTime.now(),
        updatedBy = null,
        updatedAt = LocalDateTime.now(),
        isRecurring = false,
        recurrencePattern = null,
        notes = null,
        status = "ACTIVE"
    )

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as BlockTimeEntity

        if (id != other.id) return false

        return true
    }

    override fun hashCode(): Int {
        return id.hashCode()
    }

    override fun toString(): String {
        return "BlockTimeEntity(id='$id', salonId='$salonId', startTime=$startTime, endTime=$endTime, reason='$reason', status='$status')"
    }
}

/**
 * Enumeration for block time status at entity level
 */
enum class BlockTimeStatusEntity {
    ACTIVE,
    CANCELLED,
    EXPIRED
}

/**
 * Enumeration for block reason at entity level
 */
enum class BlockReasonEntity {
    PAUZA,
    INTALNIRE,
    CONCEDIU,
    PERSONAL,
    TRAINING,
    ALTELE
}
