package ro.animaliaprogramari.animalia.adapter.outbound.persistence.mapper

import org.springframework.stereotype.Component
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.WeeklySchedule
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.Holiday as HolidayEntity
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.CustomClosure as CustomClosureEntity
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.HolidayType as HolidayTypeEntity
import ro.animaliaprogramari.animalia.domain.model.*
import java.time.DayOfWeek
import java.time.LocalTime
import java.util.*

/**
 * Mapper for converting between domain and persistence models for working hours
 */
@Component
class WorkingHoursMapper {

    /**
     * Convert persistence entities to domain model
     */
    fun toDomain(
        salonId: SalonId,
        weeklyScheduleEntities: List<WeeklySchedule>,
        holidayEntities: List<HolidayEntity>,
        customClosureEntities: List<CustomClosureEntity>
    ): WorkingHoursSettings {

        val weeklySchedule = weeklyScheduleEntities.associate { entity ->
            val dayOfWeek = DayOfWeek.valueOf(entity.dayOfWeek.uppercase())

            dayOfWeek to if (entity.isDayOff) {
                DaySchedule.dayOff()
            } else {
                DaySchedule.workingDay(
                    startTime = entity.startTime,
                    endTime = entity.endTime,
                    breakStart = if (entity.lunchBreakEnabled) entity.lunchBreakStartTime else null,
                    breakEnd = if (entity.lunchBreakEnabled) entity.lunchBreakEndTime else null
                )
            }
        }

        val holidays = holidayEntities.map { entity ->
            Holiday(
                id = HolidayId.of(entity.id),
                salonId = salonId,
                name = entity.name,
                date = entity.date,
                isWorkingDay = entity.isWorkingDay,
                type = HolidayType.fromString(entity.type.name),
                createdAt = entity.createdAt,
                updatedAt = entity.updatedAt
            )
        }

        val customClosures = customClosureEntities.map { entity ->
            CustomClosure(
                id = CustomClosureId.of(entity.id),
                salonId = salonId,
                reason = entity.reason,
                date = entity.date,
                description = entity.description,
                createdAt = entity.createdAt,
                updatedAt = entity.updatedAt
            )
        }

        return WorkingHoursSettings(
            salonId = salonId,
            weeklySchedule = weeklySchedule,
            holidays = holidays,
            customClosures = customClosures
        )
    }

    /**
     * Convert domain weekly schedule to persistence entities
     */
    fun toWeeklyScheduleEntities(workingHours: WorkingHoursSettings): List<WeeklySchedule> {
        return workingHours.weeklySchedule.map { (dayOfWeek, daySchedule) ->
            WeeklySchedule(
                id = UUID.randomUUID().toString(),
                salonId = workingHours.salonId.value,
                dayOfWeek = dayOfWeek.name.lowercase(),
                startTime = daySchedule.startTime ?: LocalTime.of(9, 0),
                endTime = daySchedule.endTime ?: LocalTime.of(17, 0),
                isDayOff = !daySchedule.isWorkingDay,
                lunchBreakEnabled = daySchedule.breakStart != null && daySchedule.breakEnd != null,
                lunchBreakStartTime = daySchedule.breakStart,
                lunchBreakEndTime = daySchedule.breakEnd
            )
        }
    }

    /**
     * Convert domain holidays to persistence entities
     */
    fun toHolidayEntities(workingHours: WorkingHoursSettings): List<HolidayEntity> {
        return workingHours.holidays.map { holiday ->
            HolidayEntity(
                id = holiday.id.value,
                salonId = workingHours.salonId.value,
                name = holiday.name,
                date = holiday.date,
                isWorkingDay = holiday.isWorkingDay,
                type = HolidayTypeEntity.valueOf(holiday.type.name),
                createdAt = holiday.createdAt,
                updatedAt = holiday.updatedAt
            )
        }
    }

    /**
     * Convert domain custom closures to persistence entities
     */
    fun toCustomClosureEntities(workingHours: WorkingHoursSettings): List<CustomClosureEntity> {
        return workingHours.customClosures.map { closure ->
            CustomClosureEntity(
                id = closure.id.value,
                salonId = workingHours.salonId.value,
                reason = closure.reason,
                date = closure.date,
                description = closure.description,
                createdAt = closure.createdAt,
                updatedAt = closure.updatedAt
            )
        }
    }
}
