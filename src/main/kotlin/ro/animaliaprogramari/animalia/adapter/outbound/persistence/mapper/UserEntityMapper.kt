package ro.animaliaprogramari.animalia.adapter.outbound.persistence.mapper

import org.springframework.stereotype.Component
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.User as UserEntity

/**
 * Mapper between User domain model and User JPA entity
 * This handles the translation between the pure domain model and the persistence layer
 */
@Component
class UserEntityMapper {

    /**
     * Convert domain model to JPA entity
     */
    fun toEntity(user: User): UserEntity {
        return UserEntity(
            id = user.id.value,
            firebaseUid = user.firebaseUid,
            email = user.email?.value,
            phoneNumber = user.phoneNumber,
            name = user.name,
            role = user.role.name,
            currentSalonId = user.currentSalonId?.value,
            isActive = user.isActive,
            createdAt = user.createdAt,
            updatedAt = user.updatedAt
        )
    }

    /**
     * Convert JPA entity to domain model
     */
    fun toDomain(entity: UserEntity): User {
        return User(
            id = UserId.of(entity.id),
            firebaseUid = entity.firebaseUid,
            email = entity.email?.let { Email.of(it) },
            phoneNumber = entity.phoneNumber,
            name = entity.name,
            role = UserRole.valueOf(entity.role),
            currentSalonId = entity.currentSalonId?.let { SalonId.of(it) },
            isActive = entity.isActive,
            createdAt = entity.createdAt,
            updatedAt = entity.updatedAt
        )
    }

}
