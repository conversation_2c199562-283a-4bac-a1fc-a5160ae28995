package ro.animaliaprogramari.animalia.adapter.outbound.persistence

import org.springframework.stereotype.Component
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.Appointment as AppointmentEntity
import java.math.BigDecimal

/**
 * Mapper between domain Appointment and JPA AppointmentEntity
 * This handles the translation between the pure domain model and persistence model
 */
@Component
class AppointmentEntityMapper {

    fun toDomain(entity: AppointmentEntity): Appointment {
        return Appointment(
            id = AppointmentId.of(entity.id),
            salonId = SalonId.of(entity.salonId),
            clientId = ClientId.of(entity.clientId),
            petId = PetId.of(entity.petId),
            staffId = StaffId.of(entity.staffId),
            appointmentDate = entity.appointmentDate,
            startTime = entity.startTime,
            endTime = entity.endTime,
            status = AppointmentStatus.valueOf(entity.status.uppercase()),
            serviceIds = entity.serviceIds.map { ServiceId.of(it) },
            totalPrice = Money.of(entity.totalPrice),
            totalDuration = Duration.ofMinutes(entity.totalDurationMinutes),
            notes = entity.notes,
            repetitionFrequency = entity.repetitionFrequency?.let {
                RepetitionFrequency.valueOf(it.uppercase())
            },
            createdAt = entity.createdAt,
            updatedAt = entity.updatedAt,
            version = entity.version
        )
    }

    fun toEntity(domain: Appointment): AppointmentEntity {
        val entity = AppointmentEntity()
        entity.id = domain.id.value
        entity.salonId = domain.salonId.value
        entity.clientId = domain.clientId.value
        entity.petId = domain.petId.value
        entity.staffId = domain.staffId.value
        entity.appointmentDate = domain.appointmentDate
        entity.startTime = domain.startTime
        entity.endTime = domain.endTime
        entity.status = domain.status.name.lowercase()
        entity.serviceIds = domain.serviceIds.map { it.value }
        entity.totalPrice = domain.totalPrice.amount
        entity.totalDurationMinutes = domain.totalDuration.minutes
        entity.notes = domain.notes
        entity.repetitionFrequency = domain.repetitionFrequency?.name?.lowercase()
        entity.createdAt = domain.createdAt
        entity.updatedAt = domain.updatedAt
        entity.version = domain.version
        return entity
    }
}
