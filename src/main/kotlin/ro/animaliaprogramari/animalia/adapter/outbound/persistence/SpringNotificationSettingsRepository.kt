package ro.animaliaprogramari.animalia.adapter.outbound.persistence

import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.NotificationSettings

/**
 * Spring Data JPA repository for notification settings
 */
@Repository
interface SpringNotificationSettingsRepository : JpaRepository<NotificationSettings, String> {

    /**
     * Find notification settings by salon ID
     */
    fun findBySalonId(salonId: String): NotificationSettings?

    /**
     * Check if notification settings exist for a salon
     */
    fun existsBySalonId(salonId: String): Boolean

    /**
     * Delete notification settings by salon ID
     */
    fun deleteBySalonId(salonId: String)
}
