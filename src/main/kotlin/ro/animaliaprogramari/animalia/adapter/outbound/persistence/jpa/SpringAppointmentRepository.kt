package ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa

import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.Appointment
import java.time.LocalDate

@Repository
interface SpringAppointmentRepository : JpaRepository<Appointment, String> {

    fun findByAppointmentDateBetween(startDate: LocalDate, endDate: LocalDate): List<Appointment>

    fun findByClientId(clientId: String): List<Appointment>

    fun findByPetId(petId: String): List<Appointment>

    fun findByStatus(status: String): List<Appointment>

    // Remove problematic query with nullable parameters

    // New salon-specific queries

    // Simple queries without nullable parameters
    fun findBySalonId(salonId: String): List<Appointment>

    fun findByStaffId(staffId: String): List<Appointment>

    fun findBySalonIdAndStaffIdAndAppointmentDate(
        salonId: String,
        staffId: String,
        appointmentDate: LocalDate
    ): List<Appointment>

    fun findByStaffIdAndAppointmentDate(
        staffId: String,
        appointmentDate: LocalDate
    ): List<Appointment>

    fun findByAppointmentDateAndStatus(
        appointmentDate: LocalDate,
        status: String
    ): List<Appointment>

    fun findByStaffIdAndAppointmentDateBetween(
        staffId: String,
        startDate: LocalDate,
        endDate: LocalDate
    ): List<Appointment>

    /**
     * Find appointments by salon ID and date range
     */
    @Query("SELECT a FROM Appointment a WHERE a.salonId = :salonId " +
           "AND a.appointmentDate BETWEEN :startDate AND :endDate")
    fun findBySalonIdAndDateRange(
        @Param("salonId") salonId: String,
        @Param("startDate") startDate: LocalDate,
        @Param("endDate") endDate: LocalDate
    ): List<Appointment>
}
