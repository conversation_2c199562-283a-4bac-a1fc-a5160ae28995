package ro.animaliaprogramari.animalia.adapter.outbound.persistence

import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.ObjectMapper
import org.springframework.stereotype.Component
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.Service as ServiceEntity
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.ServiceCategoryEntity
import java.math.BigDecimal

/**
 * Mapper between domain GroomingService and JPA ServiceEntity
 */
@Component
class GroomingServiceEntityMapper {

    private val objectMapper = ObjectMapper()

    fun toDomain(entity: ServiceEntity): SalonService {
        return SalonService(
            id = ServiceId.of(entity.id),
            salonId = SalonId.of(entity.salonId),
            name = entity.name,
            description = entity.description,
            basePrice = Money.of(entity.price),
            duration = Duration.ofMinutes(entity.duration),
            category = toDomainCategory(entity.category),
            displayOrder = entity.displayOrder,
            requirements = entity.requirements.toList(),
            isActive = entity.isActive,
            // Variable pricing fields
            sizePrices = parseJsonToPriceMap(entity.sizePrices),
            sizeDurations = parseJsonToDurationMap(entity.sizeDurations),
            // Min-max pricing fields
            minPrice = entity.minPrice?.let { Money.of(it) },
            maxPrice = entity.maxPrice?.let { Money.of(it) },
            sizeMinPrices = parseJsonToPriceMap(entity.sizeMinPrices),
            sizeMaxPrices = parseJsonToPriceMap(entity.sizeMaxPrices),
            createdAt = entity.createdAt,
            updatedAt = entity.updatedAt
        )
    }

    fun toEntity(domain: SalonService): ServiceEntity {
        return ServiceEntity(
            id = domain.id.value,
            salonId = domain.salonId.value,
            name = domain.name,
            description = domain.description,
            price = domain.basePrice.amount,
            duration = domain.duration.minutes,
            category = toEntityCategory(domain.category),
            displayOrder = domain.displayOrder,
            requirements = domain.requirements.toMutableList(),
            isActive = domain.isActive,
            // Variable pricing fields
            sizePrices = serializePriceMapToJson(domain.sizePrices),
            sizeDurations = serializeDurationMapToJson(domain.sizeDurations),
            // Min-max pricing fields
            minPrice = domain.minPrice?.amount,
            maxPrice = domain.maxPrice?.amount,
            sizeMinPrices = serializePriceMapToJson(domain.sizeMinPrices),
            sizeMaxPrices = serializePriceMapToJson(domain.sizeMaxPrices),
            createdAt = domain.createdAt,
            updatedAt = domain.updatedAt
        )
    }

    /**
     * Convert domain category to entity category
     */
    private fun toDomainCategory(category: ServiceCategoryEntity): ServiceCategory {
        return when (category) {
            ServiceCategoryEntity.GROOMING -> ServiceCategory.GROOMING
            ServiceCategoryEntity.BATHING -> ServiceCategory.BATHING
            ServiceCategoryEntity.STYLING -> ServiceCategory.STYLING
            ServiceCategoryEntity.NAIL_CARE -> ServiceCategory.NAIL_CARE
            ServiceCategoryEntity.DENTAL_CARE -> ServiceCategory.DENTAL_CARE
            ServiceCategoryEntity.SPECIALTY -> ServiceCategory.SPECIALTY
            ServiceCategoryEntity.PACKAGE -> ServiceCategory.PACKAGE
        }
    }

    /**
     * Convert entity category to domain category
     */
    private fun toEntityCategory(category: ServiceCategory): ServiceCategoryEntity {
        return when (category) {
            ServiceCategory.GROOMING -> ServiceCategoryEntity.GROOMING
            ServiceCategory.BATHING -> ServiceCategoryEntity.BATHING
            ServiceCategory.STYLING -> ServiceCategoryEntity.STYLING
            ServiceCategory.NAIL_CARE -> ServiceCategoryEntity.NAIL_CARE
            ServiceCategory.DENTAL_CARE -> ServiceCategoryEntity.DENTAL_CARE
            ServiceCategory.SPECIALTY -> ServiceCategoryEntity.SPECIALTY
            ServiceCategory.PACKAGE -> ServiceCategoryEntity.PACKAGE
        }
    }

    /**
     * Parse JSON string to price map
     */
    private fun parseJsonToPriceMap(json: String?): Map<String, Money> {
        return if (json.isNullOrBlank()) {
            emptyMap()
        } else {
            try {
                val typeRef = object : TypeReference<Map<String, BigDecimal>>() {}
                val bigDecimalMap = objectMapper.readValue(json, typeRef)
                bigDecimalMap.mapValues { Money.of(it.value) }
            } catch (e: Exception) {
                emptyMap()
            }
        }
    }

    /**
     * Parse JSON string to duration map
     */
    private fun parseJsonToDurationMap(json: String?): Map<String, Duration> {
        return if (json.isNullOrBlank()) {
            emptyMap()
        } else {
            try {
                val typeRef = object : TypeReference<Map<String, Int>>() {}
                val intMap = objectMapper.readValue(json, typeRef)
                intMap.mapValues { Duration.ofMinutes(it.value) }
            } catch (e: Exception) {
                emptyMap()
            }
        }
    }

    /**
     * Serialize price map to JSON string
     */
    private fun serializePriceMapToJson(priceMap: Map<String, Money>?): String? {
        return if (priceMap.isNullOrEmpty()) {
            null
        } else {
            try {
                val bigDecimalMap = priceMap.mapValues { it.value.amount }
                objectMapper.writeValueAsString(bigDecimalMap)
            } catch (e: Exception) {
                null
            }
        }
    }

    /**
     * Serialize duration map to JSON string
     */
    private fun serializeDurationMapToJson(durationMap: Map<String, Duration>?): String? {
        return if (durationMap.isNullOrEmpty()) {
            null
        } else {
            try {
                val intMap = durationMap.mapValues { it.value.minutes }
                objectMapper.writeValueAsString(intMap)
            } catch (e: Exception) {
                null
            }
        }
    }
}
