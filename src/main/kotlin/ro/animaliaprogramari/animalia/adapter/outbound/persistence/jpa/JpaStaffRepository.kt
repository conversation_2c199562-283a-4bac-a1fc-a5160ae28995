package ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa

import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.StaffEntity
import ro.animaliaprogramari.animalia.domain.model.SalonId
import ro.animaliaprogramari.animalia.domain.model.StaffId
import ro.animaliaprogramari.animalia.domain.model.UserId
import java.time.LocalDate

/**
 * JPA repository interface for Staff entities
 * Provides optimized queries for staff management
 */
@Repository
interface JpaStaffRepository : JpaRepository<StaffEntity, String> {

    /**
     * Find all staff by user ID (for users who work in multiple salons)
     */
    fun findAllByUserId(userId: UserId): List<StaffEntity>

    /**
     * Find staff by user ID and salon ID
     */
    fun findByUserIdAndSalonId(userId: UserId, salonId: SalonId): StaffEntity?

    /**
     * Find staff by salon ID and active status
     */
    fun findBySalonIdAndIsActive(salonId: SalonId, isActive: Boolean): List<StaffEntity>

    /**
     * Find staff by salon ID and role with pagination
     */
    fun findBySalonIdAndRoleOrderByHireDateAsc(
        salonId: SalonId,
        role: String,
        pageable: Pageable
    ): Page<StaffEntity>

    /**
     * Count active staff by salon
     */
    fun countBySalonIdAndIsActive(salonId: SalonId, isActive: Boolean): Long

    /**
     * Find staff hired within date range
     */
    fun findBySalonIdAndHireDateBetweenOrderByHireDateDesc(
        salonId: SalonId,
        startDate: LocalDate,
        endDate: LocalDate
    ): List<StaffEntity>

    /**
     * Find staff by specializations
     */
    @Query("""
        SELECT s FROM StaffEntity s
        WHERE s.salonId = :salonId
        AND s.isActive = true
        AND EXISTS (
            SELECT spec FROM s.specializations spec
            WHERE spec IN :specializations
        )
        ORDER BY s.role DESC
    """)
    fun findBySalonIdAndSpecializations(
        @Param("salonId") salonId: SalonId,
        @Param("specializations") specializations: List<String>
    ): List<StaffEntity>

}
