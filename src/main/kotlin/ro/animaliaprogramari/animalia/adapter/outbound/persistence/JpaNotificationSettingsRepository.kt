package ro.animaliaprogramari.animalia.adapter.outbound.persistence

import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.mapper.NotificationSettingsEntityMapper
import ro.animaliaprogramari.animalia.application.port.outbound.NotificationSettingsRepository
import ro.animaliaprogramari.animalia.domain.model.NotificationSettings
import ro.animaliaprogramari.animalia.domain.model.SalonId

/**
 * JPA adapter implementing the NotificationSettingsRepository port
 */
@Repository
class JpaNotificationSettingsRepository(
    private val springRepository: SpringNotificationSettingsRepository,
    private val mapper: NotificationSettingsEntityMapper
) : NotificationSettingsRepository {

    override fun save(settings: NotificationSettings): NotificationSettings {
        val entity = mapper.toEntity(settings)
        val savedEntity = springRepository.save(entity)
        return mapper.toDomain(savedEntity)
    }

    override fun findBySalonId(salonId: SalonId): NotificationSettings? {
        return springRepository.findBySalonId(salonId.value)
            ?.let { mapper.toDomain(it) }
    }

    override fun existsBySalonId(salonId: SalonId): Boolean {
        return springRepository.existsBySalonId(salonId.value)
    }

    override fun deleteBySalonId(salonId: SalonId) {
        springRepository.deleteBySalonId(salonId.value)
    }
}
