package ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa

import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.WeeklySchedule

/**
 * Spring Data JPA repository for weekly schedule
 */
@Repository
interface SpringWeeklyScheduleRepository : JpaRepository<WeeklySchedule, String> {

    /**
     * Find weekly schedule by salon ID
     */
    fun findBySalonIdOrderByDayOfWeek(salonId: String): List<WeeklySchedule>

    /**
     * Delete weekly schedule by salon ID
     */
    fun deleteBySalonId(salonId: String)
}
