package ro.animaliaprogramari.animalia.adapter.outbound.persistence.converter

import jakarta.persistence.AttributeConverter
import jakarta.persistence.Converter
import ro.animaliaprogramari.animalia.domain.model.SoundPreference

/**
 * JPA converter for SoundPreference enum
 * Converts between enum values and their lowercase string representation in the database
 */
@Converter(autoApply = true)
class SoundPreferenceConverter : AttributeConverter<SoundPreference, String> {
    
    override fun convertToDatabaseColumn(attribute: SoundPreference?): String {
        return attribute?.displayName?.lowercase() ?: "default"
    }
    
    override fun convertToEntityAttribute(dbData: String?): SoundPreference {
        return dbData?.let { SoundPreference.fromString(it) } ?: SoundPreference.DEFAULT
    }
}