package ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity

import jakarta.persistence.*
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Size
import java.time.LocalDate
import java.time.LocalDateTime

/**
 * JPA entity for custom closures
 * This is an infrastructure concern - data representation for persistence
 */
@Entity
@Table(
    name = "custom_closures",
    uniqueConstraints = [UniqueConstraint(columnNames = ["salon_id", "date"])]
)
data class CustomClosure(
    @Id
    val id: String,

    @field:NotBlank(message = "Salon ID is required")
    @Column(name = "salon_id", nullable = false)
    val salonId: String,

    @field:NotBlank(message = "Closure reason is required")
    @field:Size(max = 255, message = "Reason must not exceed 255 characters")
    @Column(nullable = false)
    val reason: String,

    @field:NotNull(message = "Closure date is required")
    @Column(nullable = false)
    val date: LocalDate,

    @field:Size(max = 1000, message = "Description must not exceed 1000 characters")
    @Column(columnDefinition = "TEXT")
    val description: String? = null,

    @Column(name = "created_at")
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @Column(name = "updated_at")
    var updatedAt: LocalDateTime = LocalDateTime.now()
) {
    // Default constructor for JPA
    constructor() : this(
        "", "", "", LocalDate.now(), null,
        LocalDateTime.now(), LocalDateTime.now()
    )
}
