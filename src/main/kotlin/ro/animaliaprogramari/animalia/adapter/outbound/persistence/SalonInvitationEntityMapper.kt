package ro.animaliaprogramari.animalia.adapter.outbound.persistence

import com.fasterxml.jackson.databind.ObjectMapper
import org.springframework.stereotype.Component
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.StaffRoleEntity
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SalonInvitation as SalonInvitationEntity

/**
 * Mapper between domain SalonInvitation and JPA SalonInvitationEntity
 */
@Component
class SalonInvitationEntityMapper(
    private val objectMapper: ObjectMapper
) {

    /**
     * Convert domain model to JPA entity
     */
    fun toEntity(invitation: SalonInvitation): SalonInvitationEntity {
        return SalonInvitationEntity(
            id = invitation.id.value,
            salonId = invitation.salonId.value,
            inviterUserId = invitation.inviterUserId.value,
            invitedUserPhone = invitation.invitedUserPhone,
            proposedRole = toEntityStaffRole(invitation.proposedRole),
            proposedPermissions = objectMapper.writeValueAsString(invitation.proposedPermissions),
            proposedClientDataPermission = toClientDataPermissionString(invitation.proposedPermissions),
            status = invitation.status,
            message = invitation.message,
            invitedAt = invitation.invitedAt,
            respondedAt = invitation.respondedAt,
            expiresAt = invitation.expiresAt,
            resendCount = invitation.resendCount,
            lastResendAt = invitation.lastResendAt,
            cancelledAt = invitation.cancelledAt,
            cancelledBy = invitation.cancelledBy?.value,
            createdAt = invitation.createdAt,
            updatedAt = invitation.updatedAt
        )
    }

    /**
     * Convert JPA entity to domain model
     */
    fun toDomain(entity: SalonInvitationEntity): SalonInvitation {
        // Handle both new and legacy permission formats
        val permissions = if (entity.proposedPermissions != null) {
            objectMapper.readValue(entity.proposedPermissions, StaffPermissions::class.java)
        } else {
            // Fallback to legacy client data permission
            StaffPermissions.fromClientDataPermission(entity.proposedClientDataPermission)
        }

        return SalonInvitation(
            id = InvitationId.of(entity.id),
            salonId = SalonId.of(entity.salonId),
            inviterUserId = UserId.of(entity.inviterUserId),
            invitedUserPhone = entity.invitedUserPhone,
            proposedRole = toDomainStaffRole(entity.proposedRole),
            proposedPermissions = permissions,
            status = entity.status,
            message = entity.message,
            invitedAt = entity.invitedAt,
            respondedAt = entity.respondedAt,
            expiresAt = entity.expiresAt,
            resendCount = entity.resendCount,
            lastResendAt = entity.lastResendAt,
            cancelledAt = entity.cancelledAt,
            cancelledBy = entity.cancelledBy?.let { UserId.of(it) },
            createdAt = entity.createdAt,
            updatedAt = entity.updatedAt
        )
    }

    private fun toEntityStaffRole(role: StaffRole): StaffRoleEntity {
        return when (role) {
            StaffRole.CHIEF_GROOMER -> StaffRoleEntity.CHIEF_GROOMER
            StaffRole.GROOMER -> StaffRoleEntity.GROOMER
            StaffRole.SENIOR_GROOMER -> StaffRoleEntity.GROOMER // Map to GROOMER for entity compatibility
            StaffRole.ASSISTANT -> StaffRoleEntity.GROOMER // Map to GROOMER for entity compatibility
        }
    }

    private fun toDomainStaffRole(role: StaffRoleEntity): StaffRole {
        return when (role) {
            StaffRoleEntity.CHIEF_GROOMER -> StaffRole.CHIEF_GROOMER
            StaffRoleEntity.GROOMER -> StaffRole.GROOMER
            StaffRoleEntity.RECEPTIONIST -> StaffRole.GROOMER
            StaffRoleEntity.MANAGER -> StaffRole.CHIEF_GROOMER
            StaffRoleEntity.ADMIN -> StaffRole.CHIEF_GROOMER
        }
    }

    /**
     * Convert StaffPermissions to legacy client data permission string
     */
    private fun toClientDataPermissionString(permissions: StaffPermissions): String {
        return when (permissions.clientDataAccess) {
            ClientDataAccess.FULL -> "FULL_ACCESS"
            ClientDataAccess.LIMITED -> "LIMITED_ACCESS"
            ClientDataAccess.NONE -> "NO_ACCESS"
        }
    }
}
