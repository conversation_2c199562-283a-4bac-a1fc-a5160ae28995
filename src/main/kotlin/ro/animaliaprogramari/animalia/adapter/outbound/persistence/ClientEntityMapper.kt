package ro.animaliaprogramari.animalia.adapter.outbound.persistence

import org.springframework.stereotype.Component
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.Client as ClientEntity

/**
 * Mapper between domain Client and JPA ClientEntity
 * This handles the translation between pure domain model and persistence model
 */
@Component
class ClientEntityMapper {

    /**
     * Convert JPA entity to domain model
     */
    fun toDomain(entity: ClientEntity): Client {
        return Client(
            id = ClientId.of(entity.id),
            name = entity.name,
            phone = entity.phone?.let { PhoneNumber.of(it) },
            email = entity.email?.let { Email.of(it) },
            address = entity.address,
            notes = entity.notes,
            isActive = entity.isActive,
            createdAt = entity.createdAt,
            updatedAt = entity.updatedAt
        )
    }

    /**
     * Convert domain model to JPA entity
     * Note: salonId can be provided separately since domain model doesn't track it
     */
    fun toEntity(domain: Client, salonId: String? = null): ClientEntity {
        return ClientEntity(
            id = domain.id.value,
            salonId = salonId,
            name = domain.name,
            phone = domain.phone?.value,
            email = domain.email?.value,
            address = domain.address,
            notes = domain.notes,
            isActive = domain.isActive,
            createdAt = domain.createdAt,
            updatedAt = domain.updatedAt,
            pets = emptyList() // Pets are handled separately
        )
    }
}
