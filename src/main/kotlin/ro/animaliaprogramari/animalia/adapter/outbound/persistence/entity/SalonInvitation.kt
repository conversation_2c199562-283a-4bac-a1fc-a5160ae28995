package ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity

import jakarta.persistence.*
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.Size
import ro.animaliaprogramari.animalia.domain.model.InvitationStatus
import java.time.LocalDateTime

@Entity
@Table(
    name = "salon_invitations",
    indexes = [
        Index(name = "idx_salon_invitations_salon_id", columnList = "salon_id"),
        Index(name = "idx_salon_invitations_inviter_user_id", columnList = "inviter_user_id"),
        Index(name = "idx_salon_invitations_invited_user_phone", columnList = "invited_user_phone"),
        Index(name = "idx_salon_invitations_status", columnList = "status"),
        Index(name = "idx_salon_invitations_expires_at", columnList = "expires_at")
    ]
)
data class SalonInvitation(
    @Id
    val id: String,

    @field:NotBlank(message = "Salon ID is required")
    @Column(name = "salon_id", nullable = false)
    val salonId: String,

    @field:NotBlank(message = "Inviter user ID is required")
    @Column(name = "inviter_user_id", nullable = false)
    val inviterUserId: String,

    @field:NotBlank(message = "Invited user phone is required")
    @field:Size(max = 50, message = "Phone must not exceed 50 characters")
    @Column(name = "invited_user_phone", nullable = false)
    val invitedUserPhone: String,

    @Enumerated(EnumType.STRING)
    @Column(name = "proposed_role", nullable = false)
    val proposedRole: StaffRoleEntity,

    @Column(name = "proposed_permissions", nullable = true, columnDefinition = "TEXT")
    val proposedPermissions: String?, // JSON representation of StaffPermissions

    @Column(name = "proposed_client_data_permission", nullable = false)
    val proposedClientDataPermission: String, // Legacy field for backward compatibility

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    val status: InvitationStatus,

    @field:Size(max = 500, message = "Message must not exceed 500 characters")
    @Column(name = "message", columnDefinition = "TEXT")
    val message: String?,

    @Column(name = "invited_at", nullable = false)
    val invitedAt: LocalDateTime,

    @Column(name = "responded_at")
    val respondedAt: LocalDateTime?,

    @Column(name = "expires_at", nullable = false)
    val expiresAt: LocalDateTime,

    @Column(name = "resend_count", nullable = false)
    val resendCount: Int = 0,

    @Column(name = "last_resend_at")
    val lastResendAt: LocalDateTime?,

    @Column(name = "cancelled_at")
    val cancelledAt: LocalDateTime?,

    @Column(name = "cancelled_by")
    val cancelledBy: String?,

    @Column(name = "created_at", nullable = false)
    val createdAt: LocalDateTime,

    @Column(name = "updated_at", nullable = false)
    var updatedAt: LocalDateTime
) {
    // Default constructor for JPA
    constructor() : this(
        "", "", "", "", StaffRoleEntity.GROOMER,
        "{\"clientDataAccess\":\"NONE\",\"canManageAppointments\":false,\"canManageServices\":false,\"canViewReports\":false,\"canManageSchedule\":false}",
        "NO_ACCESS",
        InvitationStatus.PENDING, null,
        LocalDateTime.now(), null, LocalDateTime.now().plusDays(7),
        0, null, null, null,
        LocalDateTime.now(), LocalDateTime.now()
    )
}
