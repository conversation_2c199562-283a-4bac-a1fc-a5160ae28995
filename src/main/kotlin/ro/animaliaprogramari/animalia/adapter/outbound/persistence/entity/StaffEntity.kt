package ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity

import jakarta.persistence.*
import org.hibernate.annotations.JdbcTypeCode
import org.hibernate.type.SqlTypes
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

@Entity
@Table(name = "staff")
class StaffEntity(
    @Id
    val id: String,

    @Column(name = "user_id", nullable = false)
    val userId: String,

    @Column(name = "nickname", nullable = true)
    val nickName: String? = null,

    @Column(name = "salon_id", nullable = false)
    val salonId: String,

    @Column(name = "role", nullable = false)
    @Enumerated(EnumType.STRING)
    val role: StaffRoleEntity,

    @Column(name = "working_hours", columnDefinition = "TEXT")
    val workingHours: String, // JSON representation as TEXT for H2 compatibility

    @Column(name = "hire_date", nullable = false)
    val hireDate: LocalDate,

    @ElementCollection(fetch = FetchType.EAGER)
    @CollectionTable(
        name = "staff_specializations",
        joinColumns = [Join<PERSON><PERSON>umn(name = "staff_id")]
    )
    @Enumerated(EnumType.STRING)
    @Column(name = "specialization")
    val specializations: Set<SpecializationEntity>,

    @Column(name = "is_active", nullable = false, columnDefinition = "BOOLEAN DEFAULT true")
    val isActive: Boolean = true,

//    @Column(name = "performance", columnDefinition = "jsonb")
//    val performance: String? = null, // JSON representation

    @Column(name = "created_at", nullable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @Column(name = "updated_at", nullable = false)
    val updatedAt: LocalDateTime = LocalDateTime.now()
) {
    // Default constructor for JPA
    constructor() : this(
        id = UUID.randomUUID().toString(),
        userId = UUID.randomUUID().toString(),
        nickName = null,
        salonId = UUID.randomUUID().toString(),
        role = StaffRoleEntity.GROOMER,
        workingHours = "{}",
        hireDate = LocalDate.now(),
        specializations = emptySet(),
        isActive = true,
        createdAt = LocalDateTime.now(),
        updatedAt = LocalDateTime.now()
    )
}

enum class StaffRoleEntity {
    GROOMER,
    CHIEF_GROOMER,
    RECEPTIONIST,
    MANAGER,
    ADMIN
}

enum class SpecializationEntity {
    DOG_GROOMING,
    CAT_GROOMING,
    EXOTIC_PETS,
    NAIL_TRIMMING,
    DENTAL_CARE,
    FUR_STYLING,
    MEDICAL_GROOMING,
    PUPPY_GROOMING,
    SENIOR_PET_CARE
}