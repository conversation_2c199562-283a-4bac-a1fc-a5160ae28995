package ro.animaliaprogramari.animalia.adapter.outbound.persistence

import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa.SpringSalonInvitationRepository
import ro.animaliaprogramari.animalia.application.port.outbound.SalonInvitationRepository
import ro.animaliaprogramari.animalia.domain.model.*

/**
 * JPA adapter implementing the SalonInvitationRepository port
 * This adapter translates between domain models and JPA entities
 */
@Repository
class JpaSalonInvitationRepository(
    private val springRepository: SpringSalonInvitationRepository,
    private val invitationMapper: SalonInvitationEntityMapper
) : SalonInvitationRepository {

    override fun save(invitation: SalonInvitation): SalonInvitation {
        val entity = invitationMapper.toEntity(invitation)
        val savedEntity = springRepository.save(entity)
        return invitationMapper.toDomain(savedEntity)
    }

    override fun findById(id: InvitationId): SalonInvitation? {
        return springRepository.findById(id.value)
            .map { invitationMapper.toDomain(it) }
            .orElse(null)
    }

    override fun findPendingByInvitedUserPhone(phoneNumber: String): List<SalonInvitation> {
        return springRepository.findPendingByInvitedUserPhone(phoneNumber)
            .map { invitationMapper.toDomain(it) }
    }

    override fun findBySalonId(salonId: SalonId): List<SalonInvitation> {
        return springRepository.findBySalonId(salonId.value)
            .map { invitationMapper.toDomain(it) }
    }

    override fun findByInviterUserId(inviterUserId: UserId): List<SalonInvitation> {
        return springRepository.findByInviterUserId(inviterUserId.value)
            .map { invitationMapper.toDomain(it) }
    }

    override fun findPendingBySalonId(salonId: SalonId): List<SalonInvitation> {
        return springRepository.findPendingBySalonId(salonId.value)
            .map { invitationMapper.toDomain(it) }
    }

    override fun existsPendingInvitation(salonId: SalonId, phoneNumber: String): Boolean {
        return springRepository.existsPendingInvitation(salonId.value, phoneNumber)
    }

    override fun findExpiredInvitations(): List<SalonInvitation> {
        return springRepository.findExpiredInvitations()
            .map { invitationMapper.toDomain(it) }
    }

    override fun deleteById(id: InvitationId): Boolean {
        return if (springRepository.existsById(id.value)) {
            springRepository.deleteById(id.value)
            true
        } else {
            false
        }
    }

    override fun countByStatusAndSalonId(status: InvitationStatus, salonId: SalonId): Long {
        return springRepository.countByStatusAndSalonId(status, salonId.value)
    }
}
