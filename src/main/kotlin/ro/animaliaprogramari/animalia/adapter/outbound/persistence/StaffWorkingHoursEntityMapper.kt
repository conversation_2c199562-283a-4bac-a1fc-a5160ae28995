package ro.animaliaprogramari.animalia.adapter.outbound.persistence

import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.ObjectMapper
import org.springframework.stereotype.Component
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.StaffWorkingHoursSettingsEntity
import ro.animaliaprogramari.animalia.domain.model.*
import java.time.DayOfWeek
import java.util.*

/**
 * Mapper between StaffWorkingHoursSettings domain model and JPA entity
 * Handles JSON serialization/deserialization for complex fields
 */
@Component
class StaffWorkingHoursEntityMapper(
    private val objectMapper: ObjectMapper
) {

    /**
     * Convert JPA entity to domain model
     */
    fun toDomain(entity: StaffWorkingHoursSettingsEntity): StaffWorkingHoursSettings {
        val weeklySchedule = deserializeWeeklySchedule(entity.weeklySchedule)
        val holidays = deserializeHolidays(entity.holidays)
        val customClosures = deserializeCustomClosures(entity.customClosures)

        return StaffWorkingHoursSettings(
            staffId = StaffId.of(entity.staffId),
            salonId = SalonId.of(entity.salonId),
            weeklySchedule = weeklySchedule,
            holidays = holidays,
            customClosures = customClosures,
            updatedAt = entity.updatedAt
        )
    }

    /**
     * Convert domain model to JPA entity
     */
    fun toEntity(domain: StaffWorkingHoursSettings): StaffWorkingHoursSettingsEntity {
        val weeklyScheduleJson = serializeWeeklySchedule(domain.weeklySchedule)
        val holidaysJson = serializeHolidays(domain.holidays)
        val customClosuresJson = serializeCustomClosures(domain.customClosures)

        return StaffWorkingHoursSettingsEntity(
            id = "${domain.staffId.value}_${domain.salonId.value}",
            staffId = domain.staffId.value,
            salonId = domain.salonId.value,
            weeklySchedule = weeklyScheduleJson,
            holidays = holidaysJson,
            customClosures = customClosuresJson,
            updatedAt = domain.updatedAt
        )
    }

    private fun serializeWeeklySchedule(schedule: Map<DayOfWeek, DaySchedule>): String {
        val scheduleMap = schedule.mapKeys { it.key.name }
        return objectMapper.writeValueAsString(scheduleMap)
    }

    private fun deserializeWeeklySchedule(json: String): Map<DayOfWeek, DaySchedule> {
        if (json.isBlank() || json == "{}") {
            return emptyMap()
        }
        
        val typeRef = object : TypeReference<Map<String, Map<String, Any?>>>() {}
        val scheduleMap = objectMapper.readValue(json, typeRef)
        
        return scheduleMap.mapKeys { DayOfWeek.valueOf(it.key) }
            .mapValues { parseDaySchedule(it.value) }
    }

    private fun parseDaySchedule(data: Map<String, Any?>): DaySchedule {
        val isWorkingDay = data["isWorkingDay"] as? Boolean ?: false
        val startTime = (data["startTime"] as? String)?.let { java.time.LocalTime.parse(it) }
        val endTime = (data["endTime"] as? String)?.let { java.time.LocalTime.parse(it) }
        val breakStart = (data["breakStart"] as? String)?.let { java.time.LocalTime.parse(it) }
        val breakEnd = (data["breakEnd"] as? String)?.let { java.time.LocalTime.parse(it) }

        return DaySchedule(isWorkingDay, startTime, endTime, breakStart, breakEnd)
    }

    private fun serializeHolidays(holidays: List<StaffHoliday>): String {
        return objectMapper.writeValueAsString(holidays)
    }

    private fun deserializeHolidays(json: String): List<StaffHoliday> {
        if (json.isBlank() || json == "[]") {
            return emptyList()
        }
        
        val typeRef = object : TypeReference<List<Map<String, Any?>>>() {}
        val holidayMaps = objectMapper.readValue(json, typeRef)
        
        return holidayMaps.map { parseStaffHoliday(it) }
    }

    private fun parseStaffHoliday(data: Map<String, Any?>): StaffHoliday {
        val id = StaffHolidayId.of(data["id"] as String)
        val salonId = SalonId.of(data["salonId"] as String)
        val name = data["name"] as String
        val date = java.time.LocalDate.parse(data["date"] as String)
        val isWorkingDay = data["isWorkingDay"] as? Boolean ?: false
        val type = HolidayType.valueOf(data["type"] as String)
        val createdAt = java.time.LocalDateTime.parse(data["createdAt"] as String)
        val updatedAt = java.time.LocalDateTime.parse(data["updatedAt"] as String)

        return StaffHoliday(id, salonId, name, date, isWorkingDay, type, createdAt, updatedAt)
    }

    private fun serializeCustomClosures(closures: List<StaffCustomClosure>): String {
        return objectMapper.writeValueAsString(closures)
    }

    private fun deserializeCustomClosures(json: String): List<StaffCustomClosure> {
        if (json.isBlank() || json == "[]") {
            return emptyList()
        }
        
        val typeRef = object : TypeReference<List<Map<String, Any?>>>() {}
        val closureMaps = objectMapper.readValue(json, typeRef)
        
        return closureMaps.map { parseStaffCustomClosure(it) }
    }

    private fun parseStaffCustomClosure(data: Map<String, Any?>): StaffCustomClosure {
        val id = StaffCustomClosureId.of(data["id"] as String)
        val salonId = SalonId.of(data["salonId"] as String)
        val reason = data["reason"] as String
        val date = java.time.LocalDate.parse(data["date"] as String)
        val description = data["description"] as? String
        val createdAt = java.time.LocalDateTime.parse(data["createdAt"] as String)
        val updatedAt = java.time.LocalDateTime.parse(data["updatedAt"] as String)

        return StaffCustomClosure(id, salonId, reason, date, description, createdAt, updatedAt)
    }
}
