package ro.animaliaprogramari.animalia.adapter.outbound.persistence

import org.springframework.stereotype.Component
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.Pet as PetEntity

/**
 * Mapper between domain Pet and JPA PetEntity
 */
@Component
class PetEntityMapper {

    fun toDomain(entity: PetEntity): Pet {
        return Pet(
            id = PetId.of(entity.id),
            clientId = ClientId.of(entity.clientId),
            name = entity.name,
            breed = entity.breed,
            age = entity.age,
            weight = entity.weight,
            color = entity.color,
            gender = entity.gender?.let { Gender.valueOf(it.uppercase()) },
            notes = entity.notes,
            medicalConditions = entity.medicalConditions,
            isActive = entity.isActive,
            createdAt = entity.createdAt,
            updatedAt = entity.updatedAt,
            size = entity.size,
            species = entity.species
        )
    }
    
    fun toEntity(domain: Pet): PetEntity {
        return PetEntity(
            id = domain.id.value,
            clientId = domain.clientId.value,
            name = domain.name,
            breed = domain.breed,
            species = domain.species,
            size = domain.size,
            age = domain.age,
            weight = domain.weight,
            color = domain.color,
            gender = domain.gender?.name?.lowercase(),
            notes = domain.notes,
            medicalConditions = domain.medicalConditions,
            isActive = domain.isActive,
            createdAt = domain.createdAt,
            updatedAt = domain.updatedAt
        )
    }
}
