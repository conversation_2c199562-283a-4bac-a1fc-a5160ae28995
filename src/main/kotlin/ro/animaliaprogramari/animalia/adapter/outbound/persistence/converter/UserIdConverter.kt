package ro.animaliaprogramari.animalia.adapter.outbound.persistence.converter

import jakarta.persistence.AttributeConverter
import jakarta.persistence.Converter
import ro.animaliaprogramari.animalia.domain.model.UserId

/**
 * JPA converter for UserId value object
 * Converts between UserId and its String representation in the database
 */
@Converter(autoApply = true)
class UserIdConverter : AttributeConverter<UserId, String> {
    
    override fun convertToDatabaseColumn(attribute: UserId?): String? {
        return attribute?.value
    }
    
    override fun convertToEntityAttribute(dbData: String?): UserId? {
        return dbData?.let { 
            if (it.isBlank()) null else UserId.of(it) 
        }
    }
}
