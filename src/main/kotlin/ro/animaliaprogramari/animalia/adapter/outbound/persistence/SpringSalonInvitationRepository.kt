package ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa

import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SalonInvitation
import ro.animaliaprogramari.animalia.domain.model.InvitationStatus
import java.time.LocalDateTime

/**
 * Spring Data JPA repository for SalonInvitation entities
 */
@Repository
interface SpringSalonInvitationRepository : JpaRepository<SalonInvitation, String> {

    /**
     * Find pending invitations for a user by phone number
     */
    @Query("SELECT i FROM SalonInvitation i WHERE i.invitedUserPhone = :phoneNumber AND i.status = 'PENDING' AND i.expiresAt > :now ORDER BY i.invitedAt DESC")
    fun findPendingByInvitedUserPhone(
        @Param("phoneNumber") phoneNumber: String,
        @Param("now") now: LocalDateTime = LocalDateTime.now()
    ): List<SalonInvitation>

    /**
     * Find invitations sent by a salon
     */
    @Query("SELECT i FROM SalonInvitation i WHERE i.salonId = :salonId ORDER BY i.invitedAt DESC")
    fun findBySalonId(@Param("salonId") salonId: String): List<SalonInvitation>

    /**
     * Find invitations sent by a specific user
     */
    @Query("SELECT i FROM SalonInvitation i WHERE i.inviterUserId = :inviterUserId ORDER BY i.invitedAt DESC")
    fun findByInviterUserId(@Param("inviterUserId") inviterUserId: String): List<SalonInvitation>

    /**
     * Find pending invitations for a salon
     */
    @Query("SELECT i FROM SalonInvitation i WHERE i.salonId = :salonId AND i.status = 'PENDING' AND i.expiresAt > :now ORDER BY i.invitedAt DESC")
    fun findPendingBySalonId(
        @Param("salonId") salonId: String,
        @Param("now") now: LocalDateTime = LocalDateTime.now()
    ): List<SalonInvitation>

    /**
     * Check if there's already a pending invitation for the same phone and salon
     */
    @Query("SELECT COUNT(i) > 0 FROM SalonInvitation i WHERE i.salonId = :salonId AND i.invitedUserPhone = :phoneNumber AND i.status = 'PENDING' AND i.expiresAt > :now")
    fun existsPendingInvitation(
        @Param("salonId") salonId: String,
        @Param("phoneNumber") phoneNumber: String,
        @Param("now") now: LocalDateTime = LocalDateTime.now()
    ): Boolean

    /**
     * Find expired invitations that need to be marked as expired
     */
    @Query("SELECT i FROM SalonInvitation i WHERE i.status = 'PENDING' AND i.expiresAt <= :now")
    fun findExpiredInvitations(@Param("now") now: LocalDateTime = LocalDateTime.now()): List<SalonInvitation>

    /**
     * Count invitations by status for a salon
     */
    @Query("SELECT COUNT(i) FROM SalonInvitation i WHERE i.status = :status AND i.salonId = :salonId")
    fun countByStatusAndSalonId(
        @Param("status") status: InvitationStatus,
        @Param("salonId") salonId: String
    ): Long
}
