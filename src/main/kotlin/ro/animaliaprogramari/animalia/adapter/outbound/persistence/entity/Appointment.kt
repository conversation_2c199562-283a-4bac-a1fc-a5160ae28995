package ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity

import jakarta.persistence.*
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull
import org.hibernate.annotations.JdbcTypeCode
import org.hibernate.type.SqlTypes
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime

@Entity
@Table(name = "appointments")
class Appointment(
    @Id
    var id: String = "",

    @field:NotBlank(message = "Salon ID is required")
    @Column(name = "salon_id", nullable = false)
    var salonId: String = "",

    @field:NotBlank(message = "Client ID is required")
    @Column(name = "client_id", nullable = false)
    var clientId: String = "",

    @field:NotBlank(message = "Pet ID is required")
    @Column(name = "pet_id", nullable = false)
    var petId: String = "",

    @field:NotBlank(message = "Staff ID is required")
    @Column(name = "staff_id", nullable = false)
    var staffId: String = "",

    @field:NotNull(message = "Appointment date is required")
    @Column(name = "appointment_date", nullable = false)
    var appointmentDate: LocalDate = LocalDate.now(),

    @field:NotNull(message = "Start time is required")
    @Column(name = "start_time", nullable = false)
    var startTime: LocalTime = LocalTime.now(),

    @field:NotNull(message = "End time is required")
    @Column(name = "end_time", nullable = false)
    var endTime: LocalTime = LocalTime.now(),

    @Column(nullable = false)
    var status: String = "scheduled",

    @ElementCollection
    @CollectionTable(name = "appointment_service_ids",
                    joinColumns = [JoinColumn(name = "appointment_id")])
    @Column(name = "service_id")
    var serviceIds: List<String> = mutableListOf(),

    @Column(name = "total_price", precision = 10, scale = 2, nullable = false)
    var totalPrice: BigDecimal = BigDecimal.ZERO,

    @Column(name = "total_duration", nullable = false)
    var totalDurationMinutes: Int = 0,

    @Column(columnDefinition = "TEXT")
    var notes: String? = null,

    @Column(name = "repetition_frequency")
    var repetitionFrequency: String? = null,

    @Column(name = "created_at", nullable = false)
    var createdAt: LocalDateTime = LocalDateTime.now(),

    @Column(name = "updated_at", nullable = false)
    var updatedAt: LocalDateTime = LocalDateTime.now(),

    @Version
    var version: Long = 0
) {
    // Default constructor for JPA
    constructor() : this("", "", "", "", "", LocalDate.now(), LocalTime.now(), LocalTime.now(), "scheduled", mutableListOf(), BigDecimal.ZERO, 0, null, null, LocalDateTime.now(), LocalDateTime.now(), 0)

    // Remove the circular mapping relationships to avoid conflicts
    // Services will be loaded separately through repository queries when needed

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as Appointment

        if (id != other.id) return false

        return true
    }

    override fun hashCode(): Int {
        return id.hashCode()
    }

    override fun toString(): String {
        return "Appointment(id='$id', salonId='$salonId', clientId='$clientId', petId='$petId', staffId='$staffId', appointmentDate=$appointmentDate, startTime=$startTime, endTime=$endTime, status='$status')"
    }
}
