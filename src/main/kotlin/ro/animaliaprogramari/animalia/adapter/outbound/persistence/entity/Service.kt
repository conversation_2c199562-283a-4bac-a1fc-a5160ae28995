package ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity

import jakarta.persistence.*
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.Positive
import jakarta.validation.constraints.Size
import org.hibernate.annotations.JdbcTypeCode
import org.hibernate.type.SqlTypes
import java.math.BigDecimal
import java.time.LocalDateTime

@Entity
@Table(name = "services")
class Service(
    @Id
    @Column(name = "id", nullable = false)
    var id: String,

    @Column(name = "salon_id", nullable = false)
    var salonId: String,

    @field:NotBlank(message = "Service name is required")
    @field:Size(max = 255, message = "Name must not exceed 255 characters")
    @Column(name = "name", nullable = false, columnDefinition = "VARCHAR(255)")
    var name: String,

    @Column(name = "description", nullable = true, columnDefinition = "TEXT")
    var description: String? = null,

    @field:Positive(message = "Base price must be positive")
    @Column(name = "price", nullable = false, precision = 10, scale = 2)
    var price: BigDecimal,

    @field:Positive(message = "Duration must be positive")
    @Column(name = "duration", nullable = false)
    var duration: Int,

    @Enumerated(EnumType.STRING)
    @Column(name = "category", nullable = false)
    var category: ServiceCategoryEntity,

    @Column(name = "display_order", nullable = false)
    var displayOrder: Int = 0,

    @ElementCollection
    @CollectionTable(
        name = "service_requirements",
        joinColumns = [JoinColumn(name = "service_id")]
    )
    @Column(name = "requirement")
    var requirements: MutableList<String> = mutableListOf(),

    @Column(name = "is_active", nullable = false)
    var isActive: Boolean = true,

    // Variable pricing fields - using TEXT columns for H2 compatibility
    @Column(name = "size_prices", columnDefinition = "TEXT")
    var sizePrices: String? = null, // JSON as String for H2 compatibility

    @Column(name = "size_durations", columnDefinition = "TEXT")
    var sizeDurations: String? = null, // JSON as String for H2 compatibility

    // Min-max pricing fields
    @Column(name = "min_price", nullable = true, precision = 10, scale = 2)
    var minPrice: BigDecimal? = null,

    @Column(name = "max_price", nullable = true, precision = 10, scale = 2)
    var maxPrice: BigDecimal? = null,

    @Column(name = "size_min_prices", columnDefinition = "TEXT")
    var sizeMinPrices: String? = null, // JSON as String for H2 compatibility

    @Column(name = "size_max_prices", columnDefinition = "TEXT")
    var sizeMaxPrices: String? = null, // JSON as String for H2 compatibility

    @Column(name = "created_at", nullable = false)
    var createdAt: LocalDateTime = LocalDateTime.now(),

    @Column(name = "updated_at", nullable = false)
    var updatedAt: LocalDateTime = LocalDateTime.now()
) {
    // Default constructor for JPA
    constructor() : this(
        id = "",
        salonId = "",
        name = "",
        description = null,
        price = BigDecimal.ZERO,
        duration = 0,
        category = ServiceCategoryEntity.GROOMING,
        displayOrder = 0,
        requirements = mutableListOf(),
        isActive = true,
        sizePrices = null,
        sizeDurations = null,
        minPrice = null,
        maxPrice = null,
        sizeMinPrices = null,
        sizeMaxPrices = null,
        createdAt = LocalDateTime.now(),
        updatedAt = LocalDateTime.now()
    )

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as Service

        return id == other.id
    }

    override fun hashCode(): Int {
        return id.hashCode()
    }

    override fun toString(): String {
        return "Service(id='$id', name='$name', salonId='$salonId')"
    }
}

/**
 * JPA enum for service categories
 */
enum class ServiceCategoryEntity {
    GROOMING,
    BATHING,
    STYLING,
    NAIL_CARE,
    DENTAL_CARE,
    SPECIALTY,
    PACKAGE
}