package ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity

import jakarta.persistence.*
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.Size
import java.time.LocalDateTime

@Entity
@Table(name = "users")
data class User(
    @Id
    val id: String,

    @field:NotBlank(message = "Firebase UID is required")
    @Column(name = "firebase_uid", nullable = false, unique = true)
    val firebaseUid: String,

    @field:Email(message = "Email should be valid")
    @field:Size(max = 255, message = "Email must not exceed 255 characters")
    @Column(nullable = true, unique = true)
    val email: String? = null,

    @field:Size(max = 20, message = "Phone number must not exceed 20 characters")
    @Column(name = "phone_number", nullable = true)
    val phoneNumber: String? = null,

    @field:NotBlank(message = "Name is required")
    @field:Size(max = 255, message = "Name must not exceed 255 characters")
    @Column(nullable = false)
    val name: String,

    @Column(length = 50)
    val role: String = "USER",

    @Column(name = "current_salon_id", nullable = true)
    val currentSalonId: String? = null,

    @Column(name = "is_active", columnDefinition = "BOOLEAN DEFAULT true")
    val isActive: Boolean = true,

    @Column(name = "created_at")
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @Column(name = "updated_at")
    var updatedAt: LocalDateTime = LocalDateTime.now()
) {
    // Default constructor for JPA
    constructor() : this("", "", null, null, "", "USER", null, true, LocalDateTime.now(), LocalDateTime.now())
}