package ro.animaliaprogramari.animalia.adapter.outbound.persistence

import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.application.port.outbound.SalonServiceRepository
import ro.animaliaprogramari.animalia.domain.model.SalonService
import ro.animaliaprogramari.animalia.domain.model.ServiceId
import ro.animaliaprogramari.animalia.domain.model.ServiceCategory
import ro.animaliaprogramari.animalia.domain.model.SalonId
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa.SpringServiceRepository

/**
 * JPA adapter implementing the GroomingServiceRepository port
 */
@Repository
class JpaSalonServiceRepository(
    private val springRepository: SpringServiceRepository,
    private val serviceMapper: GroomingServiceEntityMapper
) : SalonServiceRepository {

    override fun save(service: SalonService): SalonService {
        val entity = serviceMapper.toEntity(service)
        val savedEntity = springRepository.save(entity)
        return serviceMapper.toDomain(savedEntity)
    }

    override fun findById(id: ServiceId): SalonService? {
        return springRepository.findById(id.value)
            .map { serviceMapper.toDomain(it) }
            .orElse(null)
    }

    override fun findByIds(ids: List<ServiceId>): List<SalonService> {
        val stringIds = ids.map { it.value }
        return springRepository.findAllById(stringIds)
            .map { serviceMapper.toDomain(it) }
    }

    override fun findBySalonId(
        salonId: SalonId,
        search: String?,
        category: ServiceCategory?,
        isActive: Boolean?,
        limit: Int?,
        offset: Int?
    ): List<SalonService> {
        val categoryString = category?.name
        return springRepository.findBySalonIdAndSearchAndFilters(
            salonId.value,
            search,
            categoryString,
            isActive
        ).map { serviceMapper.toDomain(it) }
            .let { if (offset != null) it.drop(offset) else it }
            .let { if (limit != null) it.take(limit) else it }
    }

    override fun findActiveBySalonId(salonId: SalonId): List<SalonService> {
        return springRepository.findBySalonIdAndIsActiveOrderByDisplayOrderAscNameAsc(salonId.value, true)
            .map { serviceMapper.toDomain(it) }
    }

    override fun findAll(
        search: String?,
        category: ServiceCategory?,
        isActive: Boolean?,
        limit: Int?,
        offset: Int?
    ): List<SalonService> {
        var services = springRepository.findAll()

        return services
            .filter { isActive == null || it.isActive == isActive }
            .filter { search == null || it.name.contains(search, ignoreCase = true) }
            .let { if (offset != null) it.drop(offset) else it }
            .let { if (limit != null) it.take(limit) else it }
            .map { serviceMapper.toDomain(it) }
            .filter { category == null || it.category == category }
    }

    override fun findByCategory(category: ServiceCategory): List<SalonService> {
        return springRepository.findAll()
            .map { serviceMapper.toDomain(it) }
            .filter { it.category == category }
    }

    override fun findActiveServices(): List<SalonService> {
        return springRepository.findByIsActiveTrue()
            .map { serviceMapper.toDomain(it) }
    }

    override fun existsById(id: ServiceId): Boolean {
        return springRepository.existsById(id.value)
    }

    override fun deleteById(id: ServiceId): Boolean {
        return if (springRepository.existsById(id.value)) {
            springRepository.deleteById(id.value)
            true
        } else {
            false
        }
    }

    override fun existsByIdAndSalonId(serviceId: ServiceId, salonId: SalonId): Boolean {
        return springRepository.existsByIdAndSalonId(serviceId.value, salonId.value)
    }

    override fun count(
        search: String?,
        category: ServiceCategory?,
        isActive: Boolean?
    ): Long {
        return findAll(search, category, isActive).size.toLong()
    }

    override fun countBySalonId(
        salonId: SalonId,
        search: String?,
        category: ServiceCategory?,
        isActive: Boolean?
    ): Long {
        return findBySalonId(salonId, search, category, isActive).size.toLong()
    }
}
