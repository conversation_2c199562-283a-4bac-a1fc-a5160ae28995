package ro.animaliaprogramari.animalia.adapter.outbound.persistence

import org.springframework.stereotype.Component
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.*
import ro.animaliaprogramari.animalia.domain.model.*
import java.time.DayOfWeek
import java.time.LocalTime
import java.time.LocalDate
import java.time.LocalDateTime

/**
 * Mapper between Staff domain model and StaffEntity
 * Handles complex mapping of working hours and specializations
 */
@Component
class StaffEntityMapper {

    fun toDomain(entity: StaffEntity): Staff {
        val role = mapRoleToDomain(entity.role)
        val permissions = when (role) {
            StaffRole.CHIEF_GROOMER -> StaffPermissions.fullAccess()
            else -> StaffPermissions.defaultGroomerAccess()
        }

        return Staff(
            id = StaffId.of(entity.id),
            userId = UserId.of(entity.userId),
            salonId = SalonId.of(entity.salonId),
            role = role,
            permissions = permissions,
            specializations = entity.specializations.map { mapSpecializationToDomain(it) }.toSet(),
            isActive = entity.isActive,
            hiredAt = entity.hireDate.atStartOfDay(),
            createdAt = entity.createdAt,
            updatedAt = entity.updatedAt,
            nickname = entity.nickName
        )
    }

    fun toEntity(staff: Staff): StaffEntity {
        return StaffEntity(
            id = staff.id.value,
            userId = staff.userId.value,
            nickName = staff.nickname,
            salonId = staff.salonId.value,
            role = mapRoleToEntity(staff.role),
            workingHours = "{}",  // Legacy field - working hours now managed via StaffWorkingHoursSettings
            hireDate = staff.hiredAt.toLocalDate(),
            specializations = staff.specializations.map { mapSpecializationToEntity(it) }.toSet(),
            isActive = staff.isActive,
            createdAt = staff.createdAt,
            updatedAt = staff.updatedAt
        )
    }

    private fun mapRoleToDomain(role: StaffRoleEntity): StaffRole {
        return when (role) {
            StaffRoleEntity.GROOMER -> StaffRole.GROOMER
            StaffRoleEntity.CHIEF_GROOMER -> StaffRole.CHIEF_GROOMER
            StaffRoleEntity.RECEPTIONIST -> StaffRole.GROOMER
            StaffRoleEntity.MANAGER -> StaffRole.CHIEF_GROOMER
            StaffRoleEntity.ADMIN -> StaffRole.CHIEF_GROOMER
        }
    }

    private fun mapRoleToEntity(role: StaffRole): StaffRoleEntity {
        return when (role) {
            StaffRole.GROOMER -> StaffRoleEntity.GROOMER
            StaffRole.CHIEF_GROOMER -> StaffRoleEntity.CHIEF_GROOMER
            // Map additional roles to existing ones for compatibility
            StaffRole.ASSISTANT -> StaffRoleEntity.GROOMER
            StaffRole.SENIOR_GROOMER -> StaffRoleEntity.CHIEF_GROOMER
        }
    }

    private fun mapSpecializationToDomain(specialization: SpecializationEntity): Specialization {
        return when (specialization) {
            SpecializationEntity.DOG_GROOMING -> Specialization.BASIC_GROOMING
            SpecializationEntity.CAT_GROOMING -> Specialization.BASIC_GROOMING
            SpecializationEntity.EXOTIC_PETS -> Specialization.ADVANCED_STYLING
            SpecializationEntity.NAIL_TRIMMING -> Specialization.NAIL_CARE
            SpecializationEntity.DENTAL_CARE -> Specialization.DENTAL_CARE
            SpecializationEntity.FUR_STYLING -> Specialization.ADVANCED_STYLING
            SpecializationEntity.MEDICAL_GROOMING -> Specialization.MEDICAL_CARE
            SpecializationEntity.PUPPY_GROOMING -> Specialization.SMALL_BREEDS
            SpecializationEntity.SENIOR_PET_CARE -> Specialization.MEDICAL_CARE
        }
    }

    private fun mapSpecializationToEntity(specialization: Specialization): SpecializationEntity {
        return when (specialization) {
            Specialization.BASIC_GROOMING -> SpecializationEntity.DOG_GROOMING
            Specialization.ADVANCED_STYLING -> SpecializationEntity.FUR_STYLING
            Specialization.MEDICAL_CARE -> SpecializationEntity.MEDICAL_GROOMING
            Specialization.LARGE_BREEDS -> SpecializationEntity.DOG_GROOMING
            Specialization.SMALL_BREEDS -> SpecializationEntity.PUPPY_GROOMING
            Specialization.NAIL_CARE -> SpecializationEntity.NAIL_TRIMMING
            Specialization.DENTAL_CARE -> SpecializationEntity.DENTAL_CARE
        }
    }
}
