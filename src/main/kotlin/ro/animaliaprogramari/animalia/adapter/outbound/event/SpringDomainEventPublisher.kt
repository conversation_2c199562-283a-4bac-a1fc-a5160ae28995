package ro.animaliaprogramari.animalia.adapter.outbound.event

import org.slf4j.LoggerFactory
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Component
import ro.animaliaprogramari.animalia.application.port.outbound.DomainEventPublisher
import ro.animaliaprogramari.animalia.domain.event.DomainEvent

/**
 * Spring-based implementation of DomainEventPublisher
 * Uses Spring's ApplicationEventPublisher to publish domain events
 */
@Component
class SpringDomainEventPublisher(
    private val applicationEventPublisher: ApplicationEventPublisher
) : DomainEventPublisher {
    
    private val logger = LoggerFactory.getLogger(SpringDomainEventPublisher::class.java)
    
    override fun publish(event: DomainEvent) {
        logger.debug("Publishing domain event: ${event::class.simpleName} with ID: ${event.eventId}")
        
        try {
            applicationEventPublisher.publishEvent(event)
            logger.debug("Successfully published domain event: ${event.eventId}")
        } catch (e: Exception) {
            logger.error("Failed to publish domain event: ${event.eventId}", e)
            throw e
        }
    }
    
    override fun publishAll(events: List<DomainEvent>) {
        logger.debug("Publishing ${events.size} domain events")
        
        events.forEach { event ->
            publish(event)
        }
        
        logger.debug("Successfully published all ${events.size} domain events")
    }
}
