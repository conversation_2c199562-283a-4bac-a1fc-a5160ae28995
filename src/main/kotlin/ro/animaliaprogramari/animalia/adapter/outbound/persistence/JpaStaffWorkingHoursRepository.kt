package ro.animaliaprogramari.animalia.adapter.outbound.persistence

import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.application.port.outbound.StaffWorkingHoursRepository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa.SpringStaffWorkingHoursRepository
import ro.animaliaprogramari.animalia.domain.model.*

/**
 * JPA adapter implementing the StaffWorkingHoursRepository port
 * This adapter translates between domain models and JPA entities for staff working hours
 */
@Repository
class JpaStaffWorkingHoursRepository(
    private val springRepository: SpringStaffWorkingHoursRepository,
    private val mapper: StaffWorkingHoursEntityMapper
) : StaffWorkingHoursRepository {

    override fun findByStaffIdAndSalonId(staffId: StaffId, salonId: SalonId): StaffWorkingHoursSettings? {
        return springRepository.findByStaffIdAndSalonId(staffId.value, salonId.value)
            ?.let { mapper.toDomain(it) }
    }

    override fun save(workingHours: StaffWorkingHoursSettings): StaffWorkingHoursSettings {
        val entity = mapper.toEntity(workingHours)
        val savedEntity = springRepository.save(entity)
        return mapper.toDomain(savedEntity)
    }

    override fun deleteByStaffIdAndSalonId(staffId: StaffId, salonId: SalonId) {
        springRepository.deleteByStaffIdAndSalonId(staffId.value, salonId.value)
    }

    override fun existsByStaffIdAndSalonId(staffId: StaffId, salonId: SalonId): Boolean {
        return springRepository.existsByStaffIdAndSalonId(staffId.value, salonId.value)
    }

    override fun findBySalonId(salonId: SalonId): List<StaffWorkingHoursSettings> {
        return springRepository.findBySalonId(salonId.value)
            .map { mapper.toDomain(it) }
    }
}
