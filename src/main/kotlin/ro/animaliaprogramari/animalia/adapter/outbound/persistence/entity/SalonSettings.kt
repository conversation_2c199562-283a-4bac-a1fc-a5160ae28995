package ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity

import jakarta.persistence.*
import jakarta.validation.constraints.NotBlank
import java.time.LocalDateTime
import java.util.*

@Entity
@Table(name = "salon_settings")
data class SalonSettings(
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    var id: UUID? = null,

    @field:NotBlank(message = "Setting key is required")
    @Column(name = "setting_key", nullable = false, unique = true)
    var key: String = "",

    @field:NotBlank(message = "Setting value is required")
    @Column(name = "setting_value", nullable = false, columnDefinition = "TEXT")
    var value: String = "",

    @Column(columnDefinition = "TEXT")
    var description: String? = null,

    @Column(name = "updated_at")
    var updatedAt: LocalDateTime = LocalDateTime.now()
) {
    // Default constructor for JPA
    constructor() : this(null, "", "", null, LocalDateTime.now())
}