package ro.animaliaprogramari.animalia.application.command

import ro.animaliaprogramari.animalia.domain.model.*
import java.time.DayOfWeek

/**
 * Command for updating working hours settings
 */
data class UpdateWorkingHoursCommand(
    val salonId: SalonId,
    val updaterUserId: UserId,
    val weeklySchedule: Map<DayOfWeek, DaySchedule>,
    val holidays: List<Holiday>,
    val customClosures: List<CustomClosure>
)
