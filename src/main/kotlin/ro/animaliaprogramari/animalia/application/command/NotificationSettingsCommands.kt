package ro.animaliaprogramari.animalia.application.command

import ro.animaliaprogramari.animalia.domain.model.*

/**
 * Command to update notification settings for a salon
 */
data class UpdateNotificationSettingsCommand(
    val salonId: SalonId,
    val pushNotificationsEnabled: Boolean,
    val soundPreference: SoundPreference,
    val vibrationEnabled: Boolean,
    val doNotDisturb: DoNotDisturbSettings,
    val notificationRules: NotificationRules
)
