package ro.animaliaprogramari.animalia.application.command

import ro.animaliaprogramari.animalia.domain.model.*

/**
 * Command to authenticate user with Firebase ID token
 */
data class AuthenticateWithFirebaseCommand(
    val firebaseToken: FirebaseToken,
    val platform: String? = null,
    val appVersion: String? = null
)

/**
 * Command to refresh JWT token
 */
data class RefreshTokenCommand(
    val refreshToken: String
)

/**
 * Command to create a new user
 */
data class CreateUserCommand(
    val firebaseUid: String,
    val email: Email,
    val name: String,
    val role: UserRole = UserRole.USER
)

/**
 * Command to update user information
 */
data class UpdateUserCommand(
    val userId: UserId,
    val name: String? = null,
    val email: Email? = null
)

/**
 * Command to activate user
 */
data class ActivateUserCommand(
    val userId: UserId
)

/**
 * Command to deactivate user
 */
data class DeactivateUserCommand(
    val userId: UserId
)

/**
 * Command to update user role
 */
data class UpdateUserRoleCommand(
    val userId: UserId,
    val role: UserRole
)

/**
 * Command to delete user account
 * This will permanently delete the user and all associated data
 */
data class DeleteUserAccountCommand(
    val userId: UserId,
    val confirmationText: String
) {
    init {
        require(confirmationText == "confirm") {
            "Confirmation text must be exactly 'confirm' to proceed with account deletion"
        }
    }
}
