package ro.animaliaprogramari.animalia.application.command

import ro.animaliaprogramari.animalia.domain.model.*

/**
 * Command to create a new salon
 */
data class CreateSalonCommand(
    val name: String,
    val description: String? = null,
    val address: String?,
    val city: String? = null,
    val phone: PhoneNumber?,
    val email: Email?,
    val creatorUserId: UserId
)

/**
 * Command to update salon information
 */
data class UpdateSalonCommand(
    val salonId: SalonId,
    val name: String?,
    val address: String?,
    val phone: PhoneNumber?,
    val email: Email?,
    val updaterUserId: UserId
)

/**
 * Command to activate a salon
 */
data class ActivateSalonCommand(
    val salonId: SalonId,
    val activatorUserId: UserId
)

/**
 * Command to deactivate a salon
 */
data class DeactivateSalonCommand(
    val salonId: SalonId,
    val deactivatorUserId: UserId
)

/**
 * Command to add a client to a salon
 */
data class AddClientToSalonCommand(
    val salonId: SalonId,
    val clientId: ClientId,
    val groomerUserId: UserId
)

/**
 * Command to remove a client from a salon
 */
data class RemoveClientFromSalonCommand(
    val salonId: SalonId,
    val clientId: ClientId,
    val groomerUserId: UserId
)
