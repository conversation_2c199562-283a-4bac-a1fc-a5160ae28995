package ro.animaliaprogramari.animalia.application.command

import ro.animaliaprogramari.animalia.domain.model.*

/**
 * Command to send a salon invitation
 */
data class SendSalonInvitationCommand(
    val salonId: SalonId,
    val inviterUserId: UserId,
    val invitedUserPhone: String,
    val proposedRole: StaffRole,
    val proposedPermissions: StaffPermissions,
    val message: String? = null
)

/**
 * Command to accept a salon invitation
 */
data class AcceptSalonInvitationCommand(
    val invitationId: InvitationId,
    val acceptingUserId: UserId
)

/**
 * Command to decline a salon invitation
 */
data class DeclineSalonInvitationCommand(
    val invitationId: InvitationId,
    val decliningUserId: UserId
)

/**
 * Command to cancel a salon invitation
 */
data class CancelSalonInvitationCommand(
    val invitationId: InvitationId,
    val cancellingUserId: UserId
)

/**
 * Command to resend a salon invitation
 */
data class ResendSalonInvitationCommand(
    val invitationId: InvitationId,
    val resendingUserId: UserId,
    val newMessage: String? = null
)
