package ro.animaliaprogramari.animalia.application.monitoring

import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import ro.animaliaprogramari.animalia.application.port.outbound.MonitoringService
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

/**
 * Utility class for tracking workflow and method execution metrics
 * Provides convenient methods for monitoring business operations
 */
@Component
class WorkflowMetrics(
    private val monitoringService: MonitoringService
) {
    
    private val logger = LoggerFactory.getLogger(WorkflowMetrics::class.java)
    
    /**
     * Execute a workflow with comprehensive monitoring
     */
    inline fun <T> executeWorkflow(
        workflowName: String,
        tags: Map<String, String> = emptyMap(),
        operation: () -> T
    ): T {
        val timer = monitoringService.startTimer("workflow.$workflowName", tags)
        val startTime = System.currentTimeMillis()
        
        return try {
            logger.info("Starting workflow: $workflowName")
            val result = operation()
            val duration = System.currentTimeMillis() - startTime
            
            timer.stop(success = true)
            monitoringService.recordWorkflowExecution(
                workflowName = workflowName,
                success = true,
                durationMs = duration,
                tags = tags
            )
            
            logger.info("Workflow completed successfully: $workflowName (${duration}ms)")
            result
        } catch (e: Exception) {
            val duration = System.currentTimeMillis() - startTime
            val errorType = e.javaClass.simpleName
            
            timer.stop(success = false, errorType = errorType)
            monitoringService.recordWorkflowExecution(
                workflowName = workflowName,
                success = false,
                durationMs = duration,
                errorType = errorType,
                tags = tags
            )
            
            logger.error("Workflow failed: $workflowName (${duration}ms) - $errorType: ${e.message}")
            throw e
        }
    }
    
    /**
     * Execute a method with monitoring
     */
    inline fun <T> executeMethod(
        methodName: String,
        className: String,
        tags: Map<String, String> = emptyMap(),
        operation: () -> T
    ): T {
        val timer = monitoringService.startTimer("method.$className.$methodName", tags)
        val startTime = System.currentTimeMillis()
        
        return try {
            logger.debug("Starting method: $className.$methodName")
            val result = operation()
            val duration = System.currentTimeMillis() - startTime
            
            timer.stop(success = true)
            monitoringService.recordMethodExecution(
                methodName = methodName,
                className = className,
                success = true,
                durationMs = duration,
                tags = tags
            )
            
            logger.debug("Method completed: $className.$methodName (${duration}ms)")
            result
        } catch (e: Exception) {
            val duration = System.currentTimeMillis() - startTime
            val errorType = e.javaClass.simpleName
            
            timer.stop(success = false, errorType = errorType)
            monitoringService.recordMethodExecution(
                methodName = methodName,
                className = className,
                success = false,
                durationMs = duration,
                errorType = errorType,
                tags = tags
            )
            
            logger.debug("Method failed: $className.$methodName (${duration}ms) - $errorType: ${e.message}")
            throw e
        }
    }
    
    /**
     * Record appointment workflow specific metrics
     */
    fun recordAppointmentWorkflowMetrics(
        operation: String,
        salonId: String,
        staffId: String,
        success: Boolean,
        durationMs: Long,
        conflictsDetected: Int = 0,
        alternativesGenerated: Int = 0,
        errorType: String? = null
    ) {
        val tags = mapOf(
            "operation" to operation,
            "salon_id" to salonId,
            "staff_id" to staffId,
            "success" to success.toString(),
            "hour" to LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH")),
            "day_of_week" to LocalDateTime.now().dayOfWeek.toString()
        ).let { baseTags ->
            if (errorType != null) baseTags + ("error_type" to errorType) else baseTags
        }
        
        // Record basic metrics
        monitoringService.recordTiming("appointment.$operation", durationMs, tags)
        monitoringService.incrementCounter("appointment.operations", tags)
        
        // Record conflict-specific metrics
        if (conflictsDetected > 0) {
            monitoringService.recordMetric("appointment.conflicts_detected", conflictsDetected.toDouble(), tags)
        }
        
        if (alternativesGenerated > 0) {
            monitoringService.recordMetric("appointment.alternatives_generated", alternativesGenerated.toDouble(), tags)
        }
        
        // Record success rate
        val successValue = if (success) 1.0 else 0.0
        monitoringService.recordMetric("appointment.success_rate", successValue, tags)
    }
    
    /**
     * Record business operation metrics
     */
    fun recordBusinessOperation(
        operationType: String,
        entityType: String,
        success: Boolean,
        durationMs: Long,
        additionalMetrics: Map<String, Double> = emptyMap(),
        tags: Map<String, String> = emptyMap()
    ) {
        val operationTags = tags + mapOf(
            "operation_type" to operationType,
            "entity_type" to entityType,
            "success" to success.toString()
        )
        
        // Record timing and counter
        monitoringService.recordTiming("business.operation", durationMs, operationTags)
        monitoringService.incrementCounter("business.operations", operationTags)
        
        // Record additional metrics
        additionalMetrics.forEach { (metricName, value) ->
            monitoringService.recordMetric("business.$metricName", value, operationTags)
        }
        
        // Record success rate
        val successValue = if (success) 1.0 else 0.0
        monitoringService.recordMetric("business.success_rate", successValue, operationTags)
    }
    
    /**
     * Record performance threshold violations
     */
    fun recordPerformanceThreshold(
        operationName: String,
        actualDurationMs: Long,
        thresholdMs: Long,
        tags: Map<String, String> = emptyMap()
    ) {
        if (actualDurationMs > thresholdMs) {
            val violationTags = tags + mapOf(
                "operation" to operationName,
                "threshold_ms" to thresholdMs.toString(),
                "actual_ms" to actualDurationMs.toString(),
                "violation_ratio" to (actualDurationMs.toDouble() / thresholdMs).toString()
            )
            
            monitoringService.incrementCounter("performance.threshold_violations", violationTags)
            monitoringService.recordMetric("performance.violation_ratio", 
                actualDurationMs.toDouble() / thresholdMs, violationTags)
            
            logger.warn("Performance threshold violation: $operationName took ${actualDurationMs}ms (threshold: ${thresholdMs}ms)")
        }
    }
}
