package ro.animaliaprogramari.animalia.application.usecase

import org.slf4j.LoggerFactory
import org.springframework.transaction.annotation.Transactional
import ro.animaliaprogramari.animalia.application.command.*
import ro.animaliaprogramari.animalia.application.port.inbound.UserManagementUseCase
import ro.animaliaprogramari.animalia.application.port.outbound.*
import ro.animaliaprogramari.animalia.application.query.*
import ro.animaliaprogramari.animalia.domain.exception.EntityNotFoundException
import ro.animaliaprogramari.animalia.domain.exception.BusinessRuleViolationException
import ro.animaliaprogramari.animalia.domain.model.User

/**
 * Implementation of user management use cases
 */
open class UserManagementUseCaseImpl(
    private val userRepository: UserRepository,
    private val staffRepository: StaffRepository,
    private val clientRepository: ClientRepository,
    private val appointmentRepository: AppointmentRepository
) : UserManagementUseCase {

    private val logger = LoggerFactory.getLogger(UserManagementUseCaseImpl::class.java)

    override fun createUser(command: CreateUserCommand): User {
        val user = User.createFromFirebase(
            firebaseUid = command.firebaseUid,
            email = command.email,
            name = command.name,
            role = command.role
        )
        return userRepository.save(user)
    }

    override fun updateUser(command: UpdateUserCommand): User {
        val user = userRepository.findById(command.userId)
            ?: throw EntityNotFoundException("User", command.userId.value)

        val updatedUser = user.updateInfo(
            name = command.name,
            email = command.email
        )
        return userRepository.save(updatedUser)
    }

    override fun activateUser(command: ActivateUserCommand): User {
        val user = userRepository.findById(command.userId)
            ?: throw EntityNotFoundException("User", command.userId.value)

        val activatedUser = user.activate()
        return userRepository.save(activatedUser)
    }

    override fun deactivateUser(command: DeactivateUserCommand): User {
        val user = userRepository.findById(command.userId)
            ?: throw EntityNotFoundException("User", command.userId.value)

        val deactivatedUser = user.deactivate()
        return userRepository.save(deactivatedUser)
    }

    override fun updateUserRole(command: UpdateUserRoleCommand): User {
        val user = userRepository.findById(command.userId)
            ?: throw EntityNotFoundException("User", command.userId.value)

        val updatedUser = user.updateRole(command.role)
        return userRepository.save(updatedUser)
    }

    override fun getUserById(query: GetUserByIdQuery): User? {
        return userRepository.findById(query.userId)
    }

    override fun getUserByFirebaseUid(query: GetUserByFirebaseUidQuery): User? {
        return userRepository.findByFirebaseUid(query.firebaseUid)
    }

    override fun getUserByEmail(query: GetUserByEmailQuery): User? {
        return userRepository.findByEmail(query.email)
    }

    override fun searchUsers(query: SearchUsersQuery): List<User> {
        return userRepository.findAll(
            search = query.searchTerm,
            role = query.role,
            isActive = query.isActive,
            limit = query.limit,
            offset = query.offset
        )
    }

    override fun getAllUsers(query: GetAllUsersQuery): List<User> {
        return userRepository.findAll(
            role = query.role,
            isActive = query.isActive,
            limit = query.limit,
            offset = query.offset
        )
    }

    @Transactional
    override fun deleteUserAccount(command: DeleteUserAccountCommand): Boolean {
        logger.info("Starting account deletion process for user: ${command.userId.value}")

        // 1. Validate user exists
        userRepository.findById(command.userId)
            ?: throw EntityNotFoundException("User", command.userId.value)

        // 2. Find all staff associations for this user
        val staffAssociations = staffRepository.findActiveByUserId(command.userId)

        // 3. Delete all appointments where user is staff member
        staffAssociations.forEach { staff ->
            val appointments = appointmentRepository.findByStaffId(staff.id)
            appointments.forEach { appointment ->
                appointmentRepository.deleteById(appointment.id)
                logger.debug("Deleted appointment: ${appointment.id.value} for staff: ${staff.id.value}")
            }
        }

        // 4. Deactivate all staff associations (soft delete)
        staffAssociations.forEach { staff ->
            val deactivatedStaff = staff.deactivate()
            staffRepository.save(deactivatedStaff)
            logger.debug("Deactivated staff association: ${staff.id.value}")
        }

        // 5. Find and deactivate client records (if user has any)
        val clientRecords = clientRepository.findAll(userId = command.userId)
        clientRecords.forEach { client ->
            if (client.isActive) {
                val deactivatedClient = client.deactivate()
                clientRepository.save(deactivatedClient)
                logger.debug("Deactivated client record: ${client.id.value}")
            }
        }

        // 6. Finally, delete the user account (hard delete)
        val deleted = userRepository.deleteById(command.userId)

        if (deleted) {
            logger.info("Successfully deleted user account: ${command.userId.value} and all associated data")
        } else {
            logger.error("Failed to delete user account: ${command.userId.value}")
        }

        return deleted
    }
}
