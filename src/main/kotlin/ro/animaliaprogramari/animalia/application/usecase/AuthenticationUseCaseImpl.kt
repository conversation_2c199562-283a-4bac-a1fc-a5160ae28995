package ro.animaliaprogramari.animalia.application.usecase

import org.slf4j.LoggerFactory
import org.springframework.transaction.annotation.Transactional
import ro.animaliaprogramari.animalia.application.command.AuthenticateWithFirebaseCommand
import ro.animaliaprogramari.animalia.application.command.RefreshTokenCommand
import ro.animaliaprogramari.animalia.application.port.inbound.AuthenticationResult
import ro.animaliaprogramari.animalia.application.port.inbound.AuthenticationUseCase
import ro.animaliaprogramari.animalia.application.port.outbound.FirebaseTokenValidator
import ro.animaliaprogramari.animalia.application.port.outbound.JwtTokenGenerator
import ro.animaliaprogramari.animalia.application.port.outbound.StaffRepository
import ro.animaliaprogramari.animalia.application.port.outbound.UserRepository
import ro.animaliaprogramari.animalia.application.query.GetUserByFirebaseUidQuery
import ro.animaliaprogramari.animalia.application.query.GetUserByIdQuery
import ro.animaliaprogramari.animalia.domain.model.AuthenticatedUser
import ro.animaliaprogramari.animalia.domain.model.Email
import ro.animaliaprogramari.animalia.domain.model.User
import ro.animaliaprogramari.animalia.domain.model.UserId
import ro.animaliaprogramari.animalia.domain.service.AuthenticationService

/**
 * Implementation of authentication use cases
 * Orchestrates authentication operations using domain services and outbound ports
 */
open class AuthenticationUseCaseImpl(
    private val firebaseTokenValidator: FirebaseTokenValidator,
    private val jwtTokenGenerator: JwtTokenGenerator,
    private val userRepository: UserRepository,
    private val staffRepository: StaffRepository,
    private val authenticationService: AuthenticationService
) : AuthenticationUseCase {

    private val logger = LoggerFactory.getLogger(AuthenticationUseCaseImpl::class.java)

    override fun authenticateWithFirebase(command: AuthenticateWithFirebaseCommand): AuthenticationResult {
        try {
            logger.info("=== AUTHENTICATION USE CASE START ===")
            logger.info("Platform: ${command.platform}")
            logger.info("App Version: ${command.appVersion}")

            // 1. Validate Firebase token
            logger.info("Step 1: Validating Firebase token...")
            val firebaseResult = firebaseTokenValidator.validateToken(command.firebaseToken)
            if (!firebaseResult.isValid) {
                logger.error("Step 1 FAILED: Firebase token validation failed: ${firebaseResult.errorMessage}")
                return AuthenticationResult.failure(
                    firebaseResult.errorMessage ?: "Invalid Firebase token"
                )
            }

            logger.info("Step 1 SUCCESS: Firebase token validated successfully for UID: ${firebaseResult.firebaseUid}")

            // 2. Extract user data from Firebase token
            logger.info("Step 2: Extracting user data from Firebase token...")
            val firebaseUid = firebaseResult.firebaseUid!!
            val email = Email.ofNullable(firebaseResult.email)
            val name = firebaseResult.name!!
            val phoneNumber = firebaseResult.phoneNumber ?: "+40000000000"

            logger.info("Step 2 SUCCESS: Extracted data - UID: $firebaseUid, Email: ${email?.value}, Name: $name, Phone: $phoneNumber")

            // 3. Find or create user
            logger.info("Step 3: Finding existing user by Firebase UID...")
            val existingUser = userRepository.findByFirebaseUid(firebaseUid)
            logger.info("Step 3 RESULT: Existing user found: ${existingUser != null}")

            logger.info("Step 4: Creating or updating user...")
            val user = authenticationService.createOrUpdateUserFromFirebase(
                firebaseUid = firebaseUid,
                email = email,
                phoneNumber = phoneNumber,
                name = name,
                existingUser = existingUser
            )
            logger.info("Step 4 SUCCESS: User created/updated with ID: ${user.id?.value}")

            // 4. Validate user for authentication
            val validationResult = authenticationService.validateUserForAuthentication(user)
            if (!validationResult.isValid()) {
                return AuthenticationResult.failure(
                    validationResult.getErrorMessage() ?: "User validation failed"
                )
            }

            // 5. Save user (create or update)
            logger.debug("Saving user to database")
            val savedUser = userRepository.save(user)
            logger.debug("User saved successfully with ID: ${savedUser.id.value}")

            // 6. Load staff associations
            val staffAssociations = staffRepository.findActiveByUserId(savedUser.id)

            // 7. Create authenticated user context
            val authenticatedUser = AuthenticatedUser.from(savedUser, staffAssociations)

            // 8. Generate JWT token
            logger.debug("Generating JWT token for user")
            val jwtToken = jwtTokenGenerator.generateToken(authenticatedUser)
            logger.debug("JWT token generated successfully")

            return AuthenticationResult.success(authenticatedUser, jwtToken)

        } catch (e: Exception) {
            logger.error("Authentication failed with exception", e)
            return AuthenticationResult.failure("Authentication failed: ${e.message}")
        }
    }

    override fun refreshToken(command: RefreshTokenCommand): AuthenticationResult {
        try {
            val newToken = jwtTokenGenerator.refreshToken(command.refreshToken)
                ?: return AuthenticationResult.failure("Invalid refresh token")

            // Validate the new token to get user context
            val validationResult = jwtTokenGenerator.validateToken(newToken.token)
            if (!validationResult.isValid) {
                return AuthenticationResult.failure("Token refresh failed")
            }

            // Get user from database
            val userId = UserId.of(validationResult.userId!!)
            val user = userRepository.findById(userId)
                ?: return AuthenticationResult.failure("User not found")

            val staffAssociations = staffRepository.findActiveByUserId(user.id)
            val authenticatedUser = AuthenticatedUser.from(user, staffAssociations)
            return AuthenticationResult.success(authenticatedUser, newToken)

        } catch (e: Exception) {
            return AuthenticationResult.failure("Token refresh failed: ${e.message}")
        }
    }

    override fun getCurrentUser(query: GetUserByIdQuery): AuthenticatedUser? {
        val user = userRepository.findById(query.userId)
        return user?.takeIf { it.isActive }?.let {
            val staffAssociations = staffRepository.findActiveByUserId(it.id)
            AuthenticatedUser.from(it, staffAssociations)
        }
    }

    override fun getUserByFirebaseUid(query: GetUserByFirebaseUidQuery): User? {
        return userRepository.findByFirebaseUid(query.firebaseUid)
    }

    open override fun validateToken(token: String): AuthenticatedUser? {
        try {
            logger.info("=== JWT TOKEN VALIDATION START ===")
            logger.info("Token length: ${token.length}")
            logger.info("Token preview: ${token.take(50)}...")

            val validationResult = jwtTokenGenerator.validateToken(token)
            logger.info("JWT validation result: ${validationResult.isValid}")

            if (!validationResult.isValid) {
                logger.error("JWT validation failed: ${validationResult.errorMessage}")
                return null
            }

            logger.info("JWT validation successful, extracting user data...")
            logger.info("User ID from token: ${validationResult.userId}")
            logger.info("Role from token: ${validationResult.role}")
            logger.info("Active from token: ${validationResult.isActive}")

            val userId = UserId.of(validationResult.userId!!)
            logger.info("Looking up user in database...")
            val user = userRepository.findById(userId)

            if (user == null) {
                logger.error("User not found in database for ID: ${userId.value}")
                return null
            }

            logger.info("User found: ${user.id.value}, Active: ${user.isActive}")

            return user.takeIf { it.isActive }?.let {
                logger.info("Loading staff associations...")
                val staffAssociations = staffRepository.findActiveByUserId(it.id)
                logger.info("Staff associations count: ${staffAssociations.size}")

                val authenticatedUser = AuthenticatedUser.from(it, staffAssociations)
                logger.info("=== JWT TOKEN VALIDATION SUCCESS ===")
                authenticatedUser
            } ?: run {
                logger.error("User is not active: ${user.id.value}")
                null
            }

        } catch (e: Exception) {
            logger.error("=== JWT TOKEN VALIDATION ERROR ===")
            logger.error("Exception type: ${e.javaClass.simpleName}")
            logger.error("Exception message: ${e.message}")
            logger.error("Stack trace: ", e)
            return null
        }
    }

}
