package ro.animaliaprogramari.animalia.application.usecase

import org.slf4j.LoggerFactory
import org.springframework.transaction.annotation.Transactional
import ro.animaliaprogramari.animalia.application.command.*
import ro.animaliaprogramari.animalia.application.port.inbound.SalonManagementUseCase
import ro.animaliaprogramari.animalia.application.port.outbound.ClientRepository
import ro.animaliaprogramari.animalia.application.port.outbound.SalonRepository
import ro.animaliaprogramari.animalia.application.port.outbound.StaffRepository
import ro.animaliaprogramari.animalia.application.port.outbound.UserRepository
import ro.animaliaprogramari.animalia.application.port.outbound.WorkingHoursRepository
import ro.animaliaprogramari.animalia.application.query.*
import ro.animaliaprogramari.animalia.domain.exception.BusinessRuleViolationException
import ro.animaliaprogramari.animalia.domain.exception.EntityNotFoundException
import ro.animaliaprogramari.animalia.domain.model.*

/**
 * Implementation of salon management use case
 * Handles all salon-related business logic
 */
@Transactional
class SalonManagementUseCaseImpl(
    private val salonRepository: SalonRepository,
    private val userRepository: UserRepository,
    private val staffRepository: StaffRepository,
    private val clientRepository: ClientRepository,
    private val workingHoursRepository: WorkingHoursRepository
) : SalonManagementUseCase {

    private val logger = LoggerFactory.getLogger(SalonManagementUseCaseImpl::class.java)

    override fun createSalon(command: CreateSalonCommand): Salon {
        logger.debug("Creating salon: ${command.name}")

        // Validate creator exists
        userRepository.findById(command.creatorUserId)
            ?: throw EntityNotFoundException("Creator user not found: ${command.creatorUserId.value}")

        // Create salon (note: description field is not yet supported in domain model)
        val salon = Salon(
            id = SalonId.generate(),
            name = command.name,
            address = command.address,
            city = command.city,
            phone = command.phone,
            email = command.email,
            ownerId = command.creatorUserId,
            isActive = true,
            clientIds = emptySet()
        )

        val savedSalon = salonRepository.save(salon)
        logger.debug("Salon created: ${savedSalon.id.value}")

        // Create default working hours for the salon
        val defaultWorkingHours = WorkingHoursSettings.createDefault(savedSalon.id)
        workingHoursRepository.save(defaultWorkingHours)
        logger.debug("Default working hours created for salon: ${savedSalon.id.value}")

        // Create chief groomer staff for the creator
        val staff = Staff.createChiefGroomer(
            userId = command.creatorUserId,
            salonId = savedSalon.id
        )
        staffRepository.save(staff)
        logger.debug("Chief groomer association created for user: ${command.creatorUserId.value}")

        return savedSalon
    }

    override fun updateSalon(command: UpdateSalonCommand): Salon {
        logger.debug("Updating salon: ${command.salonId.value}")

        // Find salon
        val salon = salonRepository.findById(command.salonId)
            ?: throw EntityNotFoundException("Salon not found: ${command.salonId.value}")

        // Validate updater has permission
        val updaterStaff = staffRepository.findByUserIdAndSalonId(
            command.updaterUserId, command.salonId
        )
        if (updaterStaff == null || updaterStaff.role != StaffRole.CHIEF_GROOMER) {
            throw BusinessRuleViolationException("User does not have permission to update this salon")
        }

        // Update salon
        val updatedSalon = salon.copy(
            name = command.name ?: salon.name,
            address = command.address ?: salon.address,
            phone = command.phone ?: salon.phone,
            email = command.email ?: salon.email,
            updatedAt = java.time.LocalDateTime.now()
        )

        return salonRepository.save(updatedSalon)
    }

    override fun activateSalon(command: ActivateSalonCommand): Salon {
        logger.debug("Activating salon: ${command.salonId.value}")

        val salon = salonRepository.findById(command.salonId)
            ?: throw EntityNotFoundException("Salon not found: ${command.salonId.value}")

        val activatedSalon = salon.copy(
            isActive = true,
            updatedAt = java.time.LocalDateTime.now()
        )

        return salonRepository.save(activatedSalon)
    }

    override fun deactivateSalon(command: DeactivateSalonCommand): Salon {
        logger.debug("Deactivating salon: ${command.salonId.value}")

        val salon = salonRepository.findById(command.salonId)
            ?: throw EntityNotFoundException("Salon not found: ${command.salonId.value}")

        val deactivatedSalon = salon.copy(
            isActive = false,
            updatedAt = java.time.LocalDateTime.now()
        )

        return salonRepository.save(deactivatedSalon)
    }

    override fun addClientToSalon(command: AddClientToSalonCommand): Salon {
        logger.debug("Adding client ${command.clientId.value} to salon ${command.salonId.value}")

        // Validate salon exists
        val salon = salonRepository.findById(command.salonId)
            ?: throw EntityNotFoundException("Salon not found: ${command.salonId.value}")

        // Validate client exists
        clientRepository.findById(command.clientId)
            ?: throw EntityNotFoundException("Client not found: ${command.clientId.value}")

        // Validate groomer has permission
        val groomerStaff = staffRepository.findByUserIdAndSalonId(
            command.groomerUserId, command.salonId
        )
        if (groomerStaff == null || groomerStaff.permissions.clientDataAccess == ClientDataAccess.NONE) {
            throw BusinessRuleViolationException("Groomer does not have permission to add clients to this salon")
        }

        // Add client to salon
        val updatedClientIds = salon.clientIds + command.clientId
        val updatedSalon = salon.copy(
            clientIds = updatedClientIds,
            updatedAt = java.time.LocalDateTime.now()
        )

        return salonRepository.save(updatedSalon)
    }

    override fun removeClientFromSalon(command: RemoveClientFromSalonCommand): Salon {
        logger.debug("Removing client ${command.clientId.value} from salon ${command.salonId.value}")

        // Validate salon exists
        val salon = salonRepository.findById(command.salonId)
            ?: throw EntityNotFoundException("Salon not found: ${command.salonId.value}")

        // Validate groomer has permission
        val groomerStaff = staffRepository.findByUserIdAndSalonId(
            command.groomerUserId, command.salonId
        )
        if (groomerStaff == null || groomerStaff.role != StaffRole.CHIEF_GROOMER) {
            throw BusinessRuleViolationException("Only chief groomers can remove clients from salon")
        }

        // Remove client from salon
        val updatedClientIds = salon.clientIds - command.clientId
        val updatedSalon = salon.copy(
            clientIds = updatedClientIds,
            updatedAt = java.time.LocalDateTime.now()
        )

        return salonRepository.save(updatedSalon)
    }

    override fun getSalonById(query: GetSalonByIdQuery): Salon? {
        return salonRepository.findById(query.salonId)
    }

    override fun getAllSalons(query: GetAllSalonsQuery): List<Salon> {
        return salonRepository.findAll(
            search = query.search,
            isActive = query.isActive,
            limit = query.limit,
            offset = query.offset
        )
    }

    override fun getSalonsByUser(query: GetSalonsByUserQuery): List<Salon> {
        val staffList = staffRepository.findByUserId(query.userId)
        val salonIds = staffList
            .filter { query.isActive == null || it.isActive == query.isActive }
            .map { it.salonId }

        return salonIds.mapNotNull { salonRepository.findById(it) }
    }

    override fun getSalonDetails(query: GetSalonDetailsQuery): Map<String, Any> {
        val salon = salonRepository.findById(query.salonId)
            ?: throw EntityNotFoundException("Salon not found: ${query.salonId.value}")

        // Get salon staff
        val staffList = staffRepository.findActiveBySalonWithUserDetails(query.salonId)

        return mapOf(
            "salon" to salon,
            "clientCount" to salon.clientIds.size,
            "groomerCount" to staffList.size,
            "chiefGroomers" to staffList.filter { it.role == StaffRole.CHIEF_GROOMER }.size,
            "regularGroomers" to staffList.filter { it.role == StaffRole.GROOMER }.size
        )
    }

    override fun getSalonsWithClient(query: GetSalonsWithClientQuery): List<Salon> {
        return salonRepository.findAll().filter { salon ->
            salon.clientIds.contains(query.clientId)
        }
    }
}
