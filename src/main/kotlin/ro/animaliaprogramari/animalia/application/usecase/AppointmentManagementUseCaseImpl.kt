package ro.animaliaprogramari.animalia.application.usecase


import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import ro.animaliaprogramari.animalia.application.command.*
import ro.animaliaprogramari.animalia.application.port.inbound.AppointmentManagementUseCase
import ro.animaliaprogramari.animalia.application.port.inbound.AppointmentSummary
import ro.animaliaprogramari.animalia.application.port.inbound.AvailabilityResult
import ro.animaliaprogramari.animalia.application.port.inbound.TimeSlot
import ro.animaliaprogramari.animalia.application.port.outbound.*
import ro.animaliaprogramari.animalia.application.query.*
import ro.animaliaprogramari.animalia.domain.exception.BusinessRuleViolationException
import ro.animaliaprogramari.animalia.domain.exception.EntityNotFoundException
import ro.animaliaprogramari.animalia.domain.exception.TimeSlotUnavailableException
import ro.animaliaprogramari.animalia.domain.exception.UnauthorizedException
import ro.animaliaprogramari.animalia.domain.exception.AppointmentSchedulingConflictException
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.domain.service.AppointmentSchedulingService
import ro.animaliaprogramari.animalia.application.port.outbound.DomainEventPublisher
import ro.animaliaprogramari.animalia.domain.validation.ValidationService
import ro.animaliaprogramari.animalia.domain.service.OptimizedSchedulingConflictService
import ro.animaliaprogramari.animalia.application.monitoring.WorkflowMetrics
import java.time.LocalDate
import java.time.LocalTime
import java.time.LocalDateTime
import java.time.ZonedDateTime
import java.time.ZoneId

/**
 * Implementation of appointment management use case
 * Handles salon-specific appointment business logic
 */
@Service
@Transactional
class AppointmentManagementUseCaseImpl(
    private val appointmentRepository: AppointmentRepository,
    private val clientRepository: ClientRepository,
    private val petRepository: PetRepository,
    private val userRepository: UserRepository,
    private val salonRepository: SalonRepository,
    private val salonServiceRepository: SalonServiceRepository,
    private val staffRepository: StaffRepository,
    private val blockTimeRepository: BlockTimeRepository,
    private val workingHoursRepository: WorkingHoursRepository,
    private val staffWorkingHoursRepository: StaffWorkingHoursRepository,
    private val domainEventPublisher: DomainEventPublisher,
    private val appointmentSchedulingService: AppointmentSchedulingService,
    private val optimizedConflictService: OptimizedSchedulingConflictService,
    private val validationService: ValidationService,
    private val appointmentAlternativeSuggestionService: AppointmentAlternativeSuggestionService,
    private val workflowMetrics: WorkflowMetrics
) : AppointmentManagementUseCase {

    private val logger = LoggerFactory.getLogger(AppointmentManagementUseCaseImpl::class.java)

    override fun scheduleAppointment(command: ScheduleAppointmentCommand): Appointment {
        return workflowMetrics.executeWorkflow(
            workflowName = "schedule_appointment",
            tags = mapOf(
                "salon_id" to command.salonId.value,
                "staff_id" to command.staffId.value,
                "is_new_client" to command.isNewClient.toString(),
                "is_new_pet" to command.isNewPet.toString(),
                "has_repetition" to (command.repetitionFrequency != null).toString()
            )
        ) {
            logger.info("=== SCHEDULING APPOINTMENT ===")
            logger.info("Original request: Date={}, Time={}-{}, Staff={}, Salon={}",
                command.appointmentDate, command.startTime, command.endTime, command.staffId.value, command.salonId.value)

            try {
                // Step 1: Validate the request
                val validationContext = workflowMetrics.executeMethod(
                    methodName = "validateAppointmentRequest",
                    className = "AppointmentManagementUseCaseImpl",
                    tags = mapOf("salon_id" to command.salonId.value)
                ) {
                    validateAppointmentRequest(command)
                }

                // Step 2: Create the appointment
                val appointment = workflowMetrics.executeMethod(
                    methodName = "createAppointment",
                    className = "AppointmentManagementUseCaseImpl"
                ) {
                    createAppointment(command, validationContext)
                }

                // Step 3: Save and handle side effects
                val savedAppointment = workflowMetrics.executeMethod(
                    methodName = "saveAppointment",
                    className = "AppointmentManagementUseCaseImpl"
                ) {
                    saveAppointment(appointment)
                }

                // Step 4: Handle recurring appointments if needed
                workflowMetrics.executeMethod(
                    methodName = "handleRecurringAppointments",
                    className = "AppointmentManagementUseCaseImpl"
                ) {
                    handleRecurringAppointments(savedAppointment, command.repetitionFrequency)
                }

                // Step 5: Publish domain events
                workflowMetrics.executeMethod(
                    methodName = "publishAppointmentEvents",
                    className = "AppointmentManagementUseCaseImpl"
                ) {
                    publishAppointmentEvents(savedAppointment)
                }

                // Record successful appointment metrics
                workflowMetrics.recordAppointmentWorkflowMetrics(
                    operation = "schedule",
                    salonId = command.salonId.value,
                    staffId = command.staffId.value,
                    success = true,
                    durationMs = 0, // Will be calculated by workflow metrics
                    conflictsDetected = 0,
                    alternativesGenerated = 0
                )

                logger.info("Appointment scheduled successfully: ${savedAppointment.id.value}")
                savedAppointment
            } catch (e: TimeSlotUnavailableException) {
                logger.warn("Scheduling conflict detected for appointment: Date={}, Time={}-{}, Staff={}",
                    command.appointmentDate, command.startTime, command.endTime, command.staffId.value)
                logger.info("Generating alternatives for conflicted appointment")

                val conflicts = findConflicts(command)
                val conflictCount = conflicts.size

                // Use SQL-based alternative suggestion for better performance and accuracy
                val alternatives = try {
                    appointmentAlternativeSuggestionService.suggestAlternatives(command, conflicts)
                } catch (ex: Exception) {
                    logger.warn("Failed to generate alternatives with new service, falling back to legacy: ${ex.message}")
                    suggestAlternatives(command, conflicts)
                }

                // Record conflict metrics
                workflowMetrics.recordAppointmentWorkflowMetrics(
                    operation = "schedule",
                    salonId = command.salonId.value,
                    staffId = command.staffId.value,
                    success = false,
                    durationMs = 0, // Will be calculated by workflow metrics
                    conflictsDetected = conflictCount,
                    alternativesGenerated = alternatives.size,
                    errorType = "TimeSlotUnavailableException"
                )

                throw AppointmentSchedulingConflictException(
                    message = "Intervalul solicitat nu este disponibil",
                    conflicts = conflicts,
                    alternatives = alternatives,
                    cause = e
                )
            } catch (e: Exception) {
                logger.error("=== UNEXPECTED EXCEPTION CAUGHT ===")
                logger.error("Exception type: {}", e.javaClass.simpleName)
                logger.error("Exception message: {}", e.message)
                logger.error("Exception cause: {}", e.cause?.message)
                logger.error("Full stack trace:", e)
                logger.error("Original appointment request:")
                logger.error("  - Date: {}", command.appointmentDate)
                logger.error("  - Time: {} - {}", command.startTime, command.endTime)
                logger.error("  - Staff: {}", command.staffId.value)
                logger.error("  - Salon: {}", command.salonId.value)

                // Record error metrics
                workflowMetrics.recordAppointmentWorkflowMetrics(
                    operation = "schedule",
                    salonId = command.salonId.value,
                    staffId = command.staffId.value,
                    success = false,
                    durationMs = 0, // Will be calculated by workflow metrics
                    errorType = e.javaClass.simpleName
                )

                throw e
            }
        }
    }

    /**
     * Validates all aspects of the appointment request
     * Returns a context object with validated entities
     */
    private fun validateAppointmentRequest(command: ScheduleAppointmentCommand): AppointmentValidationContext {
        logger.debug("--- Starting appointment request validation ---")

        // Basic validation using ValidationService
        logger.debug("Running basic validation with ValidationService...")
        validationService.validateScheduleAppointment(command).throwIfInvalid()
        logger.debug("Basic validation PASSED")

        // Load and validate entities
        logger.debug("Loading and validating salon...")
        val salon = validateAndLoadSalon(command.salonId)
        logger.debug("Salon validation PASSED")

        logger.debug("Loading and validating staff...")
        validateAndLoadStaff(command.staffId, command.salonId)
        logger.debug("Staff validation PASSED")

        logger.debug("Loading and validating client...")
        val client = validateAndLoadClient(
            command.clientId,
            command.salonId,
            command.clientName,
            command.clientPhone,
            command.isNewClient
        )
        logger.debug("Client validation PASSED")

        logger.debug("Loading and validating pet...")
        val pet = validateAndLoadPet(
            command.petId,
            client.id,
            command.petName,
            command.petSpecies,
            command.isNewPet,
            command.petBreed,
            command.petSize
        )
        logger.debug("Pet validation PASSED")

        logger.debug("Loading and validating services...")
        val services = validateAndLoadServices(command.serviceIds, command.salonId)
        logger.debug("Services validation PASSED")

        // Business rule validations
        logger.debug("Running business rule validations...")
        validateAppointmentBusinessRules(command)
        logger.debug("Business rule validations PASSED")

        logger.debug("--- Appointment request validation completed successfully ---")
        return AppointmentValidationContext(
            salon = salon,
            client = client,
            pet = pet,
            services = services
        )
    }



    private fun validateAndLoadSalon(salonId: SalonId): Salon {
        return salonRepository.findById(salonId)
            ?: throw EntityNotFoundException("Salon not found: ${salonId.value}")
    }

    private fun validateAndLoadStaff(staffId: StaffId, salonId: SalonId): Staff {
        val staffMember = staffRepository.findById(staffId);
        if (staffMember == null || !staffMember.isActive || staffMember.salonId != salonId) {
            throw BusinessRuleViolationException("Staff member is not active in this salon")
        }

        // Validate permissions using staff permissions
        if (!staffMember.permissions.canManageAppointments) {
            throw UnauthorizedException("User does not have permission to schedule appointments in this salon")
        }
        return staffMember;
    }

    private fun validateAndLoadClient(
        clientId: ClientId,
        salonId: SalonId,
        clientName: String?,
        clientPhone: PhoneNumber?,
        isNewClient: Boolean = false
    ): Client {
        return if (isNewClient) {
            // Scenario 2 & 3: Create new client
            if (clientName == null || clientPhone == null) {
                throw BusinessRuleViolationException("Client name and phone are required for new clients")
            }

            logger.debug("Creating new client: $clientName for salon: ${salonId.value}")
            val newClient = Client.create(
                name = clientName,
                phone = clientPhone
            )
            val savedClient = clientRepository.save(newClient)

            // Associate the new client with the salon
            val salon = salonRepository.findById(salonId)
                ?: throw EntityNotFoundException("Salon not found: ${salonId.value}")
            val updatedSalon = salon.addClient(savedClient.id)
            salonRepository.save(updatedSalon)

            logger.debug("New client created and associated with salon: ${savedClient.id.value}")
            savedClient
        } else {
            // Scenario 1: Existing client
            val existingClient = clientRepository.findById(clientId)
                ?: throw EntityNotFoundException("Client not found: ${clientId.value}")

            // Validate existing client belongs to salon
            val salon = salonRepository.findById(salonId)
                ?: throw EntityNotFoundException("Salon not found: ${salonId.value}")
            if (!salon.hasClient(clientId)) {
                throw BusinessRuleViolationException("Client does not belong to this salon")
            }
            existingClient
        }
    }

    private fun validateAndLoadPet(
        petId: PetId?,
        clientId: ClientId,
        petName: String?,
        petSpecies: String?,
        isNewPet: Boolean = false,
        petBreed: String?,
        petSize: String?
    ): Pet {
        return if (isNewPet || petId == null) {
            // Scenario 2 & 3: Create new pet
            if (petName == null || petBreed == null) {
                throw BusinessRuleViolationException("Pet name and breed are required for new pets")
            }

            logger.info("Creating new pet: $petName for client: ${clientId.value}")
            val newPet = Pet.create(
                clientId = clientId,
                name = petName,
                breed = petBreed,
                petSize = petSize,
                species = petSpecies
            )
            val savedPet = petRepository.save(newPet)

            logger.debug("New pet created: ${savedPet.id.value}")
            savedPet
        } else {
            // Scenario 1: Existing pet
            val existingPet = petRepository.findById(petId)
                ?: throw EntityNotFoundException("Pet not found: ${petId.value}")

            // Validate existing pet belongs to client
            if (existingPet.clientId != clientId) {
                throw BusinessRuleViolationException("Pet does not belong to the specified client")
            }
            existingPet
        }
    }

    private fun validateAndLoadServices(serviceIds: List<ServiceId>, salonId: SalonId): List<SalonService> {
        return serviceIds.map { serviceId ->
            val service = salonServiceRepository.findById(serviceId)
                ?: throw EntityNotFoundException("Service not found: ${serviceId.value}")

            if (service.salonId != salonId) {
                throw BusinessRuleViolationException("Service does not belong to this salon")
            }

            if (!service.isActive) {
                throw BusinessRuleViolationException("Service '${service.name}' is not active")
            }

            service
        }
    }

    private fun validateAppointmentBusinessRules(
        command: ScheduleAppointmentCommand
    ) {
        logger.debug("Validating appointment: {} {}-{}, staff={}",
            command.appointmentDate, command.startTime, command.endTime, command.staffId.value)

        // Load required data
        val salonHours = workingHoursRepository.findBySalonId(command.salonId)
            ?: throw BusinessRuleViolationException("Salon working hours not configured")

        val staffHours = staffWorkingHoursRepository.findByStaffIdAndSalonId(command.staffId, command.salonId)
            ?: StaffWorkingHoursSettings.createDefault(command.staffId, command.salonId)

        val existingAppointments = appointmentRepository.findBySalonIdAndStaffIdAndDate(
            command.salonId, command.staffId, command.appointmentDate
        )

        val blocks = blockTimeRepository.findOverlappingBlocks(
            command.salonId,
            command.appointmentDate.atTime(command.startTime).atZone(ZoneId.systemDefault()),
            command.appointmentDate.atTime(command.endTime).atZone(ZoneId.systemDefault()),
            setOf(command.staffId)
        )

        val conflicts = optimizedConflictService.detectConflicts(
            command.appointmentDate,
            command.startTime,
            command.endTime,
            command.staffId,
            salonHours,
            staffHours,
            existingAppointments,
            blocks
        )

        if (conflicts.isNotEmpty()) {
            val conflictReasons = conflicts.joinToString("; ") { it.reason }
            throw TimeSlotUnavailableException("Conflicts detected: $conflictReasons")
        }

        logger.debug("✅ Validation passed - no conflicts detected")
    }

    /**
     * Creates the appointment using the validated context
     */
    private fun createAppointment(
        command: ScheduleAppointmentCommand,
        context: AppointmentValidationContext
    ): Appointment {
        return Appointment.create(
            salonId = context.salon.id,
            clientId = context.client.id,
            petId = context.pet.id,
            staffId = command.staffId,
            appointmentDate = command.appointmentDate,
            startTime = command.startTime,
            endTime = command.endTime,
            salonServices = context.services,
            notes = command.notes,
            repetitionFrequency = command.repetitionFrequency
        )
    }

    /**
     * Saves the appointment and handles any persistence concerns
     */
    private fun saveAppointment(appointment: Appointment): Appointment {
        return try {
            appointmentRepository.save(appointment)
        } catch (e: Exception) {
            logger.error("Failed to save appointment", e)
            throw BusinessRuleViolationException("Failed to save appointment: ${e.message}")
        }
    }

    /**
     * Handles creation of recurring appointments
     */
    private fun handleRecurringAppointments(
        appointment: Appointment,
        repetitionFrequency: RepetitionFrequency?
    ) {
        if (repetitionFrequency == null) return

        try {
            createRecurringAppointments(appointment, repetitionFrequency)
        } catch (e: Exception) {
            logger.warn("Failed to create recurring appointments", e)
            // Don't fail the main appointment creation
        }
    }

    /**
     * Publishes domain events for the appointment
     */
    private fun publishAppointmentEvents(appointment: Appointment) {
        try {
            // TODO: Create and publish AppointmentScheduledEvent
            logger.debug("Publishing appointment events for: ${appointment.id.value}")
        } catch (e: Exception) {
            logger.warn("Failed to publish appointment event", e)
            // Don't fail the appointment creation for event publishing issues
        }
    }

    override fun getAppointmentById(query: GetAppointmentByIdQuery): Appointment? {
        logger.debug("Getting appointment by ID: ${query.appointmentId.value}")
        
        val appointment = appointmentRepository.findById(query.appointmentId)
        
        // Validate access if salon context and requester are provided
        if (appointment != null && query.salonId != null && query.requesterId != null) {
            if (appointment.salonId != query.salonId) {
                throw BusinessRuleViolationException("Appointment does not belong to the specified salon")
            }

            // Check if user has access to the salon and can view appointments
            val requesterStaff = staffRepository.findByUserIdAndSalonId(query.requesterId, query.salonId)
            if (requesterStaff == null || !requesterStaff.isActive) {
                throw UnauthorizedException("User does not have access to this salon")
            }

            // User can view if they are the assigned staff member or have appropriate permissions
            val canView = requesterStaff.role == StaffRole.CHIEF_GROOMER ||
                         requesterStaff.permissions.clientDataAccess != ClientDataAccess.NONE

            if (!canView) {
                throw UnauthorizedException("User does not have permission to view this appointment")
            }
        }
        
        return appointment
    }

    override fun getSalonAppointments(query: GetSalonAppointmentsQuery): List<Appointment> {
        logger.debug("Getting salon appointments for salon: ${query.salonId.value}")
        
        // Validate salon access
        val requesterStaff = staffRepository.findByUserIdAndSalonId(query.requesterId, query.salonId)
        if (requesterStaff == null || !requesterStaff.isActive) {
            throw UnauthorizedException("User does not have access to this salon")
        }
        
        return appointmentRepository.findBySalonIdWithFilters(
            salonId = query.salonId,
            date = query.date,
            startDate = query.startDate,
            endDate = query.endDate,
            status = query.status,
            clientId = query.clientId,
            staffId = query.staffId
        )
    }

    override fun getStaffAppointments(query: GetStaffAppointmentsQuery): List<Appointment> {
        logger.debug("Getting staff appointments for staff: ${query.staffId.value}")
        
        // Validate salon access
        val requesterStaff = staffRepository.findByUserIdAndSalonId(query.requesterId, query.salonId)
        if (requesterStaff == null || !requesterStaff.isActive) {
            throw UnauthorizedException("User does not have access to this salon")
        }
        
        return appointmentRepository.findBySalonIdAndStaffIdWithFilters(
            salonId = query.salonId,
            staffId = query.staffId,
            date = query.date,
            startDate = query.startDate,
            endDate = query.endDate,
            status = query.status
        )
    }

    override fun updateAppointment(command: UpdateAppointmentCommand): Appointment {
        logger.debug("Updating appointment: ${command.appointmentId.value}")
        
        val appointment = appointmentRepository.findById(command.appointmentId)
            ?: throw EntityNotFoundException("Appointment not found: ${command.appointmentId.value}")
        
        // Validate salon context
        if (appointment.salonId != command.salonId) {
            throw BusinessRuleViolationException("Appointment does not belong to the specified salon")
        }
        
        // Validate permission
        val updaterStaff = staffRepository.findByUserIdAndSalonId(command.updaterUserId, appointment.salonId)
        if (updaterStaff == null || !updaterStaff.isActive) {
            throw UnauthorizedException("User does not have access to this salon")
        }

        // User can modify if they are the assigned staff member or have appropriate permissions
        val canModify = appointment.staffId == updaterStaff.id ||
                       updaterStaff.role == StaffRole.CHIEF_GROOMER ||
                       updaterStaff.permissions.canManageAppointments

        if (!canModify) {
            throw UnauthorizedException("User does not have permission to modify this appointment")
        }
        
        // TODO: Implement update logic with validation
        // This is a complex operation that needs careful validation of each field
        
        throw NotImplementedError("Update appointment functionality not yet implemented")
    }

    override fun cancelAppointment(command: CancelAppointmentCommand): Appointment {
        return workflowMetrics.executeWorkflow(
            workflowName = "cancel_appointment",
            tags = mapOf(
                "salon_id" to command.salonId.value,
                "appointment_id" to command.appointmentId.value
            )
        ) {
            logger.debug("Cancelling appointment: ${command.appointmentId.value}")

            val appointment = appointmentRepository.findById(command.appointmentId)
                ?: throw EntityNotFoundException("Appointment not found: ${command.appointmentId.value}")

            // Validate salon context
            if (appointment.salonId != command.salonId) {
                throw BusinessRuleViolationException("Appointment does not belong to the specified salon")
            }

            // Validate permission
            val cancellerStaff = staffRepository.findByUserIdAndSalonId(command.cancellerUserId, appointment.salonId)
            if (cancellerStaff == null || !cancellerStaff.isActive) {
                throw UnauthorizedException("User does not have access to this salon")
            }

            // User can cancel if they are the assigned staff member or have appropriate permissions
            val canCancel = appointment.staffId == appointment.staffId ||
                           cancellerStaff.role == StaffRole.CHIEF_GROOMER ||
                           cancellerStaff.permissions.canManageAppointments

            if (!canCancel) {
                throw UnauthorizedException("User does not have permission to cancel this appointment")
            }

            val cancelledAppointment = appointment.cancel(command.reason)
            val savedAppointment = appointmentRepository.save(cancelledAppointment)

            // Record successful cancellation metrics
            workflowMetrics.recordAppointmentWorkflowMetrics(
                operation = "cancel",
                salonId = command.salonId.value,
                staffId = appointment.staffId.value,
                success = true,
                durationMs = 0 // Will be calculated by workflow metrics
            )

            logger.debug("Appointment cancelled: ${savedAppointment.id.value}")
            savedAppointment
        }
    }

    override fun completeAppointment(command: CompleteAppointmentCommand): Appointment {
        logger.debug("Completing appointment: ${command.appointmentId.value}")
        
        val appointment = appointmentRepository.findById(command.appointmentId)
            ?: throw EntityNotFoundException("Appointment not found: ${command.appointmentId.value}")
        
        // Validate salon context
        if (appointment.salonId != command.salonId) {
            throw BusinessRuleViolationException("Appointment does not belong to the specified salon")
        }
        
        // Validate permission
        val completerStaff = staffRepository.findByUserIdAndSalonId(command.completerUserId, appointment.salonId)
        if (completerStaff == null || !completerStaff.isActive) {
            throw UnauthorizedException("User does not have access to this salon")
        }

        // User can complete if they are the assigned staff member or have appropriate permissions
        val canComplete = appointment.staffId == appointment.staffId ||
                         completerStaff.role == StaffRole.CHIEF_GROOMER ||
                         completerStaff.permissions.canManageAppointments

        if (!canComplete) {
            throw UnauthorizedException("User does not have permission to complete this appointment")
        }
        
        val completedAppointment = appointment.complete(command.notes)
        val savedAppointment = appointmentRepository.save(completedAppointment)
        
        logger.debug("Appointment completed: ${savedAppointment.id.value}")
        return savedAppointment
    }

    override fun rescheduleAppointment(command: RescheduleAppointmentCommand): Appointment {
        logger.debug("Rescheduling appointment: ${command.appointmentId.value}")

        // Step 1: Load and validate the existing appointment
        val appointment = appointmentRepository.findById(command.appointmentId)
            ?: throw EntityNotFoundException("Appointment not found: ${command.appointmentId.value}")

        // Step 2: Validate salon context
        if (appointment.salonId != command.salonId) {
            throw BusinessRuleViolationException("Appointment does not belong to the specified salon")
        }

        // Step 3: Validate permission
        val reschedulerStaff = staffRepository.findByUserIdAndSalonId(command.reschedulerUserId, appointment.salonId)
        if (reschedulerStaff == null || !reschedulerStaff.isActive) {
            throw UnauthorizedException("User does not have access to this salon")
        }

        // User can reschedule if they are the assigned staff member or have appropriate permissions
        val canReschedule = appointment.staffId == appointment.staffId ||
                           reschedulerStaff.role == StaffRole.CHIEF_GROOMER ||
                           reschedulerStaff.permissions.canManageAppointments

        if (!canReschedule) {
            throw UnauthorizedException("User does not have permission to reschedule this appointment")
        }

        // Step 4: Validate appointment can be rescheduled
        if (!appointment.canBeRescheduled()) {
            throw BusinessRuleViolationException("Appointment cannot be rescheduled in current status: ${appointment.status}")
        }

        // Step 5: Determine new date and staff
        val newDate = command.newDate ?: appointment.appointmentDate
        val newStaffId = command.newstaffId ?: appointment.staffId

        // Step 6: Validate new time slot availability
        validateRescheduleTimeSlot(
            appointment = appointment,
            newDate = newDate,
            newStartTime = command.newStartTime,
            newEndTime = command.newEndTime,
            newStaffId = newStaffId
        )

        // Step 7: Reschedule the appointment
        val rescheduledAppointment = appointment.reschedule(
            newDate = newDate,
            newStartTime = command.newStartTime,
            newEndTime = command.newEndTime,
            newStaffId = newStaffId
        )

        // Step 8: Add reschedule reason to notes if provided
        val finalAppointment = if (command.reason != null) {
            rescheduledAppointment.updateNotes(
                if (rescheduledAppointment.notes.isNullOrBlank()) {
                    "Rescheduled: ${command.reason}"
                } else {
                    "${rescheduledAppointment.notes}\nRescheduled: ${command.reason}"
                }
            )
        } else {
            rescheduledAppointment
        }

        // Step 9: Save the rescheduled appointment
        val savedAppointment = appointmentRepository.save(finalAppointment)

        logger.info("Appointment rescheduled successfully: ${savedAppointment.id.value}")
        return savedAppointment
    }

    override fun markNoShow(command: MarkNoShowCommand): Appointment {
        throw NotImplementedError("Mark no-show functionality not yet implemented")
    }

    override fun markInProgress(command: MarkInProgressCommand): Appointment {
        throw NotImplementedError("Mark in-progress functionality not yet implemented")
    }

    override fun checkAvailability(query: CheckAvailabilityQuery): AvailabilityResult {
        throw NotImplementedError("Check availability functionality not yet implemented")
    }

    override fun getConflictingAppointments(query: GetConflictingAppointmentsQuery): List<Appointment> {
        throw NotImplementedError("Get conflicting appointments functionality not yet implemented")
    }

    override fun getAvailableSlots(query: GetAvailableSlotsQuery): List<TimeSlot> {
        throw NotImplementedError("Get available slots functionality not yet implemented")
    }

    override fun getAppointmentSummary(query: GetAppointmentSummaryQuery): AppointmentSummary {
        throw NotImplementedError("Get appointment summary functionality not yet implemented")
    }

    override fun createBulkAppointments(command: CreateBulkAppointmentsCommand): List<Appointment> {
        throw NotImplementedError("Create bulk appointments functionality not yet implemented")
    }

    override fun deleteAppointment(command: DeleteAppointmentCommand): Boolean {
        throw NotImplementedError("Delete appointment functionality not yet implemented")
    }

    // Legacy support methods
    override fun getAppointmentsByDateRange(query: GetAppointmentsByDateRangeQuery): List<Appointment> {
        return appointmentRepository.findByDateRange(query.dateRange.startDate, query.dateRange.endDate, query.staffId, query.status)
    }

    override fun getAppointmentsByGroomer(query: GetAppointmentsByGroomerQuery): List<Appointment> {
        return appointmentRepository.findByStaffId(query.staffId, query.dateRange.startDate, query.dateRange.endDate, query.status)
    }

    override fun checkGroomerAvailability(query: CheckGroomerAvailabilityQuery): AvailabilityResult {
        val existingAppointments = appointmentRepository.findByStaffIdAndDate(query.staffId, query.date)
        val hasConflict = existingAppointments.any { appointment ->
            appointment.isActive() &&
            appointment.overlaps(query.timeSlot.startTime, query.timeSlot.endTime)
        }
        
        return AvailabilityResult(
            isAvailable = !hasConflict,
            reason = if (hasConflict) "Time slot conflicts with existing appointment" else null,
            conflictingAppointments = if (hasConflict) {
                existingAppointments.filter { it.overlaps(query.timeSlot.startTime, query.timeSlot.endTime) }.map { it.id }
            } else emptyList()
        )
    }

    private fun createRecurringAppointments(baseAppointment: Appointment, frequency: RepetitionFrequency) {
        logger.debug("Creating recurring appointments for frequency: $frequency")

        val serviceList = salonServiceRepository.findByIds(baseAppointment.serviceIds)
        val dates = frequency.generateDates(baseAppointment.appointmentDate, 10)

        dates.forEach { nextDate ->
            if (isSlotAvailable(baseAppointment.salonId, baseAppointment.staffId, nextDate, baseAppointment.startTime, baseAppointment.endTime)) {
                val newAppt = Appointment.create(
                    salonId = baseAppointment.salonId,
                    clientId = baseAppointment.clientId,
                    petId = baseAppointment.petId,
                    staffId = baseAppointment.staffId,
                    appointmentDate = nextDate,
                    startTime = baseAppointment.startTime,
                    endTime = baseAppointment.endTime,
                    salonServices = serviceList,
                    notes = baseAppointment.notes,
                    repetitionFrequency = frequency
                )
                appointmentRepository.save(newAppt)
            }
        }
    }

    /**
     * Validates that the new time slot is available for rescheduling
     */
    private fun validateRescheduleTimeSlot(
        appointment: Appointment,
        newDate: LocalDate,
        newStartTime: LocalTime,
        newEndTime: LocalTime,
        newStaffId: StaffId
    ) {
        // Validate basic time constraints
        require(newStartTime.isBefore(newEndTime)) { "Start time must be before end time" }

        // Check for block time conflicts first
        validateRescheduleBlockTimeConflicts(appointment.salonId, newDate, newStartTime, newEndTime, newStaffId)

        // Get existing appointments for the new staff member and date
        val existingAppointments = appointmentRepository.findBySalonIdAndStaffIdAndDate(
            appointment.salonId, newStaffId, newDate
        )

        // Validate time slot availability (excluding the current appointment being rescheduled)
        appointmentSchedulingService.validateTimeSlotAvailability(
            newStaffId,
            newDate,
            newStartTime,
            newEndTime,
            existingAppointments,
            excludeAppointmentId = appointment.id
        )

        // If staff is changing, validate the new staff member
        if (newStaffId != appointment.staffId) {
            validateAndLoadStaff(newStaffId, appointment.salonId)
            // Note: Staff availability validation is handled by validateTimeSlotAvailability
            // which checks for conflicting appointments
        }

        // Validate appointment timing (not in the past, reasonable advance notice)
        val now = LocalDateTime.now()
        val newAppointmentDateTime = LocalDateTime.of(newDate, newStartTime)

        if (newAppointmentDateTime.isBefore(now)) {
            throw BusinessRuleViolationException("Cannot reschedule appointments to the past")
        }

        // Require at least 1 hour advance notice for same-day rescheduling
        if (newDate == now.toLocalDate() &&
            newAppointmentDateTime.isBefore(now.plusHours(1))) {
            throw BusinessRuleViolationException(
                "Same-day rescheduling requires at least 1 hour advance notice"
            )
        }
    }

    /**
     * Validate that the reschedule doesn't conflict with any block times
     */
    private fun validateRescheduleBlockTimeConflicts(
        salonId: SalonId,
        newDate: LocalDate,
        newStartTime: LocalTime,
        newEndTime: LocalTime,
        newStaffId: StaffId
    ) {
        // Convert appointment time to ZonedDateTime for comparison with block times
        val appointmentStartDateTime = ZonedDateTime.of(
            newDate,
            newStartTime,
            ZoneId.systemDefault()
        )
        val appointmentEndDateTime = ZonedDateTime.of(
            newDate,
            newEndTime,
            ZoneId.systemDefault()
        )

        // Find overlapping block times for this staff member
        val overlappingBlocks = blockTimeRepository.findOverlappingBlocks(
            salonId = salonId,
            startTime = appointmentStartDateTime,
            endTime = appointmentEndDateTime,
            staffIds = setOf(newStaffId)
        )

        // Check if any active block times conflict with the appointment
        val conflictingBlocks = overlappingBlocks.filter { blockTime ->
            blockTime.isActive() && blockTime.affectsStaff(newStaffId)
        }

        if (conflictingBlocks.isNotEmpty()) {
            val blockDetails = conflictingBlocks.joinToString(", ") { block ->
                "${block.getDisplayReason()} (${block.startTime.toLocalTime()}-${block.endTime.toLocalTime()})"
            }
            throw TimeSlotUnavailableException(
                "Cannot reschedule appointment during blocked time: $blockDetails"
            )
        }
    }

    /** Helper to collect conflicting appointments and block times */
    private fun findConflicts(command: ScheduleAppointmentCommand): List<AppointmentConflictItem> {
        val conflicts = mutableListOf<AppointmentConflictItem>()

        val startDateTime = command.appointmentDate.atTime(command.startTime).atZone(ZoneId.systemDefault())
        val endDateTime = command.appointmentDate.atTime(command.endTime).atZone(ZoneId.systemDefault())

        val staffName = staffRepository.findById(command.staffId)?.nickname ?: """Unknown Staff"""

        val overlappingBlocks = blockTimeRepository.findOverlappingBlocks(
            command.salonId,
            startDateTime,
            endDateTime,
            setOf(command.staffId)
        )
        overlappingBlocks.filter { it.isActive() && it.affectsStaff(command.staffId) }.forEach { block ->
            conflicts.add(
                AppointmentConflictItem(
                    id = block.id.value,
                    staffId = command.staffId,
                    staffName = staffName,
                    type = ConflictItemType.BLOCK_TIME,
                    date = block.startTime.toLocalDate(),
                    startTime = block.startTime.toLocalTime(),
                    endTime = block.endTime.toLocalTime()
                )
            )
        }

        val existingAppointments = appointmentRepository.findBySalonIdAndStaffIdAndDate(
            command.salonId,
            command.staffId,
            command.appointmentDate
        )
        existingAppointments.filter {
            it.isActive() && it.overlaps(command.startTime, command.endTime)
        }.forEach { appt ->
            conflicts.add(
                AppointmentConflictItem(
                    id = appt.id.value,
                    staffId = appt.staffId,
                    staffName = staffName,
                    type = ConflictItemType.APPOINTMENT,
                    date = appt.appointmentDate,
                    startTime = appt.startTime,
                    endTime = appt.endTime
                )
            )
        }

        return conflicts
    }

    /** Generate alternative suggestions based on business rules */
    private fun suggestAlternatives(
        command: ScheduleAppointmentCommand,
        conflicts: List<AppointmentConflictItem>
    ): List<AlternativeSuggestion> {
        logger.info("Generating alternatives for appointment: salon=${command.salonId}, staff=${command.staffId}, date=${command.appointmentDate}, time=${command.startTime}-${command.endTime}")

        val suggestions = mutableListOf<AlternativeSuggestion>()
        val staff = staffRepository.findById(command.staffId)
        val staffName = staff?.nickname ?: """Unknown Staff"""

        // Pre-load data for efficient alternative validation
        val validationContext = AlternativeValidationContext.create(
            command.salonId,
            command.staffId,
            workingHoursRepository,
            staffWorkingHoursRepository,
            appointmentRepository,
            blockTimeRepository
        )

        // Time-based alternatives +/- 1-3 hours
        for (offset in 1..3) {
            if (suggestions.size >= 5) break
            val laterStart = command.startTime.plusHours(offset.toLong())
            val laterEnd = command.endTime.plusHours(offset.toLong())
            if (isSlotAvailableWithContext(validationContext, command.appointmentDate, laterStart, laterEnd)) {
                suggestions.add(
                    AlternativeSuggestion(
                        type = AlternativeType.TIME_ADJUSTMENT,
                        priority = 1,
                        suggestion = AlternativeSlot(
                            date = command.appointmentDate,
                            startTime = laterStart,
                            endTime = laterEnd,
                            staffId = command.staffId.value,
                            staffName = staffName
                        ),
                        reason = "Același personal, cu $offset oră mai târziu",
                        confidence = 0.9 - (offset - 1) * 0.1
                    )
                )
                logger.debug("Added later alternative: ${command.appointmentDate} ${laterStart}-${laterEnd}")
            } else {
                logger.debug("Later alternative not available: ${command.appointmentDate} ${laterStart}-${laterEnd}")
            }

            if (suggestions.size >= 5) break
            val earlierStart = command.startTime.minusHours(offset.toLong())
            val earlierEnd = command.endTime.minusHours(offset.toLong())
            if (earlierStart >= LocalTime.MIN && isSlotAvailableWithContext(validationContext, command.appointmentDate, earlierStart, earlierEnd)) {
                suggestions.add(
                    AlternativeSuggestion(
                        type = AlternativeType.TIME_ADJUSTMENT,
                        priority = 1,
                        suggestion = AlternativeSlot(
                            date = command.appointmentDate,
                            startTime = earlierStart,
                            endTime = earlierEnd,
                            staffId = command.staffId.value,
                            staffName = staffName
                        ),
                        reason = "Același personal, cu $offset oră mai devreme",
                        confidence = 0.9 - (offset - 1) * 0.1
                    )
                )
                logger.debug("Added earlier alternative: ${command.appointmentDate} ${earlierStart}-${earlierEnd}")
            } else {
                logger.debug("Earlier alternative not available: ${command.appointmentDate} ${earlierStart}-${earlierEnd}")
            }
        }

        // Day-based alternatives +/- up to 7 days
        for (dayOffset in 1..7) {
            if (suggestions.size >= 5) break
            val dateBefore = command.appointmentDate.minusDays(dayOffset.toLong())
            if (isSlotAvailableWithContext(validationContext, dateBefore, command.startTime, command.endTime)) {
                suggestions.add(
                    AlternativeSuggestion(
                        type = AlternativeType.DAY_ADJUSTMENT,
                        priority = 2,
                        suggestion = AlternativeSlot(
                            date = dateBefore,
                            startTime = command.startTime,
                            endTime = command.endTime,
                            staffId = command.staffId.value,
                            staffName = staffName
                        ),
                        reason = "Același personal, ${dayOffset} zile mai devreme",
                        confidence = 0.8 - (dayOffset - 1) * 0.05
                    )
                )
                logger.debug("Added earlier date alternative: ${dateBefore} ${command.startTime}-${command.endTime}")
            } else {
                logger.debug("Earlier date alternative not available: ${dateBefore} ${command.startTime}-${command.endTime}")
            }

            if (suggestions.size >= 5) break
            val dateAfter = command.appointmentDate.plusDays(dayOffset.toLong())
            if (isSlotAvailableWithContext(validationContext, dateAfter, command.startTime, command.endTime)) {
                suggestions.add(
                    AlternativeSuggestion(
                        type = AlternativeType.DAY_ADJUSTMENT,
                        priority = 2,
                        suggestion = AlternativeSlot(
                            date = dateAfter,
                            startTime = command.startTime,
                            endTime = command.endTime,
                            staffId = command.staffId.value,
                            staffName = staffName
                        ),
                        reason = "Același personal, ${dayOffset} zile mai târziu",
                        confidence = 0.8 - (dayOffset - 1) * 0.05
                    )
                )
                logger.debug("Added later date alternative: ${dateAfter} ${command.startTime}-${command.endTime}")
            } else {
                logger.debug("Later date alternative not available: ${dateAfter} ${command.startTime}-${command.endTime}")
            }
        }

        if (suggestions.size < 5) {
            val availableStaff = staffRepository.findAvailableStaff(
                command.salonId,
                command.appointmentDate,
                TimeSlot(command.startTime, command.endTime),
                listOf(command.staffId)
            )
            for (other in availableStaff) {
                if (suggestions.size >= 5) break
                suggestions.add(
                    AlternativeSuggestion(
                        type = AlternativeType.STAFF_ALTERNATIVE,
                        priority = 3,
                        suggestion = AlternativeSlot(
                            date = command.appointmentDate,
                            startTime = command.startTime,
                            endTime = command.endTime,
                            staffId = other.id.value,
                            staffName = other.nickname ?: "Alt staff"
                        ),
                        reason = "Personal alternativ disponibil",
                        confidence = 0.7
                    )
                )
                logger.debug("Added staff alternative: ${other.nickname} for ${command.appointmentDate} ${command.startTime}-${command.endTime}")
            }
        }

        logger.info("Generated ${suggestions.size} alternatives for appointment")
        return suggestions.take(5)
    }

    private fun isSlotAvailable(
        salonId: SalonId,
        staffId: StaffId,
        date: LocalDate,
        start: LocalTime,
        end: LocalTime
    ): Boolean {
        val salonHours = workingHoursRepository.findBySalonId(salonId)
        val staffHours = staffWorkingHoursRepository.findByStaffIdAndSalonId(staffId, salonId)
            ?: StaffWorkingHoursSettings.createDefault(staffId, salonId)
        val existingAppointments = appointmentRepository.findBySalonIdAndStaffIdAndDate(salonId, staffId, date)
        val blocks = blockTimeRepository.findOverlappingBlocks(
            salonId,
            date.atTime(start).atZone(ZoneId.systemDefault()),
            date.atTime(end).atZone(ZoneId.systemDefault()),
            setOf(staffId)
        )
        return optimizedConflictService.isSlotAvailable(
            date,
            start,
            end,
            staffId,
            salonHours,
            staffHours,
            existingAppointments,
            blocks
        )
    }

    /**
     * Optimized slot availability check using pre-loaded context data
     */
    private fun isSlotAvailableWithContext(
        context: AlternativeValidationContext,
        date: LocalDate,
        start: LocalTime,
        end: LocalTime
    ): Boolean {
        // Get appointments for the specific date (cached or loaded on demand)
        val existingAppointments = context.getAppointmentsForDate(date)

        // Get block times for the specific time range
        val blocks = context.getBlockTimesForRange(date, start, end)

        val isAvailable = optimizedConflictService.isSlotAvailable(
            date,
            start,
            end,
            context.staffId,
            context.salonHours,
            context.staffHours,
            existingAppointments,
            blocks
        )

        logger.debug("Slot availability check: date=$date, time=$start-$end, available=$isAvailable")
        return isAvailable
    }
}

/**
 * Context for efficient alternative validation with pre-loaded data
 */
private class AlternativeValidationContext(
    val salonId: SalonId,
    val staffId: StaffId,
    val salonHours: WorkingHoursSettings,
    val staffHours: StaffWorkingHoursSettings,
    private val appointmentRepository: AppointmentRepository,
    private val blockTimeRepository: BlockTimeRepository
) {
    private val appointmentCache = mutableMapOf<LocalDate, List<Appointment>>()
    private val blockTimeCache = mutableMapOf<String, List<BlockTime>>()

    fun getAppointmentsForDate(date: LocalDate): List<Appointment> {
        return appointmentCache.getOrPut(date) {
            appointmentRepository.findBySalonIdAndStaffIdAndDate(salonId, staffId, date)
        }
    }

    fun getBlockTimesForRange(date: LocalDate, start: LocalTime, end: LocalTime): List<BlockTime> {
        val key = "${date}_${start}_${end}"
        return blockTimeCache.getOrPut(key) {
            blockTimeRepository.findOverlappingBlocks(
                salonId,
                date.atTime(start).atZone(ZoneId.systemDefault()),
                date.atTime(end).atZone(ZoneId.systemDefault()),
                setOf(staffId)
            )
        }
    }

    companion object {
        fun create(
            salonId: SalonId,
            staffId: StaffId,
            workingHoursRepository: WorkingHoursRepository,
            staffWorkingHoursRepository: StaffWorkingHoursRepository,
            appointmentRepository: AppointmentRepository,
            blockTimeRepository: BlockTimeRepository
        ): AlternativeValidationContext {
            val salonHours = workingHoursRepository.findBySalonId(salonId)
                ?: throw BusinessRuleViolationException("Salon working hours not configured for salon ${salonId.value}")
            val staffHours = staffWorkingHoursRepository.findByStaffIdAndSalonId(staffId, salonId)
                ?: StaffWorkingHoursSettings.createDefault(staffId, salonId)

            return AlternativeValidationContext(
                salonId,
                staffId,
                salonHours,
                staffHours,
                appointmentRepository,
                blockTimeRepository
            )
        }
    }
}

/**
 * Context object to hold validated entities
 * Eliminates the need to pass multiple parameters around
 */
data class AppointmentValidationContext(
    val salon: Salon,
    val client: Client,
    val pet: Pet,
    val services: List<SalonService>
)
