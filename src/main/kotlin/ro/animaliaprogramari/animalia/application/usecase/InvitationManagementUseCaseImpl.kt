package ro.animaliaprogramari.animalia.application.usecase

import org.slf4j.LoggerFactory
import org.springframework.transaction.annotation.Transactional
import ro.animaliaprogramari.animalia.application.command.*
import ro.animaliaprogramari.animalia.application.port.inbound.InvitationManagementUseCase
import ro.animaliaprogramari.animalia.application.port.outbound.SalonInvitationRepository
import ro.animaliaprogramari.animalia.application.port.outbound.SalonRepository
import ro.animaliaprogramari.animalia.application.port.outbound.StaffRepository
import ro.animaliaprogramari.animalia.application.port.outbound.UserRepository
import ro.animaliaprogramari.animalia.application.query.*
import ro.animaliaprogramari.animalia.domain.exception.BusinessRuleViolationException
import ro.animaliaprogramari.animalia.domain.exception.EntityNotFoundException
import ro.animaliaprogramari.animalia.domain.exception.UnauthorizedException
import ro.animaliaprogramari.animalia.domain.model.*

/**
 * Implementation of invitation management use case
 * Handles all invitation-related business logic
 */
@Transactional
class InvitationManagementUseCaseImpl(
    private val invitationRepository: SalonInvitationRepository,
    private val userRepository: UserRepository,
    private val salonRepository: SalonRepository,
    private val staffRepository: StaffRepository
) : InvitationManagementUseCase {

    private val logger = LoggerFactory.getLogger(InvitationManagementUseCaseImpl::class.java)

    override fun sendInvitation(command: SendSalonInvitationCommand): SalonInvitation {
        logger.debug("Sending invitation for salon: ${command.salonId.value} to phone: ${command.invitedUserPhone}")

        // Validate salon exists
        salonRepository.findById(command.salonId)
            ?: throw EntityNotFoundException("Salon not found: ${command.salonId.value}")

        // Validate inviter exists and has permission
        val inviter = userRepository.findById(command.inviterUserId)
            ?: throw EntityNotFoundException("Inviter user not found: ${command.inviterUserId.value}")

        // Check if inviter has permission to send invitations for this salon
        val inviterStaff = staffRepository.findByUserIdAndSalonId(
            command.inviterUserId, command.salonId
        )

        if (inviterStaff == null || !inviterStaff.isActive) {
            throw UnauthorizedException("User is not associated with this salon")
        }

        // Only chief groomers and admins can send invitations
        if (inviterStaff.role != StaffRole.CHIEF_GROOMER && inviter.role != UserRole.ADMIN) {
            throw UnauthorizedException("Only chief groomers and admins can send invitations")
        }

        // Check if there's already a pending invitation for this phone and salon
        if (invitationRepository.existsPendingInvitation(command.salonId, command.invitedUserPhone)) {
            throw BusinessRuleViolationException("There is already a pending invitation for this phone number and salon")
        }

        // Check if user is already associated with the salon
        val existingUser = userRepository.findByPhoneNumber(command.invitedUserPhone)
        if (existingUser != null) {
            val existingStaff = staffRepository.findByUserIdAndSalonId(
                existingUser.id, command.salonId
            )
            if (existingStaff != null && existingStaff.isActive) {
                throw BusinessRuleViolationException("User is already associated with this salon")
            }
        }

        // Create and save invitation
        val invitation = SalonInvitation.create(
            salonId = command.salonId,
            inviterUserId = command.inviterUserId,
            invitedUserPhone = command.invitedUserPhone,
            proposedRole = command.proposedRole,
            proposedPermissions = command.proposedPermissions,
            message = command.message
        )

        val savedInvitation = invitationRepository.save(invitation)
        logger.info("Invitation sent successfully with ID: ${savedInvitation.id.value}")

        return savedInvitation
    }

    override fun acceptInvitation(command: AcceptSalonInvitationCommand): Staff {
        logger.debug("Accepting invitation: ${command.invitationId.value}")

        // Find invitation
        val invitation = invitationRepository.findById(command.invitationId)
            ?: throw EntityNotFoundException("Invitation not found: ${command.invitationId.value}")

        // Validate invitation can be accepted
        if (!invitation.canBeResponded()) {
            throw BusinessRuleViolationException("Invitation cannot be accepted (status: ${invitation.status}, expired: ${invitation.isExpired()})")
        }

        // Find accepting user
        val user = userRepository.findById(command.acceptingUserId)
            ?: throw EntityNotFoundException("User not found: ${command.acceptingUserId.value}")

        // Verify the user's phone matches the invitation
        if (user.phoneNumber != invitation.invitedUserPhone) {
            throw UnauthorizedException("User phone number does not match invitation")
        }

        // Check if user is already associated with the salon
        val existingStaff = staffRepository.findByUserIdAndSalonId(
            command.acceptingUserId, invitation.salonId
        )
        if (existingStaff != null && existingStaff.isActive) {
            throw BusinessRuleViolationException("User is already associated with this salon")
        }

        // Accept invitation
        val acceptedInvitation = invitation.accept()
        invitationRepository.save(acceptedInvitation)

        // Create staff member
        val staff = if (invitation.proposedRole == StaffRole.CHIEF_GROOMER) {
            Staff.createChiefGroomer(command.acceptingUserId, invitation.salonId)
        } else {
            Staff.createGroomer(command.acceptingUserId, invitation.salonId, invitation.proposedPermissions)
        }

        val savedStaff = staffRepository.save(staff)
        logger.debug("Invitation accepted and staff created: ${savedStaff.userId.value} -> ${savedStaff.salonId.value}")

        return savedStaff
    }

    override fun declineInvitation(command: DeclineSalonInvitationCommand): SalonInvitation {
        logger.debug("Declining invitation: ${command.invitationId.value}")

        // Find invitation
        val invitation = invitationRepository.findById(command.invitationId)
            ?: throw EntityNotFoundException("Invitation not found: ${command.invitationId.value}")

        // Validate invitation can be declined
        if (!invitation.canBeResponded()) {
            throw BusinessRuleViolationException("Invitation cannot be declined (status: ${invitation.status}, expired: ${invitation.isExpired()})")
        }

        // Find declining user
        val user = userRepository.findById(command.decliningUserId)
            ?: throw EntityNotFoundException("User not found: ${command.decliningUserId.value}")

        // Verify the user's phone matches the invitation
        if (user.phoneNumber != invitation.invitedUserPhone) {
            throw UnauthorizedException("User phone number does not match invitation")
        }

        // Decline invitation
        val declinedInvitation = invitation.decline()
        val savedInvitation = invitationRepository.save(declinedInvitation)

        logger.debug("Invitation declined successfully")
        return savedInvitation
    }

    override fun cancelInvitation(command: CancelSalonInvitationCommand): SalonInvitation {
        logger.debug("Cancelling invitation: ${command.invitationId.value}")

        // Find invitation
        val invitation = invitationRepository.findById(command.invitationId)
            ?: throw EntityNotFoundException("Invitation not found: ${command.invitationId.value}")

        // Validate invitation can be cancelled
        if (invitation.status != InvitationStatus.PENDING) {
            throw BusinessRuleViolationException("Only pending invitations can be cancelled")
        }

        // Validate cancelling user has permission
        val user = userRepository.findById(command.cancellingUserId)
            ?: throw EntityNotFoundException("User not found: ${command.cancellingUserId.value}")

        // Check if user is the inviter or has admin/chief groomer privileges
        val canCancel = invitation.inviterUserId == command.cancellingUserId ||
                user.role == UserRole.ADMIN ||
                staffRepository.findByUserIdAndSalonId(command.cancellingUserId, invitation.salonId)
                    ?.let { it.isActive && it.role == StaffRole.CHIEF_GROOMER } == true

        if (!canCancel) {
            throw UnauthorizedException("User does not have permission to cancel this invitation")
        }

        // Cancel invitation with audit trail
        val cancelledInvitation = invitation.cancel(command.cancellingUserId)
        val savedInvitation = invitationRepository.save(cancelledInvitation)

        logger.debug("Invitation cancelled successfully")
        return savedInvitation
    }

    override fun resendInvitation(command: ResendSalonInvitationCommand): SalonInvitation {
        logger.debug("Resending invitation: ${command.invitationId.value}")

        // Find invitation
        val invitation = invitationRepository.findById(command.invitationId)
            ?: throw EntityNotFoundException("Invitation not found: ${command.invitationId.value}")

        // Validate invitation can be resent (must be pending but can be expired)
        if (invitation.status != InvitationStatus.PENDING) {
            throw BusinessRuleViolationException("Only pending invitations can be resent")
        }

        // Validate resending user has permission
        val user = userRepository.findById(command.resendingUserId)
            ?: throw EntityNotFoundException("User not found: ${command.resendingUserId.value}")

        // Check if user is the inviter or has admin/chief groomer privileges
        val canResend = invitation.inviterUserId == command.resendingUserId ||
                user.role == UserRole.ADMIN ||
                staffRepository.findByUserIdAndSalonId(command.resendingUserId, invitation.salonId)
                    ?.let { it.isActive && it.role == StaffRole.CHIEF_GROOMER } == true

        if (!canResend) {
            throw UnauthorizedException("User does not have permission to resend this invitation")
        }

        // Update invitation with resend tracking and extend expiry
        val resentInvitation = invitation.resend().let { resent ->
            // Update message if provided
            if (command.newMessage != null) {
                resent.copy(message = command.newMessage)
            } else {
                resent
            }
        }

        // Save updated invitation
        val savedInvitation = invitationRepository.save(resentInvitation)
        logger.debug("Invitation resent successfully. Resend count: ${savedInvitation.resendCount}")

        return savedInvitation
    }

    @Transactional(readOnly = true)
    override fun getInvitationById(query: GetInvitationByIdQuery): SalonInvitation? {
        return invitationRepository.findById(query.invitationId)
    }

    @Transactional(readOnly = true)
    override fun getPendingInvitationsByPhone(query: GetPendingInvitationsByPhoneQuery): List<SalonInvitation> {
        return invitationRepository.findPendingByInvitedUserPhone(query.phoneNumber)
    }

    @Transactional(readOnly = true)
    override fun getInvitationsBySalon(query: GetInvitationsBySalonQuery): List<SalonInvitation> {
        return if (query.status != null) {
            invitationRepository.findBySalonId(query.salonId)
                .filter { it.status == query.status }
        } else {
            invitationRepository.findBySalonId(query.salonId)
        }
    }

    @Transactional(readOnly = true)
    override fun getInvitationsByInviter(query: GetInvitationsByInviterQuery): List<SalonInvitation> {
        return invitationRepository.findByInviterUserId(query.inviterUserId)
    }

    @Transactional(readOnly = true)
    override fun getPendingInvitationsBySalon(query: GetPendingInvitationsBySalonQuery): List<SalonInvitation> {
        return invitationRepository.findPendingBySalonId(query.salonId)
    }

    @Transactional(readOnly = true)
    override fun hasPendingInvitations(query: HasPendingInvitationsQuery): Boolean {
        return invitationRepository.findPendingByInvitedUserPhone(query.phoneNumber).isNotEmpty()
    }

    override fun processExpiredInvitations(): Int {
        logger.debug("Processing expired invitations")

        val expiredInvitations = invitationRepository.findExpiredInvitations()
        var processedCount = 0

        expiredInvitations.forEach { invitation ->
            try {
                val expiredInvitation = invitation.copy(
                    status = InvitationStatus.EXPIRED,
                    updatedAt = java.time.LocalDateTime.now()
                )
                invitationRepository.save(expiredInvitation)
                processedCount++
            } catch (e: Exception) {
                logger.error("Failed to mark invitation as expired: ${invitation.id.value}", e)
            }
        }

        logger.debug("Processed $processedCount expired invitations")
        return processedCount
    }
}
