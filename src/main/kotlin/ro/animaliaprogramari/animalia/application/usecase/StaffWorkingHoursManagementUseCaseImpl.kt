package ro.animaliaprogramari.animalia.application.usecase

import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import ro.animaliaprogramari.animalia.application.command.UpdateStaffWorkingHoursCommand
import ro.animaliaprogramari.animalia.application.port.inbound.StaffWorkingHoursManagementUseCase
import ro.animaliaprogramari.animalia.application.port.outbound.AppointmentRepository
import ro.animaliaprogramari.animalia.application.port.outbound.StaffRepository
import ro.animaliaprogramari.animalia.application.port.outbound.StaffWorkingHoursRepository
import ro.animaliaprogramari.animalia.application.query.GetStaffAvailabilityQuery
import ro.animaliaprogramari.animalia.application.query.GetStaffWorkingHoursQuery
import ro.animaliaprogramari.animalia.domain.exception.BusinessRuleViolationException
import ro.animaliaprogramari.animalia.domain.exception.EntityNotFoundException
import ro.animaliaprogramari.animalia.domain.model.*

/**
 * Implementation of staff working hours management use case
 * Contains the business logic for staff working hours operations
 */
@Service
@Transactional
class StaffWorkingHoursManagementUseCaseImpl(
    private val staffWorkingHoursRepository: StaffWorkingHoursRepository,
    private val staffRepository: StaffRepository,
    private val appointmentRepository: AppointmentRepository
) : StaffWorkingHoursManagementUseCase {

    private val logger = LoggerFactory.getLogger(StaffWorkingHoursManagementUseCaseImpl::class.java)

    override fun getStaffWorkingHours(query: GetStaffWorkingHoursQuery): StaffWorkingHoursSettings {
        logger.debug("Getting working hours for staff: ${query.staffId.value} in salon: ${query.salonId.value}")

        // Validate staff exists
        staffRepository.findById(StaffId.of(query.staffId.value))
            ?: throw EntityNotFoundException("Staff not found: ${query.staffId.value}")

        // Check permissions
        validatePermissions(query.requesterId, query.staffId, query.salonId, "view")

        // Get working hours or create default if not exists
        val workingHours = staffWorkingHoursRepository.findByStaffIdAndSalonId(query.staffId, query.salonId)
            ?: StaffWorkingHoursSettings.createDefault(query.staffId, query.salonId)

        logger.debug("Working hours retrieved for staff: ${query.staffId.value}")
        return workingHours
    }

    override fun updateStaffWorkingHours(command: UpdateStaffWorkingHoursCommand): StaffWorkingHoursSettings {
        logger.debug("Updating working hours for staff: ${command.staffId.value} in salon: ${command.salonId.value}")

        // Validate staff exists
        staffRepository.findById(command.staffId) ?: throw EntityNotFoundException("Staff not found: ${command.staffId.value}")

        // Check permissions
        validatePermissions(command.updaterUserId, command.staffId, command.salonId, "modify")

        // Get existing working hours or create default
        val existingWorkingHours = staffWorkingHoursRepository.findByStaffIdAndSalonId(command.staffId, command.salonId)
            ?: StaffWorkingHoursSettings.createDefault(command.staffId, command.salonId)

        // Update working hours
        val updatedWorkingHours = existingWorkingHours
            .updateWeeklySchedule(command.weeklySchedule)
            .updateHolidays(command.holidays)
            .updateCustomClosures(command.customClosures)

        // Save updated working hours
        val savedWorkingHours = staffWorkingHoursRepository.save(updatedWorkingHours)
        logger.debug("Working hours updated for staff: ${command.staffId.value}")

        return savedWorkingHours
    }

    override fun resetStaffWorkingHours(staffId: String, salonId: String, requesterId: String): StaffWorkingHoursSettings {
        logger.debug("Resetting working hours for staff: $staffId in salon: $salonId")

        val staffIdObj = StaffId.of(staffId)
        val salonIdObj = SalonId.of(salonId)
        val requesterIdObj = UserId.of(requesterId)

        // ✅ FIXED: Validate staff exists using staffId instead of userId
        staffRepository.findById(staffIdObj)
            ?: throw EntityNotFoundException("Staff not found: $staffId")

        // Only CHIEF_GROOMER can reset staff schedules
        val requesterStaff = staffRepository.findByUserIdAndSalonId(requesterIdObj, salonIdObj)
        if (requesterStaff?.role != StaffRole.CHIEF_GROOMER) {
            throw BusinessRuleViolationException("Only CHIEF_GROOMER can reset staff schedules")
        }

        // Create default working hours
        val defaultWorkingHours = StaffWorkingHoursSettings.createDefault(staffIdObj, salonIdObj)

        // Save default working hours
        val savedWorkingHours = staffWorkingHoursRepository.save(defaultWorkingHours)
        logger.debug("Working hours reset for staff: $staffId")

        return savedWorkingHours
    }

    override fun getStaffAvailability(query: GetStaffAvailabilityQuery): StaffAvailabilityReport {
        logger.debug("Getting staff availability for salon: ${query.salonId.value} on date: ${query.date}")

        // Get all active staff for the salon
        val allStaff = staffRepository.findActiveBySalonWithUserDetails(query.salonId)

        val staffAvailability = allStaff.map { staff ->
            val staffId = staff.id // ✅ FIXED: Use staff.id instead of staff.userId

            // Get working hours for this staff member
            val workingHours = staffWorkingHoursRepository.findByStaffIdAndSalonId(staffId, query.salonId)
                ?: StaffWorkingHoursSettings.createDefault(staffId, query.salonId)

            // Check if staff is available on this date
            val isAvailableOnDate = workingHours.isAvailableOn(query.date)
            val daySchedule = workingHours.getWorkingHoursFor(query.date)

            if (!isAvailableOnDate) {
                StaffMemberAvailability(
                    staff = staff,
                    isAvailable = false,
                    workingHours = null,
                    conflictingAppointments = emptyList(),
                    reason = "Day off"
                )
            } else {
                // Check for conflicting appointments
                val existingAppointments = appointmentRepository.findBySalonIdAndStaffIdAndDate(
                    query.salonId, staff.id, query.date
                )

                val conflictingAppointments = if (query.startTime != null && query.endTime != null) {
                    existingAppointments.filter { appointment ->
                        // Check if appointment overlaps with requested time
                        !(appointment.endTime.isBefore(query.startTime) || appointment.startTime.isAfter(query.endTime))
                    }
                } else {
                    existingAppointments
                }

                val isAvailable = conflictingAppointments.isEmpty()
                val reason = if (!isAvailable) "Conflicting appointment" else null

                StaffMemberAvailability(
                    staff = staff,
                    isAvailable = isAvailable,
                    workingHours = daySchedule,
                    conflictingAppointments = conflictingAppointments,
                    reason = reason
                )
            }
        }

        return StaffAvailabilityReport(
            date = query.date,
            staffAvailability = staffAvailability
        )
    }

    private fun validatePermissions(requesterId: UserId, staffId: StaffId, salonId: SalonId, action: String) {
        val requesterStaff = staffRepository.findByUserIdAndSalonId(requesterId, salonId)
            ?: throw BusinessRuleViolationException("Nu aveți acces la acest salon")

        // CHIEF_GROOMER can view/modify any staff schedule
        if (requesterStaff.role == StaffRole.CHIEF_GROOMER) {
            return
        }

        // Staff member can only view/modify their own schedule
        // ✅ FIXED: Compare staffId with requesterStaff.id instead of requesterId
        if (requesterStaff.id == staffId) {
            return
        }

        throw BusinessRuleViolationException("Nu aveți permisiunea să ${if (action == "view") "accesați" else "modificați"} programul acestui angajat")
    }
}
