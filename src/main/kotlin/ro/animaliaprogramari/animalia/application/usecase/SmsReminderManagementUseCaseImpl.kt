package ro.animaliaprogramari.animalia.application.usecase

import org.slf4j.LoggerFactory
import ro.animaliaprogramari.animalia.application.command.SmsMessageCommand
import ro.animaliaprogramari.animalia.application.command.UpdateSmsReminderSettingsCommand
import ro.animaliaprogramari.animalia.application.port.inbound.SmsReminderManagementUseCase
import ro.animaliaprogramari.animalia.application.port.outbound.SalonRepository
import ro.animaliaprogramari.animalia.application.port.outbound.SmsReminderSettingsRepository
import ro.animaliaprogramari.animalia.application.port.outbound.StaffRepository
import ro.animaliaprogramari.animalia.application.query.GetSmsReminderSettingsQuery
import ro.animaliaprogramari.animalia.domain.exception.BusinessRuleViolationException
import ro.animaliaprogramari.animalia.domain.exception.EntityNotFoundException
import ro.animaliaprogramari.animalia.domain.model.SmsReminderSettings
import ro.animaliaprogramari.animalia.domain.model.StaffRole

/**
 * Implementation of SMS reminder management use cases
 */
class SmsReminderManagementUseCaseImpl(
    private val smsReminderSettingsRepository: SmsReminderSettingsRepository,
    private val salonRepository: SalonRepository,
    private val staffRepository: StaffRepository
) : SmsReminderManagementUseCase {

    private val logger = LoggerFactory.getLogger(SmsReminderManagementUseCaseImpl::class.java)

    override fun getSmsReminderSettings(query: GetSmsReminderSettingsQuery): SmsReminderSettings {
        logger.debug("Getting SMS reminder settings for salon: ${query.salonId.value}")

        // Validate salon exists
        val salon = salonRepository.findById(query.salonId)
            ?: throw EntityNotFoundException("Salon not found: ${query.salonId.value}")

        // Get existing settings or create default
        return smsReminderSettingsRepository.findBySalonId(query.salonId)
            ?: SmsReminderSettings.createDefault(query.salonId)
    }

    override fun updateSmsReminderSettings(command: UpdateSmsReminderSettingsCommand): SmsReminderSettings {
        logger.debug("Updating SMS reminder settings for salon: ${command.salonId.value}")

        // Validate salon exists
        val salon = salonRepository.findById(command.salonId)
            ?: throw EntityNotFoundException("Salon not found: ${command.salonId.value}")

        // Validate user has permission to update settings (must be chief groomer)
        val userStaff = staffRepository.findByUserIdAndSalonId(
            command.updaterUserId, command.salonId
        )
        if (userStaff == null || userStaff.role != StaffRole.CHIEF_GROOMER) {
            throw BusinessRuleViolationException("User does not have permission to update SMS settings for this salon")
        }

        // Get existing settings or create default
        val existingSettings = smsReminderSettingsRepository.findBySalonId(command.salonId)
            ?: SmsReminderSettings.createDefault(command.salonId)

        // Update settings
        val updatedSettings = existingSettings.updateSettings(
            appointmentConfirmations = command.appointmentConfirmations,
            dayBeforeReminders = command.dayBeforeReminders,
            followUpMessages = command.followUpMessages,
            selectedProvider = command.selectedProvider
        )

        return smsReminderSettingsRepository.save(updatedSettings)
    }

    override fun sendSms(command: SmsMessageCommand) {
        logger.info("Sending SMS message to: ${command.phoneNumber}")
    }
}
