package ro.animaliaprogramari.animalia.application.usecase

import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import ro.animaliaprogramari.animalia.application.command.SendSalonInvitationCommand
import ro.animaliaprogramari.animalia.application.port.inbound.InvitationManagementUseCase
import ro.animaliaprogramari.animalia.application.port.inbound.SalonStaffManagementUseCase
import ro.animaliaprogramari.animalia.application.port.outbound.SalonInvitationRepository
import ro.animaliaprogramari.animalia.application.port.outbound.StaffRepository
import ro.animaliaprogramari.animalia.application.port.outbound.UserRepository
import ro.animaliaprogramari.animalia.domain.model.*
import java.time.LocalDateTime

/**
 * Implementation of SalonStaffManagementUseCase
 * This is the application service layer in hexagonal architecture
 */
@Service
@Transactional
class SalonStaffManagementUseCaseImpl(
    private val staffRepository: StaffRepository,
    private val userRepository: UserRepository,
    private val invitationManagementUseCase: InvitationManagementUseCase,
    private val invitationRepository: SalonInvitationRepository
) : SalonStaffManagementUseCase {

    override fun getSalonStaff(
        salonId: SalonId,
        activeOnly: Boolean,
        search: String?
    ): List<SalonStaffInfo> {
        // Get all staff for the salon
        val staffMembers = if (activeOnly) {
            staffRepository.findActiveBySalonWithUserDetails(salonId)
        } else {
            staffRepository.findBySalonWithUserDetails(salonId)
        }

        // Get user details for each staff member and apply search filter
        val staffInfo = staffMembers.mapNotNull { staff ->
            val user = userRepository.findById(staff.userId)
            user?.let {
                // Apply search filter if provided
                if (search.isNullOrBlank() ||
                    user.name.contains(search, ignoreCase = true) ||
                    user.email?.value?.contains(search, ignoreCase = true) == true ||
                    user.phoneNumber?.contains(search, ignoreCase = true) == true) {
                    SalonStaffInfo.from(staff, user)
                } else null
            }
        }

        return staffInfo
    }

    override fun getSalonPendingInvitations(
        salonId: SalonId,
        search: String?
    ): List<Pair<SalonInvitation, String>> {
        // Get all pending invitations for the salon
        val invitations = invitationRepository.findPendingBySalonId(salonId)

        // Get inviter names and apply search filter
        val invitationsWithInviters = invitations.mapNotNull { invitation ->
            val inviter = userRepository.findById(invitation.inviterUserId)
            inviter?.let { user ->
                // Apply search filter if provided
                if (search.isNullOrBlank() ||
                    invitation.invitedUserPhone.contains(search, ignoreCase = true) ||
                    user.name.contains(search, ignoreCase = true)) {
                    Pair(invitation, user.name)
                } else null
            }
        }

        return invitationsWithInviters
    }

    override fun inviteStaffByPhone(
        salonId: SalonId,
        inviterUserId: UserId,
        phoneNumber: String,
        role: StaffRole,
        permissions: StaffPermissions,
        message: String?
    ): SalonInvitation {
        // Use the invitation management use case directly with Staff types
        val command = SendSalonInvitationCommand(
            salonId = salonId,
            inviterUserId = inviterUserId,
            invitedUserPhone = phoneNumber,
            proposedRole = role,
            proposedPermissions = permissions,
            message = message
        )

        return invitationManagementUseCase.sendInvitation(command)
    }

    override fun addUserToSalon(
        salonId: SalonId,
        userId: UserId,
        role: StaffRole,
        permissions: StaffPermissions
    ): Staff {
        // Verify user exists
        val user = userRepository.findById(userId)
            ?: throw IllegalArgumentException("Utilizatorul nu a fost găsit")

        // Check if staff already exists
        val existingStaff = staffRepository.findByUserIdAndSalonId(userId, salonId)
        if (existingStaff != null) {
            throw IllegalArgumentException("Utilizatorul este deja angajat în acest salon")
        }

        // Create new staff member
        val staff = if (role == StaffRole.CHIEF_GROOMER) {
            Staff.createChiefGroomer(userId, salonId)
        } else {
            Staff.createGroomer(userId, salonId, permissions)
        }

        return staffRepository.save(staff)
    }

    override fun updateStaffRole(
        salonId: SalonId,
        userId: UserId,
        role: StaffRole,
        permissions: StaffPermissions,
        nickName: String?
    ): Staff {
        val staff = staffRepository.findByUserIdAndSalonId(userId, salonId)
            ?: throw IllegalArgumentException("Angajatul nu a fost găsit în acest salon")

        val name = userRepository.findById(userId)?.name

        val finalName = if (nickName.isNullOrBlank()) {
            name
        } else {
            nickName
        }

        // Update role and permissions
        val updatedStaff = staff.updateRole(role).updatePermissions(permissions).updateNickName(finalName!!)

        return staffRepository.save(updatedStaff)
    }

    override fun toggleStaffStatus(
        salonId: SalonId,
        userId: UserId
    ): Staff {
        val staff = staffRepository.findByUserIdAndSalonId(userId, salonId)
            ?: throw IllegalArgumentException("Angajatul nu a fost găsit în acest salon")

        // Toggle status
        val updatedStaff = if (staff.isActive) {
            staff.deactivate()
        } else {
            staff.reactivate()
        }

        return staffRepository.save(updatedStaff)
    }

    override fun toggleStaffStatusByStaffId(
        salonId: SalonId,
        staffId: StaffId
    ): Staff {
        val staff = staffRepository.findById(staffId)
            ?: throw IllegalArgumentException("Angajatul nu a fost găsit")

        // Verify staff belongs to the salon
        if (staff.salonId != salonId) {
            throw IllegalArgumentException("Angajatul nu aparține acestui salon")
        }

        // Toggle status
        val updatedStaff = if (staff.isActive) {
            staff.deactivate()
        } else {
            staff.reactivate()
        }

        return staffRepository.save(updatedStaff)
    }

    override fun removeStaffFromSalon(
        salonId: SalonId,
        userId: UserId
    ): Staff {
        val staff = staffRepository.findByUserIdAndSalonId(userId, salonId)
            ?: throw IllegalArgumentException("Angajatul nu a fost găsit în acest salon")

        // Soft delete by deactivating
        val deactivatedStaff = staff.deactivate()
        return staffRepository.save(deactivatedStaff)
    }

    override fun getSalonStaffDetails(
        salonId: SalonId,
        userId: UserId
    ): SalonStaffInfo? {
        val user = userRepository.findById(userId) ?: return null

        val staff = staffRepository.findByUserIdAndSalonId(userId, salonId)
            ?: return null

        return SalonStaffInfo(
            staff = staff,
            userName = user.name,
            userEmail = user.email?.value,
            userPhone = user.phoneNumber
        )
    }

    override fun updatePendingInvitation(
        salonId: SalonId,
        invitationId: InvitationId,
        role: StaffRole,
        permissions: StaffPermissions
    ): SalonInvitation {
        // Find the invitation
        val invitation = invitationRepository.findById(invitationId)
            ?: throw IllegalArgumentException("Invitația nu a fost găsită")

        // Verify invitation belongs to the salon
        if (invitation.salonId != salonId) {
            throw IllegalArgumentException("Invitația nu aparține acestui salon")
        }

        // Verify invitation is still pending
        if (invitation.status != InvitationStatus.PENDING) {
            throw IllegalArgumentException("Doar invitațiile în așteptare pot fi modificate")
        }

        // Verify invitation is not expired
        if (invitation.isExpired()) {
            throw IllegalArgumentException("Invitația a expirat și nu poate fi modificată")
        }

        // Validate permissions for role
        val validatedPermissions = if (role == StaffRole.CHIEF_GROOMER) {
            StaffPermissions.fullAccess()
        } else {
            permissions
        }

        // Create updated invitation
        val updatedInvitation = invitation.copy(
            proposedRole = role,
            proposedPermissions = validatedPermissions,
            updatedAt = LocalDateTime.now()
        )

        return invitationRepository.save(updatedInvitation)
    }

    override fun cancelPendingInvitation(
        salonId: SalonId,
        invitationId: InvitationId,
        cancellingUserId: UserId
    ): Boolean {
        // Find the invitation
        val invitation = invitationRepository.findById(invitationId)
            ?: throw IllegalArgumentException("Invitația nu a fost găsită")

        // Verify invitation belongs to the salon
        if (invitation.salonId != salonId) {
            throw IllegalArgumentException("Invitația nu aparține acestui salon")
        }

        // Verify invitation is still pending
        if (invitation.status != InvitationStatus.PENDING) {
            throw IllegalArgumentException("Doar invitațiile în așteptare pot fi anulate")
        }

        // Cancel the invitation with audit trail
        val cancelledInvitation = invitation.cancel(cancellingUserId)
        invitationRepository.save(cancelledInvitation)

        return true
    }
}
