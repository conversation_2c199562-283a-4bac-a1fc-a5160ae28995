package ro.animaliaprogramari.animalia.application.usecase

import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import ro.animaliaprogramari.animalia.application.command.ScheduleAppointmentCommand
import ro.animaliaprogramari.animalia.application.port.outbound.*
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.domain.service.SchedulingConflictService
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZoneId
import kotlin.math.abs

/**
 * SQL-based appointment alternative suggestion service
 * Replaces the complex coroutine-based approach with efficient database queries
 * and proper business logic for finding realistic appointment alternatives.
 */
@Service
class AppointmentAlternativeSuggestionService(
    private val workingHoursRepository: WorkingHoursRepository,
    private val staffWorkingHoursRepository: StaffWorkingHoursRepository,
    private val appointmentRepository: AppointmentRepository,
    private val blockTimeRepository: BlockTimeRepository,
    private val staffRepository: StaffRepository,
    private val schedulingConflictService: SchedulingConflictService
) {
    private val logger = LoggerFactory.getLogger(AppointmentAlternativeSuggestionService::class.java)

    /**
     * Generate alternative suggestions using efficient SQL-based approach
     * Finds realistic alternatives with proper business logic validation
     */
    fun suggestAlternatives(
        command: ScheduleAppointmentCommand,
        conflicts: List<AppointmentConflictItem>
    ): List<AlternativeSuggestion> {
        logger.info("Generating alternatives for appointment: salon=${command.salonId}, staff=${command.staffId}, date=${command.appointmentDate}, time=${command.startTime}-${command.endTime}")

        val alternatives = mutableListOf<AlternativeSuggestion>()

        // Method 1: Find alternative staff at the same time slot (highest priority)
        val alternativeStaff = findAlternativeStaff(
            originalDate = command.appointmentDate,
            originalStartTime = command.startTime,
            originalEndTime = command.endTime,
            salonId = command.salonId,
            excludeStaffId = command.staffId
        )

        alternativeStaff.forEach { staff ->
            alternatives.add(
                AlternativeSuggestion(
                    type = AlternativeType.STAFF_ALTERNATIVE,
                    priority = 1,
                    suggestion = AlternativeSlot(
                        date = command.appointmentDate,
                        startTime = command.startTime,
                        endTime = command.endTime,
                        staffId = staff.id.value,
                        staffName = staff.nickname ?: "Unknown Staff"
                    ),
                    reason = "Personal alternativ disponibil: ${staff.nickname}",
                    confidence = 0.95
                )
            )
        }

        // Method 2: Find alternative time slots for the same staff
        val alternativeSlots = findAlternativeSlots(
            staffId = command.staffId,
            originalDate = command.appointmentDate,
            originalStartTime = command.startTime,
            originalEndTime = command.endTime,
            salonId = command.salonId,
            limit = 10
        )

        alternatives.addAll(alternativeSlots)

        // Sort by priority and confidence, take top 5
        val sortedAlternatives = alternatives
            .sortedWith(compareBy<AlternativeSuggestion> { it.priority }.thenByDescending { it.confidence })
            .take(5)

        logger.info("Generated ${sortedAlternatives.size} valid alternatives")
        return sortedAlternatives
    }

    /**
     * Method 1: Find alternative time slots for the SAME staff member
     * Search criteria in priority order:
     * 1. Same day, earlier/later time slots (within business hours)
     * 2. Next/previous days with same time slot
     * 3. Next/previous days with any available time slot
     */
    fun findAlternativeSlots(
        staffId: StaffId,
        originalDate: LocalDate,
        originalStartTime: LocalTime,
        originalEndTime: LocalTime,
        salonId: SalonId,
        limit: Int = 5
    ): List<AlternativeSuggestion> {
        logger.debug("Finding alternative slots for staff ${staffId.value} on $originalDate $originalStartTime-$originalEndTime")

        val staff = staffRepository.findById(staffId)
        val staffName = staff?.nickname ?: "Unknown Staff"
        val alternatives = mutableListOf<AlternativeSuggestion>()

        // Load validation context
        val salonHours = workingHoursRepository.findBySalonId(salonId)
        val staffHours = staffWorkingHoursRepository.findByStaffIdAndSalonId(staffId, salonId)
            ?: StaffWorkingHoursSettings.createDefault(staffId, salonId)

        // 1. Same day alternatives (earlier/later times)
        val sameDayAlternatives = findSameDayAlternatives(
            staffId, originalDate, originalStartTime, originalEndTime,
            salonId, staffName, salonHours, staffHours
        )
        alternatives.addAll(sameDayAlternatives)

        // 2. Different day alternatives with same time
        if (alternatives.size < limit) {
            val differentDayAlternatives = findDifferentDayAlternatives(
                staffId, originalDate, originalStartTime, originalEndTime,
                salonId, staffName, salonHours, staffHours, limit - alternatives.size
            )
            alternatives.addAll(differentDayAlternatives)
        }

        // 3. Different day alternatives with any available time
        if (alternatives.size < limit) {
            val flexibleAlternatives = findFlexibleTimeAlternatives(
                staffId, originalDate, originalStartTime, originalEndTime,
                salonId, staffName, salonHours, staffHours, limit - alternatives.size
            )
            alternatives.addAll(flexibleAlternatives)
        }

        return alternatives.take(limit)
    }

    /**
     * Method 2: Find OTHER staff members available at the EXACT same time slot
     * Returns list of available staff with their details
     * If no staff available at exact time, returns empty list
     */
    fun findAlternativeStaff(
        originalDate: LocalDate,
        originalStartTime: LocalTime,
        originalEndTime: LocalTime,
        salonId: SalonId,
        excludeStaffId: StaffId
    ): List<Staff> {
        logger.debug("Finding alternative staff for $originalDate $originalStartTime-$originalEndTime, excluding ${excludeStaffId.value}")

        // Get all active staff for the salon (excluding the original staff)
        val allStaff = staffRepository.findBySalonId(salonId)
            .filter { it.id != excludeStaffId && it.isActive }

        val availableStaff = mutableListOf<Staff>()

        // Load salon working hours
        val salonHours = workingHoursRepository.findBySalonId(salonId)

        // Check each staff member's availability
        for (staff in allStaff) {
            val staffHours = staffWorkingHoursRepository.findByStaffIdAndSalonId(staff.id, salonId)
                ?: StaffWorkingHoursSettings.createDefault(staff.id, salonId)

            // Get existing appointments and blocks for this staff on the date
            val existingAppointments = appointmentRepository.findBySalonIdAndStaffIdAndDate(
                salonId, staff.id, originalDate
            )

            val blocks = blockTimeRepository.findOverlappingBlocks(
                salonId,
                originalDate.atTime(originalStartTime).atZone(ZoneId.systemDefault()),
                originalDate.atTime(originalEndTime).atZone(ZoneId.systemDefault()),
                setOf(staff.id)
            )

            // Check if this staff member is available
            val isAvailable = schedulingConflictService.isSlotAvailable(
                originalDate,
                originalStartTime,
                originalEndTime,
                staff.id,
                salonHours,
                staffHours,
                existingAppointments,
                blocks
            )

            if (isAvailable) {
                availableStaff.add(staff)
                logger.debug("Staff ${staff.nickname} is available at the requested time")
            }
        }

        logger.debug("Found ${availableStaff.size} alternative staff members")
        return availableStaff
    }

    /**
     * Find same day alternatives (earlier/later times within business hours)
     */
    private fun findSameDayAlternatives(
        staffId: StaffId,
        date: LocalDate,
        originalStartTime: LocalTime,
        originalEndTime: LocalTime,
        salonId: SalonId,
        staffName: String,
        salonHours: WorkingHoursSettings,
        staffHours: StaffWorkingHoursSettings
    ): List<AlternativeSuggestion> {
        val alternatives = mutableListOf<AlternativeSuggestion>()
        val duration = java.time.Duration.between(originalStartTime, originalEndTime)

        // Get existing appointments and blocks for this date
        val existingAppointments = appointmentRepository.findBySalonIdAndStaffIdAndDate(salonId, staffId, date)
        val blocks = blockTimeRepository.findOverlappingBlocks(
            salonId,
            date.atTime(LocalTime.MIN).atZone(ZoneId.systemDefault()),
            date.atTime(LocalTime.MAX).atZone(ZoneId.systemDefault()),
            setOf(staffId)
        )

        // Generate time slots in 30-minute intervals
        val timeSlots = generateTimeSlots(date, salonHours, staffHours, duration)

        timeSlots.forEach { (startTime, endTime) ->
            // Skip the original time slot
            if (startTime == originalStartTime && endTime == originalEndTime) return@forEach

            val isAvailable = schedulingConflictService.isSlotAvailable(
                date, startTime, endTime, staffId, salonHours, staffHours, existingAppointments, blocks
            )

            if (isAvailable) {
                val timeDiffMinutes = abs(java.time.Duration.between(originalStartTime, startTime).toMinutes())
                val confidence = when {
                    timeDiffMinutes <= 60 -> 0.9
                    timeDiffMinutes <= 120 -> 0.8
                    timeDiffMinutes <= 180 -> 0.7
                    else -> 0.6
                }

                val reason = when {
                    startTime.isBefore(originalStartTime) -> {
                        val hours = timeDiffMinutes / 60
                        val minutes = timeDiffMinutes % 60
                        when {
                            hours > 0 && minutes > 0 -> "Același personal, cu ${hours}h ${minutes}min mai devreme"
                            hours > 0 -> "Același personal, cu ${hours}h mai devreme"
                            else -> "Același personal, cu ${minutes}min mai devreme"
                        }
                    }
                    else -> {
                        val hours = timeDiffMinutes / 60
                        val minutes = timeDiffMinutes % 60
                        when {
                            hours > 0 && minutes > 0 -> "Același personal, cu ${hours}h ${minutes}min mai târziu"
                            hours > 0 -> "Același personal, cu ${hours}h mai târziu"
                            else -> "Același personal, cu ${minutes}min mai târziu"
                        }
                    }
                }

                alternatives.add(
                    AlternativeSuggestion(
                        type = AlternativeType.TIME_ADJUSTMENT,
                        priority = 2,
                        suggestion = AlternativeSlot(
                            date = date,
                            startTime = startTime,
                            endTime = endTime,
                            staffId = staffId.value,
                            staffName = staffName
                        ),
                        reason = reason,
                        confidence = confidence
                    )
                )
            }
        }

        return alternatives.sortedByDescending { it.confidence }
    }

    /**
     * Find different day alternatives with same time slot
     */
    private fun findDifferentDayAlternatives(
        staffId: StaffId,
        originalDate: LocalDate,
        originalStartTime: LocalTime,
        originalEndTime: LocalTime,
        salonId: SalonId,
        staffName: String,
        salonHours: WorkingHoursSettings,
        staffHours: StaffWorkingHoursSettings,
        limit: Int
    ): List<AlternativeSuggestion> {
        val alternatives = mutableListOf<AlternativeSuggestion>()

        // Check next 7 days and previous 3 days
        val datesToCheck = mutableListOf<LocalDate>()

        // Add future dates (higher priority)
        for (i in 1..7) {
            datesToCheck.add(originalDate.plusDays(i.toLong()))
        }

        // Add past dates (lower priority)
        for (i in 1..3) {
            datesToCheck.add(originalDate.minusDays(i.toLong()))
        }

        for (date in datesToCheck) {
            if (alternatives.size >= limit) break

            // Get existing appointments and blocks for this date
            val existingAppointments = appointmentRepository.findBySalonIdAndStaffIdAndDate(salonId, staffId, date)
            val blocks = blockTimeRepository.findOverlappingBlocks(
                salonId,
                date.atTime(originalStartTime).atZone(ZoneId.systemDefault()),
                date.atTime(originalEndTime).atZone(ZoneId.systemDefault()),
                setOf(staffId)
            )

            val isAvailable = schedulingConflictService.isSlotAvailable(
                date, originalStartTime, originalEndTime, staffId, salonHours, staffHours, existingAppointments, blocks
            )

            if (isAvailable) {
                val daysDiff = abs(java.time.temporal.ChronoUnit.DAYS.between(originalDate, date))
                val confidence = when {
                    daysDiff <= 1 -> 0.85
                    daysDiff <= 3 -> 0.75
                    daysDiff <= 7 -> 0.65
                    else -> 0.55
                }

                val reason = if (date.isAfter(originalDate)) {
                    "Același personal, ${daysDiff} zile mai târziu"
                } else {
                    "Același personal, ${daysDiff} zile mai devreme"
                }

                alternatives.add(
                    AlternativeSuggestion(
                        type = AlternativeType.DAY_ADJUSTMENT,
                        priority = 3,
                        suggestion = AlternativeSlot(
                            date = date,
                            startTime = originalStartTime,
                            endTime = originalEndTime,
                            staffId = staffId.value,
                            staffName = staffName
                        ),
                        reason = reason,
                        confidence = confidence
                    )
                )
            }
        }

        return alternatives.sortedByDescending { it.confidence }
    }

    /**
     * Find flexible time alternatives on different days
     */
    private fun findFlexibleTimeAlternatives(
        staffId: StaffId,
        originalDate: LocalDate,
        originalStartTime: LocalTime,
        originalEndTime: LocalTime,
        salonId: SalonId,
        staffName: String,
        salonHours: WorkingHoursSettings,
        staffHours: StaffWorkingHoursSettings,
        limit: Int
    ): List<AlternativeSuggestion> {
        val alternatives = mutableListOf<AlternativeSuggestion>()
        val duration = java.time.Duration.between(originalStartTime, originalEndTime)

        // Check next 5 days for any available slots
        for (i in 1..5) {
            if (alternatives.size >= limit) break

            val date = originalDate.plusDays(i.toLong())
            val timeSlots = generateTimeSlots(date, salonHours, staffHours, duration)

            // Get existing appointments and blocks for this date
            val existingAppointments = appointmentRepository.findBySalonIdAndStaffIdAndDate(salonId, staffId, date)
            val blocks = blockTimeRepository.findOverlappingBlocks(
                salonId,
                date.atTime(LocalTime.MIN).atZone(ZoneId.systemDefault()),
                date.atTime(LocalTime.MAX).atZone(ZoneId.systemDefault()),
                setOf(staffId)
            )

            // Find first available slot on this day
            for ((startTime, endTime) in timeSlots) {
                if (alternatives.size >= limit) break

                val isAvailable = schedulingConflictService.isSlotAvailable(
                    date, startTime, endTime, staffId, salonHours, staffHours, existingAppointments, blocks
                )

                if (isAvailable) {
                    val confidence = when (i) {
                        1 -> 0.7
                        2 -> 0.6
                        3 -> 0.5
                        else -> 0.4
                    }

                    alternatives.add(
                        AlternativeSuggestion(
                            type = AlternativeType.DAY_ADJUSTMENT,
                            priority = 4,
                            suggestion = AlternativeSlot(
                                date = date,
                                startTime = startTime,
                                endTime = endTime,
                                staffId = staffId.value,
                                staffName = staffName
                            ),
                            reason = "Același personal, ${i} zile mai târziu la ${startTime}",
                            confidence = confidence
                        )
                    )
                    break // Only take first available slot per day
                }
            }
        }

        return alternatives
    }

    /**
     * Generate possible time slots within business hours
     */
    private fun generateTimeSlots(
        date: LocalDate,
        salonHours: WorkingHoursSettings,
        staffHours: StaffWorkingHoursSettings,
        duration: java.time.Duration
    ): List<Pair<LocalTime, LocalTime>> {
        val slots = mutableListOf<Pair<LocalTime, LocalTime>>()

        // Determine working hours for the day
        val salonDay = salonHours.getWorkingHoursFor(date)
        val staffDay = staffHours.getWorkingHoursFor(date)

        // Use the most restrictive hours
        val startHour = maxOf(
            salonDay?.startTime ?: LocalTime.of(8, 0),
            staffDay?.startTime ?: LocalTime.of(8, 0)
        )
        val endHour = minOf(
            salonDay?.endTime ?: LocalTime.of(18, 0),
            staffDay?.endTime ?: LocalTime.of(18, 0)
        )

        // Generate 30-minute intervals
        var currentTime = startHour
        while (currentTime.plus(duration) <= endHour) {
            val endTime = currentTime.plus(duration)
            slots.add(Pair(currentTime, endTime))
            currentTime = currentTime.plusMinutes(30)
        }

        return slots
    }
}
