package ro.animaliaprogramari.animalia.application.port.inbound

import ro.animaliaprogramari.animalia.domain.model.*

/**
 * Use case interface for salon service management operations
 * This is a port in hexagonal architecture
 */
interface SalonServiceManagementUseCase {

    /**
     * Create a new service for a salon
     */
    fun createService(
        salonId: SalonId,
        name: String,
        description: String?,
        price: Money,
        duration: Duration,
        category: ServiceCategory,
        displayOrder: Int = 0,
        requirements: List<String> = emptyList(),
        sizePrices: Map<String, Money> = emptyMap(),
        sizeDurations: Map<String, Duration> = emptyMap(),
        minPrice: Money? = null,
        maxPrice: Money? = null,
        sizeMinPrices: Map<String, Money>? = null,
        sizeMaxPrices: Map<String, Money>? = null
    ): SalonService

    /**
     * Update an existing service
     */
    fun updateService(
        serviceId: ServiceId,
        salonId: SalonId,
        name: String? = null,
        description: String? = null,
        price: Money? = null,
        duration: Duration? = null,
        category: ServiceCategory? = null,
        displayOrder: Int? = null,
        requirements: List<String>? = null,
        sizePrices: Map<String, Money>? = null,
        sizeDurations: Map<String, Duration>? = null,
        minPrice: Money? = null,
        maxPrice: Money? = null,
        sizeMinPrices: Map<String, Money>? = null,
        sizeMaxPrices: Map<String, Money>? = null
    ): SalonService

    /**
     * Get service by ID (with salon verification)
     */
    fun getServiceById(serviceId: ServiceId, salonId: SalonId): SalonService?

    /**
     * Get all services for a salon
     */
    fun getServicesBySalon(
        salonId: SalonId,
        activeOnly: Boolean = true,
        search: String? = null,
        category: ServiceCategory? = null
    ): List<SalonService>

    /**
     * Deactivate a service (soft delete)
     */
    fun deactivateService(serviceId: ServiceId, salonId: SalonId): SalonService

    /**
     * Activate a service
     */
    fun activateService(serviceId: ServiceId, salonId: SalonId): SalonService

    /**
     * Delete a service permanently (only if not used in appointments)
     */
    fun deleteService(serviceId: ServiceId, salonId: SalonId)

    /**
     * Check if service name is unique within salon
     */
    fun isServiceNameUnique(salonId: SalonId, name: String, excludeServiceId: ServiceId? = null): Boolean

    /**
     * Toggle service status (active/inactive)
     */
    fun toggleServiceStatus(serviceId: ServiceId, salonId: SalonId): SalonService

    /**
     * Duplicate an existing service
     */
    fun duplicateService(serviceId: ServiceId, salonId: SalonId): SalonService
}
