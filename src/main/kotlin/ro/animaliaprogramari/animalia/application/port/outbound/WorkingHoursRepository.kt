package ro.animaliaprogramari.animalia.application.port.outbound

import ro.animaliaprogramari.animalia.domain.model.*

/**
 * Repository port for working hours persistence operations
 * This is an outbound port that defines the persistence contract for working hours
 */
interface WorkingHoursRepository {

    /**
     * Find working hours settings by salon ID
     */
    fun findBySalonId(salonId: SalonId): WorkingHoursSettings

    /**
     * Save working hours settings
     */
    fun save(workingHours: WorkingHoursSettings): WorkingHoursSettings

    /**
     * Delete working hours settings by salon ID
     */
    fun deleteBySalonId(salonId: SalonId)

    /**
     * Check if working hours settings exist for salon
     */
    fun existsBySalonId(salonId: SalonId): Bo<PERSON>an
}
