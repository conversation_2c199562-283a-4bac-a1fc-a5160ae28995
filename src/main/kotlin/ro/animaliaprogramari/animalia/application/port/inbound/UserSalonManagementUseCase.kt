package ro.animaliaprogramari.animalia.application.port.inbound

import ro.animaliaprogramari.animalia.application.command.SwitchCurrentSalonCommand
import ro.animaliaprogramari.animalia.application.query.GetUserStaffAssociationsQuery
import ro.animaliaprogramari.animalia.domain.model.Staff
import ro.animaliaprogramari.animalia.domain.model.User

/**
 * Inbound port for user salon management operations
 * Defines the contract for managing user-salon relationships through staff
 */
interface UserSalonManagementUseCase {

    /**
     * Get user's staff associations (salons where user works)
     */
    fun getUserStaffAssociations(query: GetUserStaffAssociationsQuery): List<Staff>

    /**
     * Switch user's current salon
     */
    fun switchCurrentSalon(command: SwitchCurrentSalonCommand): User
}
