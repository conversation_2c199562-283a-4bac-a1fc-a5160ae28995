package ro.animaliaprogramari.animalia.application.port.inbound

import ro.animaliaprogramari.animalia.application.command.*
import ro.animaliaprogramari.animalia.application.query.*
import ro.animaliaprogramari.animalia.domain.model.Client

/**
 * Inbound port for client management use cases
 */
interface ClientManagementUseCase {

    /**
     * Register a new client
     */
    fun registerClient(command: RegisterClientCommand): Client

    /**
     * Update client information
     */
    fun updateClient(command: UpdateClientCommand): Client

    /**
     * Deactivate a client
     */
    fun deactivateClient(command: DeactivateClientCommand): Client

    /**
     * Activate a client
     */
    fun activateClient(command: ActivateClientCommand): Client

    /**
     * Delete a client (soft delete with validation)
     */
    fun deleteClient(command: DeleteClientCommand): Client

    /**
     * Get client by ID
     */
    fun getClientById(query: GetClientByIdQuery): Client?

    /**
     * Get client by email
     */
    fun getClientByEmail(query: GetClientByEmailQuery): Client?

    /**
     * Search clients
     */
    fun searchClients(query: SearchClientsQuery): List<Client>

    /**
     * Get all clients with pagination
     */
    fun getAllClients(query: GetAllClientsQuery): List<Client>

    /**
     * Get clients by groomer
     */
    fun getClientsByGroomer(query: GetClientsByGroomerQuery): List<Client>
}
