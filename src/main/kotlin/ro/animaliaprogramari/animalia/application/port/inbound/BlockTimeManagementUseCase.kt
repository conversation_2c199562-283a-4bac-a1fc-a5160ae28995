package ro.animaliaprogramari.animalia.application.port.inbound

import ro.animaliaprogramari.animalia.application.command.*
import ro.animaliaprogramari.animalia.application.query.*
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.domain.service.TimeSlotSuggestion

/**
 * Inbound port for block time management operations
 * Defines the contract for block time business operations
 */
interface BlockTimeManagementUseCase {

    /**
     * Create a new block time
     */
    fun createBlockTime(command: CreateBlockTimeCommand): BlockTimeResult

    /**
     * Get block time by ID
     */
    fun getBlockTimeById(query: GetBlockTimeByIdQuery): BlockTime?

    /**
     * Get all block times for a salon with filtering
     */
    fun getBlockTimeList(query: GetBlockTimeListQuery): BlockTimeListResult

    /**
     * Get detailed block time information
     */
    fun getBlockTimeDetails(query: GetBlockTimeDetailsQuery): BlockTimeDetails?

    /**
     * Update an existing block time
     */
    fun updateBlockTime(command: UpdateBlockTimeCommand): BlockTimeUpdateResult

    /**
     * Delete (cancel) a block time
     */
    fun deleteBlockTime(command: DeleteBlockTimeCommand): BlockTimeDeletionResult

    /**
     * Perform bulk operations on block times
     */
    fun bulkBlockTimeOperations(command: BulkBlockTimeOperationsCommand): BulkOperationResult

    /**
     * Check time availability for blocking
     */
    fun checkTimeAvailability(query: CheckTimeAvailabilityQuery): AvailabilityCheckResult

    /**
     * Get block time statistics
     */
    fun getBlockTimeStatistics(query: GetBlockTimeStatisticsQuery): BlockTimeStatistics
}

/**
 * Result of block time creation
 */
data class BlockTimeResult(
    val blockTime: BlockTime,
    val affectedAppointments: List<AffectedAppointment> = emptyList(),
    val message: String
)

/**
 * Result of block time list query
 */
data class BlockTimeListResult(
    val blocks: List<BlockTimeWithStaffNames>,
    val pagination: PaginationInfo,
    val summary: BlockTimeSummary
)

/**
 * Block time with staff names populated
 */
data class BlockTimeWithStaffNames(
    val blockTime: BlockTime,
    val staffNames: List<String>,
    val createdByName: String
)

/**
 * Detailed block time information
 */
data class BlockTimeDetails(
    val blockTime: BlockTime,
    val staffDetails: List<StaffInfo>,
    val createdByName: String,
    val affectedAppointments: List<AffectedAppointment>,
    val history: List<BlockTimeHistoryEntry>
)

/**
 * Staff information for block time details
 */
data class StaffInfo(
    val staffId: String,
    val name: String,
    val nickname: String?,
    val role: String
)

/**
 * Block time history entry
 */
data class BlockTimeHistoryEntry(
    val action: String,
    val performedBy: String,
    val performedByName: String,
    val timestamp: java.time.LocalDateTime,
    val details: String
)

/**
 * Result of block time update
 */
data class BlockTimeUpdateResult(
    val blockTime: BlockTime,
    val changes: List<FieldChange>,
    val message: String
)

/**
 * Field change information
 */
data class FieldChange(
    val field: String,
    val oldValue: String,
    val newValue: String
)

/**
 * Result of block time deletion
 */
data class BlockTimeDeletionResult(
    val blockId: String,
    val status: String,
    val cancelledBy: String,
    val cancelledAt: java.time.LocalDateTime,
    val cancellationReason: String?,
    val affectedStaff: List<AffectedStaffMember>,
    val message: String
)

/**
 * Affected staff member information
 */
data class AffectedStaffMember(
    val staffId: String,
    val staffName: String,
    val notified: Boolean
)

/**
 * Result of bulk operations
 */
data class BulkOperationResult(
    val operation: String,
    val totalRequested: Int,
    val successful: Int,
    val failed: Int,
    val results: List<BulkOperationItemResult>,
    val message: String
)

/**
 * Individual bulk operation result
 */
data class BulkOperationItemResult(
    val index: Int,
    val success: Boolean,
    val blockId: String?,
    val message: String,
    val error: String? = null
)

/**
 * Result of availability check
 */
data class AvailabilityCheckResult(
    val available: Boolean,
    val conflicts: List<BlockTimeConflict>,
    val availableStaff: List<AvailableStaffInfo>,
    val suggestions: List<TimeSlotSuggestion>
)

/**
 * Available staff information
 */
data class AvailableStaffInfo(
    val staffId: String,
    val staffName: String,
    val available: Boolean
)

/**
 * Block time statistics
 */
data class BlockTimeStatistics(
    val period: StatisticsPeriod,
    val summary: StatisticsSummary,
    val byReason: List<ReasonStatistics>,
    val byStaff: List<StaffStatistics>,
    val trends: StatisticsTrends
)

/**
 * Statistics period information
 */
data class StatisticsPeriod(
    val startDate: java.time.LocalDate,
    val endDate: java.time.LocalDate,
    val totalDays: Int
)

/**
 * Summary statistics
 */
data class StatisticsSummary(
    val totalBlocks: Int,
    val totalHoursBlocked: Double,
    val averageBlockDuration: Int,
    val mostActiveDay: java.time.LocalDate?,
    val leastActiveDay: java.time.LocalDate?
)

/**
 * Statistics by reason
 */
data class ReasonStatistics(
    val reason: String,
    val count: Int,
    val totalHours: Double,
    val percentage: Double
)

/**
 * Statistics by staff
 */
data class StaffStatistics(
    val staffId: String,
    val staffName: String,
    val totalBlocks: Int,
    val totalHours: Double,
    val averageDuration: Int,
    val mostCommonReason: String
)

/**
 * Trends information
 */
data class StatisticsTrends(
    val weeklyPattern: List<WeeklyPatternEntry>,
    val hourlyPattern: List<HourlyPatternEntry>
)

/**
 * Weekly pattern entry
 */
data class WeeklyPatternEntry(
    val day: String,
    val averageBlocks: Double,
    val averageHours: Double
)

/**
 * Hourly pattern entry
 */
data class HourlyPatternEntry(
    val hour: Int,
    val averageBlocks: Double
)

/**
 * Pagination information
 */
data class PaginationInfo(
    val currentPage: Int,
    val totalPages: Int,
    val totalItems: Int,
    val itemsPerPage: Int,
    val hasNextPage: Boolean,
    val hasPreviousPage: Boolean
)

/**
 * Block time summary
 */
data class BlockTimeSummary(
    val totalActiveBlocks: Int,
    val totalHoursBlocked: Double,
    val mostCommonReason: String,
    val staffWithMostBlocks: StaffBlockSummary?
)

/**
 * Staff block summary
 */
data class StaffBlockSummary(
    val staffId: String,
    val staffName: String,
    val blockCount: Int
)

/**
 * Affected appointment information
 */
data class AffectedAppointment(
    val appointmentId: String,
    val clientName: String,
    val conflictType: String,
    val suggestedAction: String
)
