package ro.animaliaprogramari.animalia.application.port.outbound

import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import ro.animaliaprogramari.animalia.domain.model.*
import java.time.LocalDate

/**
 * Repository interface for Staff entity with optimized query methods
 * Eliminates N+1 queries and provides efficient data access
 */
interface StaffRepository {

    /**
     * Save staff member
     */
    fun save(staff: Staff): Staff

    /**
     * Find staff by ID
     */
    fun findById(id: StaffId): Staff?

    /**
     * Find all staff by user ID (for users who work in multiple salons)
     */
    fun findByUserId(userId: UserId): List<Staff>

    /**
     * Find active staff by user ID
     */
    fun findActiveByUserId(userId: UserId): List<Staff>

    /**
     * Find staff by user ID and salon ID
     */
    fun findByUserIdAndSalonId(userId: UserId, salonId: SalonId): Staff?

    /**
     * Find all staff members in a salon
     */
    fun findBySalonId(salonId: SalonId): List<Staff>

    /**
     * Find all staff members in a salon with user details (optimized)
     * Uses JOIN FETCH to eliminate N+1 queries
     */
    fun findBySalonWithUserDetails(salonId: SalonId): List<Staff>

    /**
     * Find all active staff members in a salon with user details (optimized)
     * Uses JOIN FETCH to eliminate N+1 queries
     */
    fun findActiveBySalonWithUserDetails(salonId: SalonId): List<Staff>

    /**
     * Find staff by salon and role with pagination
     */
    fun findBySalonAndRole(
        salonId: SalonId, 
        role: StaffRole, 
        pageable: Pageable
    ): Page<Staff>

    /**
     * Find staff available for a specific time slot
     * Optimized query that checks working hours and existing appointments
     */
    fun findAvailableStaff(
        salonId: SalonId,
        date: LocalDate,
        timeSlot: TimeSlot,
        excludeStaffIds: List<StaffId> = emptyList()
    ): List<Staff>

    /**
     * Find staff with specific specializations
     */
    fun findBySpecializations(
        salonId: SalonId,
        specializations: Set<Specialization>
    ): List<Staff>

    /**
     * Find staff who can handle a specific service category
     */
    fun findByServiceCapability(
        salonId: SalonId,
        serviceCategory: ServiceCategory
    ): List<Staff>

    /**
     * Get staff workload metrics for a period
     * Optimized query that calculates metrics in database
     */
    fun getWorkloadMetrics(
        staffIds: List<StaffId>,
        startDate: LocalDate,
        endDate: LocalDate
    ): Map<StaffId, WorkloadMetrics>

    /**
     * Find staff with performance above threshold
     */
    fun findHighPerformingStaff(
        salonId: SalonId,
        minimumRating: Double,
        minimumAppointments: Int
    ): List<Staff>

    /**
     * Count active staff by salon
     */
    fun countActiveBySalon(salonId: SalonId): Long

    /**
     * Find staff hired within date range
     */
    fun findByHireDateBetween(
        salonId: SalonId,
        startDate: LocalDate,
        endDate: LocalDate
    ): List<Staff>

    /**
     * Delete staff member (soft delete)
     */
    fun delete(staff: Staff)

    /**
     * Find all staff members with their appointment counts (optimized)
     * Single query with aggregation to avoid N+1
     */
    fun findStaffWithAppointmentCounts(
        salonId: SalonId,
        startDate: LocalDate,
        endDate: LocalDate
    ): List<StaffWithAppointmentCount>

    /**
     * Find staff members who need schedule updates
     */
    fun findStaffNeedingScheduleUpdate(salonId: SalonId): List<Staff>

    /**
     * Batch update staff performance metrics
     * Optimized for bulk operations
     */
    fun updatePerformanceMetrics(updates: List<StaffPerformanceUpdate>)
}

/**
 * Data class for staff with appointment count (eliminates N+1 queries)
 */
data class StaffWithAppointmentCount(
    val staff: Staff,
    val appointmentCount: Long,
    val totalRevenue: Double,
    val averageRating: Double
)

/**
 * Data class for batch performance updates
 */
data class StaffPerformanceUpdate(
    val staffId: StaffId,
    val performance: StaffPerformance
)
