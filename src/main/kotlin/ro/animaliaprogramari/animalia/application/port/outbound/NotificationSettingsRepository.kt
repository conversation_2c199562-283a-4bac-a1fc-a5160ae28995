package ro.animaliaprogramari.animalia.application.port.outbound

import ro.animaliaprogramari.animalia.domain.model.NotificationSettings
import ro.animaliaprogramari.animalia.domain.model.SalonId

/**
 * Outbound port for notification settings persistence
 * This interface defines the contract for notification settings data access
 */
interface NotificationSettingsRepository {

    /**
     * Save notification settings
     */
    fun save(settings: NotificationSettings): NotificationSettings

    /**
     * Find notification settings by salon ID
     */
    fun findBySalonId(salonId: SalonId): NotificationSettings?

    /**
     * Check if notification settings exist for a salon
     */
    fun existsBySalonId(salonId: SalonId): Boolean

    /**
     * Delete notification settings by salon ID
     */
    fun deleteBySalonId(salonId: SalonId)
}
