package ro.animaliaprogramari.animalia.application.port.inbound

import ro.animaliaprogramari.animalia.application.command.*
import ro.animaliaprogramari.animalia.application.query.*
import ro.animaliaprogramari.animalia.domain.model.Pet

/**
 * Inbound port for pet management use cases
 */
interface PetManagementUseCase {
    
    /**
     * Add a new pet
     */
    fun addPet(command: AddPetCommand): Pet
    
    /**
     * Update pet information
     */
    fun updatePet(command: UpdatePetCommand): Pet
    
    /**
     * Deactivate a pet
     */
    fun deactivatePet(command: DeactivatePetCommand): Pet
    
    /**
     * Activate a pet
     */
    fun activatePet(command: ActivatePetCommand): Pet
    
    /**
     * Get pet by ID
     */
    fun getPetById(query: GetPetByIdQuery): Pet?
    
    /**
     * Get pets by client
     */
    fun getPetsByClient(query: GetPetsByClientQuery): List<Pet>
    
    /**
     * Search pets
     */
    fun searchPets(query: SearchPetsQuery): List<Pet>
    
    /**
     * Get all pets with pagination
     */
    fun getAllPets(query: GetAllPetsQuery): List<Pet>
}
