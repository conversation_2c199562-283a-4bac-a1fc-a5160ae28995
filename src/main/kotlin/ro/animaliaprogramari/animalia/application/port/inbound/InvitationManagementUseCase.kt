package ro.animaliaprogramari.animalia.application.port.inbound

import ro.animaliaprogramari.animalia.application.command.*
import ro.animaliaprogramari.animalia.application.query.*
import ro.animaliaprogramari.animalia.domain.model.SalonInvitation
import ro.animaliaprogramari.animalia.domain.model.Staff

/**
 * Inbound port for invitation management operations
 * This interface defines the business operations available for invitation management
 */
interface InvitationManagementUseCase {

    /**
     * Send a salon invitation
     */
    fun sendInvitation(command: SendSalonInvitationCommand): SalonInvitation

    /**
     * Accept a salon invitation
     */
    fun acceptInvitation(command: AcceptSalonInvitationCommand): Staff

    /**
     * Decline a salon invitation
     */
    fun declineInvitation(command: DeclineSalonInvitationCommand): SalonInvitation

    /**
     * Cancel a salon invitation
     */
    fun cancelInvitation(command: CancelSalonInvitationCommand): SalonInvitation

    /**
     * Resend a salon invitation
     */
    fun resendInvitation(command: ResendSalonInvitationCommand): SalonInvitation

    /**
     * Get invitation by ID
     */
    fun getInvitationById(query: GetInvitationByIdQuery): SalonInvitation?

    /**
     * Get pending invitations for a user by phone number
     */
    fun getPendingInvitationsByPhone(query: GetPendingInvitationsByPhoneQuery): List<SalonInvitation>

    /**
     * Get invitations sent by a salon
     */
    fun getInvitationsBySalon(query: GetInvitationsBySalonQuery): List<SalonInvitation>

    /**
     * Get invitations sent by a user
     */
    fun getInvitationsByInviter(query: GetInvitationsByInviterQuery): List<SalonInvitation>

    /**
     * Get pending invitations for a salon
     */
    fun getPendingInvitationsBySalon(query: GetPendingInvitationsBySalonQuery): List<SalonInvitation>

    /**
     * Check if user has pending invitations
     */
    fun hasPendingInvitations(query: HasPendingInvitationsQuery): Boolean

    /**
     * Process expired invitations (background task)
     */
    fun processExpiredInvitations(): Int
}
