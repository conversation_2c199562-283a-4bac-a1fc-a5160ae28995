package ro.animaliaprogramari.animalia.application.port.outbound

import ro.animaliaprogramari.animalia.domain.event.DomainEvent

/**
 * Outbound port for publishing domain events
 */
interface DomainEventPublisher {
    
    /**
     * Publish a single domain event
     */
    fun publish(event: DomainEvent)
    
    /**
     * Publish multiple domain events
     */
    fun publishAll(events: List<DomainEvent>)
}
