package ro.animaliaprogramari.animalia.application.port.outbound

import ro.animaliaprogramari.animalia.domain.model.FirebaseToken

/**
 * Outbound port for Firebase token validation
 * This will be implemented by Firebase Admin SDK adapter
 */
@FunctionalInterface
interface FirebaseTokenValidator {
    
    /**
     * Validate Firebase ID token and extract user information
     */
    fun validateToken(token: FirebaseToken): FirebaseTokenValidationResult
}

/**
 * Result of Firebase token validation
 */
data class FirebaseTokenValidationResult(
    val isValid: Boolean,
    val firebaseUid: String?,
    val email: String?,
    val name: String?,
    val emailVerified: Boolean = false,
    val errorMessage: String? = null,
    val phoneNumber: String?
) {
    companion object {
        fun success(
            firebaseUid: String,
            email: String,
            name: String,
            emailVerified: Boolean = false,
            phoneNumber: String?
        ): FirebaseTokenValidationResult {
            return FirebaseTokenValidationResult(
                isValid = true,
                firebaseUid = firebaseUid,
                email = email,
                name = name,
                emailVerified = emailVerified,
                phoneNumber = phoneNumber
            )
        }
        
        fun failure(errorMessage: String): FirebaseTokenValidationResult {
            return FirebaseTokenValidationResult(
                isValid = false,
                firebaseUid = null,
                email = null,
                name = null,
                errorMessage = errorMessage,
                phoneNumber = null
            )
        }
    }
}
