package ro.animaliaprogramari.animalia.application.port.outbound

import ro.animaliaprogramari.animalia.domain.model.ClientId
import ro.animaliaprogramari.animalia.domain.model.Salon
import ro.animaliaprogramari.animalia.domain.model.SalonId

/**
 * Outbound port for salon persistence
 * This is an interface that will be implemented by the persistence adapter
 */
interface SalonRepository {

    /**
     * Save a salon
     */
    fun save(salon: Salon): Salon

    /**
     * Find a salon by ID
     */
    fun findById(id: SalonId): Salon?

    /**
     * Find all salons with optional filtering
     */
    fun findAll(
        search: String? = null,
        isActive: Boolean? = null,
        limit: Int? = null,
        offset: Int? = null
    ): List<Salon>

    /**
     * Check if a salon exists by ID
     */
    fun existsById(id: SalonId): Boolean

    /**
     * Delete a salon by ID
     */
    fun deleteById(id: SalonId): Boolean

    /**
     * Count total salons with optional filtering
     */
    fun count(search: String? = null, isActive: Boolean? = null): Long

    /**
     * Find salons that have a specific client
     */
    fun findSalonsWithClient(clientId: ClientId): List<Salon>

    /**
     * Get all active salon IDs
     */
    fun findAllActiveSalonIds(): List<SalonId>
}
