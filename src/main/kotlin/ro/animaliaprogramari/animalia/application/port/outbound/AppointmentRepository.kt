package ro.animaliaprogramari.animalia.application.port.outbound

import ro.animaliaprogramari.animalia.domain.model.*
import java.time.LocalDate
import java.time.ZonedDateTime

/**
 * Outbound port for appointment persistence
 */
interface AppointmentRepository {
    
    /**
     * Save an appointment
     */
    fun save(appointment: Appointment): Appointment
    
    /**
     * Find an appointment by ID
     */
    fun findById(id: AppointmentId): Appointment?
    
    /**
     * Find appointments by date range
     */
    fun findByDateRange(startDate: LocalDate, endDate: LocalDate): List<Appointment>
    
    /**
     * Find appointments for a specific groomer and date range
     */
    fun findByGroomerAndDateRange(
        userId: UserId,
        startDate: LocalDate,
        endDate: LocalDate
    ): List<Appointment>
    
    /**
     * Find appointments for a specific client
     */
    fun findByClientId(clientId: ClientId): List<Appointment>
    
    /**
     * Find appointments for a specific pet
     */
    fun findByPetId(petId: PetId): List<Appointment>
    
    /**
     * Find appointments by status
     */
    fun findByStatus(status: AppointmentStatus): List<Appointment>
    
    /**
     * Find appointments for a specific date with optional filters
     */
    fun findByDate(
        date: LocalDate,
        userId: UserId? = null,
        status: AppointmentStatus? = null
    ): List<Appointment>
    
    /**
     * Find all appointments with optional filtering
     */
    fun findAll(
        startDate: LocalDate? = null,
        endDate: LocalDate? = null,
        userId: UserId? = null,
        clientId: ClientId? = null,
        status: AppointmentStatus? = null,
        limit: Int? = null,
        offset: Int? = null
    ): List<Appointment>
    
    /**
     * Check if an appointment exists by ID
     */
    fun existsById(id: AppointmentId): Boolean
    
    /**
     * Delete an appointment by ID
     */
    fun deleteById(id: AppointmentId): Boolean
    
    /**
     * Count total appointments with optional filtering
     */
    fun count(
        startDate: LocalDate? = null,
        endDate: LocalDate? = null,
        userId: UserId? = null,
        clientId: ClientId? = null,
        status: AppointmentStatus? = null
    ): Long

    // Salon-specific methods

    /**
     * Find appointments by salon ID with optional filters
     */
    fun findBySalonIdWithFilters(
        salonId: SalonId,
        date: LocalDate? = null,
        startDate: LocalDate? = null,
        endDate: LocalDate? = null,
        status: AppointmentStatus? = null,
        clientId: ClientId? = null,
        staffId: StaffId? = null
    ): List<Appointment>

    /**
     * Find appointments by date and status
     */
    fun findByDateAndStatus(
        date: LocalDate,
        status: AppointmentStatus
    ): List<Appointment>

    /**
     * Find appointments by salon ID and staff ID with optional filters
     */
    fun findBySalonIdAndStaffIdWithFilters(
        salonId: SalonId,
        staffId: StaffId,
        date: LocalDate? = null,
        startDate: LocalDate? = null,
        endDate: LocalDate? = null,
        status: AppointmentStatus? = null
    ): List<Appointment>

    /**
     * Find appointments by salon ID, staff ID and date
     */
    fun findBySalonIdAndStaffIdAndDate(
        salonId: SalonId,
        staffId: StaffId,
        date: LocalDate
    ): List<Appointment>

    /**
     * Find appointments by date range with optional filters (legacy support)
     */
    fun findByDateRange(
        startDate: LocalDate,
        endDate: LocalDate,
        staffId: StaffId? = null,
        status: AppointmentStatus? = null
    ): List<Appointment>

    /**
     * Find appointments by staff ID with optional filters (legacy support)
     */
    fun findByStaffId(
        staffId: StaffId,
        startDate: LocalDate? = null,
        endDate: LocalDate? = null,
        status: AppointmentStatus? = null
    ): List<Appointment>

    /**
     * Find appointments by staff ID and date (legacy support)
     */
    fun findByStaffIdAndDate(
        staffId: StaffId,
        date: LocalDate
    ): List<Appointment>

    /**
     * Find appointments by staff ID after a specific date
     */
    fun findByStaffIdAndDateAfter(
        staffId: StaffId,
        date: LocalDate
    ): List<Appointment>

    /**
     * Find appointments by staff ID between dates
     */
    fun findByStaffIdAndDateBetween(
        staffId: StaffId,
        startDate: LocalDate,
        endDate: LocalDate
    ): List<Appointment>

    /**
     * Find appointments by salon ID, staff IDs and time range for block time conflict detection
     */
    fun findBySalonIdAndStaffIdsAndTimeRange(
        salonId: SalonId,
        staffIds: Set<StaffId>,
        startTime: ZonedDateTime,
        endTime: ZonedDateTime
    ): List<Appointment>
}
