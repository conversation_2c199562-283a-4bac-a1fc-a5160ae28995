package ro.animaliaprogramari.animalia.application.port.outbound

/**
 * Outbound port for application monitoring
 * This will be implemented by monitoring adapters
 */
interface MonitoringService {

    /**
     * Record a metric with the given name and value
     */
    fun recordMetric(name: String, value: Double, tags: Map<String, String> = emptyMap())

    /**
     * Increment a counter metric
     */
    fun incrementCounter(name: String, tags: Map<String, String> = emptyMap())

    /**
     * Record timing for an operation
     */
    fun recordTiming(name: String, timeMs: Long, tags: Map<String, String> = emptyMap())

    /**
     * Record an event occurrence
     */
    fun recordEvent(eventType: String, details: Map<String, Any> = emptyMap())

    /**
     * Start a timer for an operation
     * Returns a Timer object that can be used to stop and record the timing
     */
    fun startTimer(name: String, tags: Map<String, String> = emptyMap()): Timer

    /**
     * Record workflow execution with success/failure tracking
     */
    fun recordWorkflowExecution(
        workflowName: String,
        success: Boolean,
        durationMs: Long,
        errorType: String? = null,
        tags: Map<String, String> = emptyMap()
    )

    /**
     * Record method execution timing and success rate
     */
    fun recordMethodExecution(
        methodName: String,
        className: String,
        success: Boolean,
        durationMs: Long,
        errorType: String? = null,
        tags: Map<String, String> = emptyMap()
    )

    /**
     * Timer interface for measuring operation duration
     */
    interface Timer {
        /**
         * Stop the timer and record the timing
         */
        fun stop()

        /**
         * Stop the timer and record with success/failure status
         */
        fun stop(success: Boolean, errorType: String? = null)
    }
}
