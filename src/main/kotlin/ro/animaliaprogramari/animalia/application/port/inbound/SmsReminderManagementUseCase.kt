package ro.animaliaprogramari.animalia.application.port.inbound

import ro.animaliaprogramari.animalia.application.command.SmsMessageCommand
import ro.animaliaprogramari.animalia.application.command.UpdateSmsReminderSettingsCommand
import ro.animaliaprogramari.animalia.application.query.GetSmsReminderSettingsQuery
import ro.animaliaprogramari.animalia.domain.model.SmsReminderSettings

/**
 * Inbound port for SMS reminder management use cases
 */
interface SmsReminderManagementUseCase {

    /**
     * Get SMS reminder settings for a salon
     */
    fun getSmsReminderSettings(query: GetSmsReminderSettingsQuery): SmsReminderSettings

    /**
     * Update SMS reminder settings for a salon
     */
    fun updateSmsReminderSettings(command: UpdateSmsReminderSettingsCommand): SmsReminderSettings


    fun sendSms(command: SmsMessageCommand): Unit
}
