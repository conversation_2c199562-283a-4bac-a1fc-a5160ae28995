package ro.animaliaprogramari.animalia.application.query

import ro.animaliaprogramari.animalia.domain.model.*
import java.time.LocalDate

/**
 * Query to get block time by ID
 */
data class GetBlockTimeByIdQuery(
    val salonId: SalonId,
    val blockId: BlockTimeId
)

/**
 * Query to get block time list with filtering and pagination
 */
data class GetBlockTimeListQuery(
    val salonId: SalonId,
    val startDate: LocalDate? = null,
    val endDate: LocalDate? = null,
    val staffId: StaffId? = null,
    val reason: BlockReason? = null,
    val status: BlockTimeStatus? = null,
    val page: Int = 1,
    val limit: Int = 50
) {
    init {
        require(page > 0) { "Page must be positive" }
        require(limit > 0) { "Limit must be positive" }
        require(limit <= 100) { "Limit cannot exceed 100" }
        if (startDate != null && endDate != null) {
            require(!endDate.isBefore(startDate)) { "End date must be after or equal to start date" }
        }
    }
}

/**
 * Query to get detailed block time information
 */
data class GetBlockTimeDetailsQuery(
    val salonId: SalonId,
    val blockId: BlockTimeId
)

/**
 * Query to check time availability for blocking
 */
data class CheckTimeAvailabilityQuery(
    val salonId: SalonId,
    val startTime: java.time.ZonedDateTime,
    val endTime: java.time.ZonedDateTime,
    val staffIds: Set<StaffId>
) {
    init {
        require(endTime.isAfter(startTime)) { "End time must be after start time" }
        require(staffIds.isNotEmpty()) { "At least one staff member must be specified" }
    }
}

/**
 * Query to get block time statistics
 */
data class GetBlockTimeStatisticsQuery(
    val salonId: SalonId,
    val startDate: LocalDate? = null,
    val endDate: LocalDate? = null,
    val staffId: StaffId? = null
) {
    init {
        if (startDate != null && endDate != null) {
            require(!endDate.isBefore(startDate)) { "End date must be after or equal to start date" }
        }
    }
}
