package ro.animaliaprogramari.animalia.application.query

import ro.animaliaprogramari.animalia.domain.model.ClientId
import ro.animaliaprogramari.animalia.domain.model.PetId

/**
 * Query to get pet by ID
 */
data class GetPetByIdQuery(
    val petId: PetId
)

/**
 * Query to get pets by client
 */
data class GetPetsByClientQuery(
    val clientId: ClientId,
    val activeOnly: Boolean = false
)

/**
 * Query to search pets
 */
data class SearchPetsQuery(
    val searchTerm: String,
    val clientId: ClientId? = null,
    val isActive: Boolean? = null,
    val limit: Int? = null,
    val offset: Int? = null
)

/**
 * Query to get all pets
 */
data class GetAllPetsQuery(
    val clientId: ClientId? = null,
    val isActive: Boolean? = null,
    val limit: Int? = null,
    val offset: Int? = null
)
