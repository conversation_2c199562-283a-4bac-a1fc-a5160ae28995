package ro.animaliaprogramari.animalia.application.query

import ro.animaliaprogramari.animalia.domain.model.*
import java.time.LocalDate
import java.time.LocalTime

/**
 * Query for getting staff working hours settings
 */
data class GetStaffWorkingHoursQuery(
    val staffId: StaffId,
    val salonId: SalonId,
    val requesterId: UserId
)

/**
 * Query for getting staff availability
 */
data class GetStaffAvailabilityQuery(
    val salonId: SalonId,
    val date: LocalDate,
    val startTime: LocalTime? = null,
    val endTime: LocalTime? = null,
    val requesterId: UserId
)
