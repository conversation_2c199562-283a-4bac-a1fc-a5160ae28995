package ro.animaliaprogramari.animalia.application.query

import ro.animaliaprogramari.animalia.domain.model.*
import java.time.LocalDate

/**
 * Query to get appointments by various filters
 */
data class GetAppointmentsByFiltersQuery(
    val salonId: SalonId,
    val date: LocalDate? = null,
    val startDate: LocalDate? = null,
    val endDate: LocalDate? = null,
    val status: AppointmentStatus? = null,
    val clientId: ClientId? = null,
    val staffId: StaffId? = null
)

/**
 * Query to get appointment by ID
 */
data class GetAppointmentByIdQuery(
    val appointmentId: AppointmentId,
    val salonId: SalonId? = null,
    val requesterId: UserId? = null
)

/**
 * Query to get staff workload
 */
data class GetStaffWorkloadQuery(
    val staffId: StaffId,
    val startDate: LocalDate,
    val endDate: LocalDate
)

/**
 * Query to get staff performance
 */
data class GetStaffPerformanceQuery(
    val staffId: StaffId,
    val period: DateRange
)



/**
 * Query to get staff schedule
 */
data class GetStaffScheduleQuery(
    val staffId: StaffId,
    val startDate: LocalDate,
    val endDate: LocalDate
)
