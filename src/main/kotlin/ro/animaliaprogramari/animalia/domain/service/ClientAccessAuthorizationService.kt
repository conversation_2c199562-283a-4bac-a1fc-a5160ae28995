package ro.animaliaprogramari.animalia.domain.service

import ro.animaliaprogramari.animalia.domain.model.*
import org.springframework.stereotype.Service

/**
 * Domain service for client access authorization
 */
@Service
class ClientAccessAuthorizationService {

    /**
     * Check if staff member can access client data
     */
    fun canAccessClientData(staff: Staff, clientId: ClientId): <PERSON><PERSON><PERSON> {
        return when (staff.permissions.clientDataAccess) {
            ClientDataAccess.NONE -> false
            ClientDataAccess.LIMITED -> staff.role.canAccessClientData()
            ClientDataAccess.FULL -> true
        }
    }

    /**
     * Check if staff member can modify client data
     */
    fun canModifyClientData(staff: Staff, clientId: ClientId): <PERSON><PERSON><PERSON> {
        return staff.permissions.clientDataAccess == ClientDataAccess.FULL &&
                staff.role.canManageStaff()
    }

    /**
     * Check if staff member can view client appointments
     */
    fun canViewClientAppointments(staff: Staff, clientId: ClientId): Bo<PERSON>an {
        return canAccessClientData(staff, clientId) && staff.permissions.canManageAppointments
    }

    /**
     * Check if staff member can manage client appointments
     */
    fun canManageClientAppointments(staff: Staff, clientId: ClientId): Boolean {
        return canAccessClientData(staff, clientId) && 
               staff.permissions.canManageAppointments &&
               staff.role.canAccessClientData()
    }
}
