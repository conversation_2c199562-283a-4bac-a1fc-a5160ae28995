package ro.animaliaprogramari.animalia.domain.service

import ro.animaliaprogramari.animalia.domain.model.*
import java.math.BigDecimal

/**
 * Domain service for pricing calculations
 * Contains pure business logic for pricing rules
 */
class PricingService {
    
    /**
     * Calculate total price for an appointment
     */
    fun calculateAppointmentPrice(
        services: List<SalonService>,
        pet: Pet,
        client: Client,
        discounts: List<Discount> = emptyList()
    ): Money {
        val basePrice = calculateBasePrice(services, pet)
        val discountAmount = calculateDiscounts(basePrice, discounts, client, pet)
        return basePrice.subtract(discountAmount)
    }
    
    /**
     * Calculate base price before discounts
     */
    private fun calculateBasePrice(services: List<SalonService>, pet: Pet): Money {
        val baseTotal = services.fold(Money.ZERO) { total, service ->
            total.add(service.basePrice)
        }
        
        // Apply size-based pricing adjustments
        val sizeMultiplier = calculateSizeMultiplier(pet)
        return baseTotal.multiply(sizeMultiplier)
    }
    
    /**
     * Calculate size-based pricing multiplier
     */
    private fun calculateSizeMultiplier(pet: Pet): BigDecimal {
        val weight = pet.weight ?: return BigDecimal.ONE
        
        return when {
            weight <= BigDecimal("5") -> BigDecimal("0.8")  // Small pets (cats, small dogs)
            weight <= BigDecimal("15") -> BigDecimal.ONE     // Medium pets
            weight <= BigDecimal("30") -> BigDecimal("1.2")  // Large pets
            else -> BigDecimal("1.5")                        // Extra large pets
        }
    }
    
    /**
     * Calculate total discount amount
     */
    private fun calculateDiscounts(
        basePrice: Money,
        discounts: List<Discount>,
        client: Client,
        pet: Pet
    ): Money {
        return discounts.fold(Money.ZERO) { total, discount ->
            val discountAmount = when (discount.type) {
                DiscountType.PERCENTAGE -> basePrice.multiply(discount.value.divide(BigDecimal("100")))
                DiscountType.FIXED_AMOUNT -> Money.of(discount.value)
                DiscountType.SENIOR_PET -> if (pet.isSenior()) Money.of(discount.value) else Money.ZERO
                DiscountType.FIRST_TIME_CLIENT -> if (isFirstTimeClient(client)) Money.of(discount.value) else Money.ZERO
                DiscountType.LOYALTY -> calculateLoyaltyDiscount(basePrice, discount.value, client)
            }
            total.add(discountAmount)
        }
    }
    
    /**
     * Check if this is a first-time client
     * Note: This would typically require checking appointment history
     */
    private fun isFirstTimeClient(client: Client): Boolean {
        // This is a simplified implementation
        // In a real system, this would check the client's appointment history
        return false
    }
    
    /**
     * Calculate loyalty discount based on client history
     */
    private fun calculateLoyaltyDiscount(basePrice: Money, discountPercentage: BigDecimal, client: Client): Money {
        // This is a simplified implementation
        // In a real system, this would check the client's appointment history and calculate loyalty level
        return Money.ZERO
    }
    
    /**
     * Calculate price estimate for services
     */
    fun calculatePriceEstimate(
        services: List<SalonService>,
        petWeight: BigDecimal? = null
    ): PriceEstimate {
        val basePrice = services.fold(Money.ZERO) { total, service ->
            total.add(service.basePrice)
        }
        
        val sizeMultiplier = petWeight?.let { weight ->
            when {
                weight <= BigDecimal("5") -> BigDecimal("0.8")
                weight <= BigDecimal("15") -> BigDecimal.ONE
                weight <= BigDecimal("30") -> BigDecimal("1.2")
                else -> BigDecimal("1.5")
            }
        } ?: BigDecimal.ONE
        
        val adjustedPrice = basePrice.multiply(sizeMultiplier)
        
        // Calculate price range (±10% for potential discounts/surcharges)
        val minPrice = adjustedPrice.multiply(BigDecimal("0.9"))
        val maxPrice = adjustedPrice.multiply(BigDecimal("1.1"))
        
        return PriceEstimate(
            estimatedPrice = adjustedPrice,
            minPrice = minPrice,
            maxPrice = maxPrice,
            basePrice = basePrice,
            sizeAdjustment = sizeMultiplier
        )
    }
}

/**
 * Data class representing a discount
 */
data class Discount(
    val type: DiscountType,
    val value: BigDecimal,
    val description: String
)

/**
 * Enumeration for discount types
 */
enum class DiscountType {
    PERCENTAGE,
    FIXED_AMOUNT,
    SENIOR_PET,
    FIRST_TIME_CLIENT,
    LOYALTY
}

/**
 * Data class representing a price estimate
 */
data class PriceEstimate(
    val estimatedPrice: Money,
    val minPrice: Money,
    val maxPrice: Money,
    val basePrice: Money,
    val sizeAdjustment: BigDecimal
)
