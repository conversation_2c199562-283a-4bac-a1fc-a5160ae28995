package ro.animaliaprogramari.animalia.domain.service

import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import ro.animaliaprogramari.animalia.domain.exception.*
import ro.animaliaprogramari.animalia.domain.model.*
import java.time.ZonedDateTime
import java.time.temporal.ChronoUnit

/**
 * Domain service for block time scheduling operations
 * Handles conflict detection and validation logic
 */
@Service
class BlockTimeSchedulingService {

    private val logger = LoggerFactory.getLogger(BlockTimeSchedulingService::class.java)

    /**
     * Validate that a block time can be created without conflicts
     */
    fun validateBlockTimeCreation(
        salonId: SalonId,
        startTime: ZonedDateTime,
        endTime: ZonedDateTime,
        staffIds: Set<StaffId>,
        existingBlocks: List<BlockTime>,
        existingAppointments: List<Appointment>
    ): ValidationResult {
        
        // Basic time validation
        validateTimeRange(startTime, endTime)
        
        // Check for conflicts
        val conflicts = detectConflicts(
            startTime, endTime, staffIds, existingBlocks, existingAppointments
        )
        
        return if (conflicts.isEmpty()) {
            ValidationResult.success()
        } else {
            ValidationResult.conflicted(conflicts)
        }
    }

    /**
     * Detect conflicts with existing blocks and appointments
     */
    fun detectConflicts(
        startTime: ZonedDateTime,
        endTime: ZonedDateTime,
        staffIds: Set<StaffId>,
        existingBlocks: List<BlockTime>,
        existingAppointments: List<Appointment>
    ): List<BlockTimeConflict> {
        logger.debug("=== BLOCK TIME CONFLICT DETECTION START ===")
        logger.debug("Block time request details:")
        logger.debug("  - Start time: {}", startTime)
        logger.debug("  - End time: {}", endTime)
        logger.debug("  - Staff IDs: {}", staffIds.map { it.value })
        logger.debug("  - Existing blocks count: {}", existingBlocks.size)
        logger.debug("  - Existing appointments count: {}", existingAppointments.size)

        val conflicts = mutableListOf<BlockTimeConflict>()

        // Check conflicts with existing blocks
        for (staffId in staffIds) {
            logger.debug("--- Checking conflicts for staff: {} ---", staffId.value)

            val blockConflicts = findBlockConflicts(staffId, startTime, endTime, existingBlocks)
            logger.debug("Found {} block conflicts for staff {}", blockConflicts.size, staffId.value)
            conflicts.addAll(blockConflicts)

            val appointmentConflicts = findAppointmentConflicts(staffId, startTime, endTime, existingAppointments)
            logger.debug("Found {} appointment conflicts for staff {}", appointmentConflicts.size, staffId.value)
            conflicts.addAll(appointmentConflicts)
        }

        logger.debug("=== BLOCK TIME CONFLICT DETECTION COMPLETE ===")
        logger.debug("Total conflicts found: {}", conflicts.size)
        if (conflicts.isNotEmpty()) {
            logger.debug("Conflict details: {}", conflicts.map {
                "Staff=${it.staffId.value}, Type=${it.conflictType}, Time=${it.conflictDetails.startTime}-${it.conflictDetails.endTime}"
            })
        }

        return conflicts
    }

    /**
     * Suggest alternative time slots when conflicts exist
     */
    fun suggestAlternativeTimeSlots(
        startTime: ZonedDateTime,
        endTime: ZonedDateTime,
        staffIds: Set<StaffId>,
        existingBlocks: List<BlockTime>,
        existingAppointments: List<Appointment>
    ): List<TimeSlotSuggestion> {
        
        val suggestions = mutableListOf<TimeSlotSuggestion>()
        val duration = ChronoUnit.MINUTES.between(startTime, endTime)
        
        // Try earlier time slots
        var earlierStart = startTime.minusHours(1)
        var earlierEnd = earlierStart.plusMinutes(duration)
        
        if (detectConflicts(earlierStart, earlierEnd, staffIds, existingBlocks, existingAppointments).isEmpty()) {
            suggestions.add(
                TimeSlotSuggestion(
                    type = SuggestionType.ADJUST_TIME,
                    description = "Ajustați intervalul la ${earlierStart.toLocalTime()}-${earlierEnd.toLocalTime()}",
                    startTime = earlierStart,
                    endTime = earlierEnd,
                    staffIds = staffIds
                )
            )
        }
        
        // Try later time slots
        var laterStart = startTime.plusHours(1)
        var laterEnd = laterStart.plusMinutes(duration)
        
        if (detectConflicts(laterStart, laterEnd, staffIds, existingBlocks, existingAppointments).isEmpty()) {
            suggestions.add(
                TimeSlotSuggestion(
                    type = SuggestionType.ADJUST_TIME,
                    description = "Ajustați intervalul la ${laterStart.toLocalTime()}-${laterEnd.toLocalTime()}",
                    startTime = laterStart,
                    endTime = laterEnd,
                    staffIds = staffIds
                )
            )
        }
        
        // Suggest excluding conflicted staff
        val availableStaff = staffIds.filter { staffId ->
            detectConflicts(startTime, endTime, setOf(staffId), existingBlocks, existingAppointments).isEmpty()
        }.toSet()
        
        if (availableStaff.isNotEmpty() && availableStaff.size < staffIds.size) {
            suggestions.add(
                TimeSlotSuggestion(
                    type = SuggestionType.EXCLUDE_STAFF,
                    description = "Excludeți membrii echipei cu conflicte",
                    startTime = startTime,
                    endTime = endTime,
                    staffIds = availableStaff
                )
            )
        }
        
        return suggestions
    }

    /**
     * Validate time range for block creation
     */
    private fun validateTimeRange(startTime: ZonedDateTime, endTime: ZonedDateTime) {
        val now = ZonedDateTime.now()
        
        // Check if start time is in the past (with 5-minute buffer)
        if (startTime.isBefore(now.minusMinutes(5))) {
            throw PastTimeBlockException("Block start time must be at least 5 minutes in the future")
        }
        
        // Check if end time is after start time
        if (!endTime.isAfter(startTime)) {
            throw InvalidBlockDurationException("End time must be after start time")
        }
        
        // Check duration limits
        val durationMinutes = ChronoUnit.MINUTES.between(startTime, endTime)
        if (durationMinutes < 15) {
            throw InvalidBlockDurationException("Block duration must be at least 15 minutes")
        }
        
        if (durationMinutes > 720) { // 12 hours
            throw InvalidBlockDurationException("Block duration cannot exceed 12 hours")
        }
    }

    /**
     * Find conflicts with existing block times
     */
    private fun findBlockConflicts(
        staffId: StaffId,
        startTime: ZonedDateTime,
        endTime: ZonedDateTime,
        existingBlocks: List<BlockTime>
    ): List<BlockTimeConflict> {
        logger.debug("Checking block conflicts for staff {}", staffId.value)

        val relevantBlocks = existingBlocks.filter { it.isActive() && it.affectsStaff(staffId) }
        logger.debug("Found {} active blocks affecting staff {}", relevantBlocks.size, staffId.value)

        val conflictingBlocks = relevantBlocks.filter { it.overlapsWith(startTime, endTime) }
        logger.debug("Found {} overlapping blocks for staff {}", conflictingBlocks.size, staffId.value)

        conflictingBlocks.forEach { block ->
            logger.debug("BLOCK CONFLICT DETECTED:")
            logger.debug("  - Block ID: {}", block.id.value)
            logger.debug("  - Block time: {} - {}", block.startTime, block.endTime)
            logger.debug("  - Requested time: {} - {}", startTime, endTime)
            logger.debug("  - Block reason: {}", block.getDisplayReason())
            logger.debug("  - Block affects staff: {}", block.affectsStaff(staffId))
        }

        return conflictingBlocks.map { block ->
            BlockTimeConflict(
                staffId = staffId,
                staffName = "Staff Member", // Will be populated by use case
                conflictType = ConflictType.BLOCK,
                conflictDetails = ConflictDetails(
                    id = block.id.value,
                    startTime = block.startTime,
                    endTime = block.endTime,
                    description = "Existing block: ${block.getDisplayReason()}"
                )
            )
        }
    }

    /**
     * Find conflicts with existing appointments
     */
    private fun findAppointmentConflicts(
        staffId: StaffId,
        startTime: ZonedDateTime,
        endTime: ZonedDateTime,
        existingAppointments: List<Appointment>
    ): List<BlockTimeConflict> {
        logger.debug("Checking appointment conflicts for staff {}", staffId.value)

        val relevantAppointments = existingAppointments.filter { it.staffId == staffId }
        logger.debug("Found {} appointments for staff {}", relevantAppointments.size, staffId.value)

        val conflictingAppointments = relevantAppointments.filter { appointment ->
            val appointmentStart = appointment.appointmentDate.atTime(appointment.startTime).atZone(startTime.zone)
            val appointmentEnd = appointment.appointmentDate.atTime(appointment.endTime).atZone(startTime.zone)
            val hasOverlap = appointmentStart.isBefore(endTime) && appointmentEnd.isAfter(startTime)

            if (hasOverlap) {
                logger.debug("APPOINTMENT CONFLICT DETECTED:")
                logger.debug("  - Appointment ID: {}", appointment.id.value)
                logger.debug("  - Appointment time: {} - {}", appointmentStart, appointmentEnd)
                logger.debug("  - Requested block time: {} - {}", startTime, endTime)
                logger.debug("  - Appointment status: {}", appointment.status)
            }

            hasOverlap
        }

        logger.debug("Found {} conflicting appointments for staff {}", conflictingAppointments.size, staffId.value)

        return conflictingAppointments.map { appointment ->
            BlockTimeConflict(
                staffId = staffId,
                staffName = "Staff Member", // Will be populated by use case
                conflictType = ConflictType.APPOINTMENT,
                conflictDetails = ConflictDetails(
                    id = appointment.id.value,
                    startTime = appointment.appointmentDate.atTime(appointment.startTime).atZone(startTime.zone),
                    endTime = appointment.appointmentDate.atTime(appointment.endTime).atZone(startTime.zone),
                    description = "Appointment with client"
                )
            )
        }
    }
}

/**
 * Result of block time validation
 */
sealed class ValidationResult {
    object Success : ValidationResult()
    data class Conflicted(val conflictList: List<BlockTimeConflict>) : ValidationResult()

    companion object {
        fun success(): ValidationResult = Success
        fun conflicted(conflicts: List<BlockTimeConflict>): ValidationResult = Conflicted(conflicts)
    }

    fun isSuccess(): Boolean = this is Success
    fun getConflicts(): List<BlockTimeConflict> = if (this is Conflicted) conflictList else emptyList()
}

/**
 * Time slot suggestion for resolving conflicts
 */
data class TimeSlotSuggestion(
    val type: SuggestionType,
    val description: String,
    val startTime: ZonedDateTime,
    val endTime: ZonedDateTime,
    val staffIds: Set<StaffId>
)

/**
 * Types of suggestions for conflict resolution
 */
enum class SuggestionType {
    ADJUST_TIME,
    EXCLUDE_STAFF,
    FORCE_BLOCK
}
