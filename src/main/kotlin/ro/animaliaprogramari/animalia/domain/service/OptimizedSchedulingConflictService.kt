package ro.animaliaprogramari.animalia.domain.service

import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import ro.animaliaprogramari.animalia.domain.model.*
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZoneId

/**
 * Optimized service for fast, accurate scheduling conflict detection
 * Provides clear, human-readable conflict explanations with minimal overhead
 */
@Service
class OptimizedSchedulingConflictService {

    private val logger = LoggerFactory.getLogger(OptimizedSchedulingConflictService::class.java)

    /**
     * Detect all conflicts for an appointment request
     * Returns detailed conflict information with human-readable explanations
     */
    fun detectConflicts(
        date: LocalDate,
        startTime: LocalTime,
        endTime: LocalTime,
        staffId: StaffId,
        salonHours: WorkingHoursSettings,
        staffHours: StaffWorkingHoursSettings,
        appointments: List<Appointment>,
        blocks: List<BlockTime>
    ): List<DetailedScheduleConflict> {
        
        val conflicts = mutableListOf<DetailedScheduleConflict>()
        val requestedSlot = TimeSlot.of(startTime, endTime)
        
        logger.debug("Checking conflicts for {}, {}-{}, staff={}", date, startTime, endTime, staffId.value)

        // 1. Check salon availability (fast path - check closure first)
        checkSalonAvailability(date, requestedSlot, salonHours)?.let { conflicts.add(it) }

        // 2. Check staff availability (fast path - check closure first)  
        checkStaffAvailability(date, requestedSlot, staffHours)?.let { conflicts.add(it) }

        // 3. Check appointment overlaps (only if staff/salon are available)
        if (conflicts.isEmpty()) {
            checkAppointmentOverlaps(date, requestedSlot, staffId, appointments)?.let { conflicts.add(it) }
        }

        // 4. Check block time overlaps (only if no other conflicts)
        if (conflicts.isEmpty()) {
            checkBlockTimeOverlaps(requestedSlot, staffId, blocks)?.let { conflicts.add(it) }
        }

        logConflictResults(conflicts, date, startTime, endTime)
        return conflicts
    }

    /**
     * Fast availability check without detailed conflict information
     */
    fun isSlotAvailable(
        date: LocalDate,
        startTime: LocalTime,
        endTime: LocalTime,
        staffId: StaffId,
        salonHours: WorkingHoursSettings,
        staffHours: StaffWorkingHoursSettings,
        appointments: List<Appointment>,
        blocks: List<BlockTime>
    ): Boolean {
        return detectConflicts(date, startTime, endTime, staffId, salonHours, staffHours, appointments, blocks).isEmpty()
    }

    // Private helper methods for specific conflict checks

    private fun checkSalonAvailability(
        date: LocalDate,
        requestedSlot: TimeSlot,
        salonHours: WorkingHoursSettings
    ): DetailedScheduleConflict? {
        
        // Check if salon is closed on this date
        if (!salonHours.isOpenOn(date)) {
            val reason = getSalonClosureReason(date, salonHours)
            logger.error("SALON CLOSED: {} - {}", date, reason)
            return DetailedScheduleConflict(
                type = ScheduleConflictType.SALON_CLOSED,
                reason = "Salon is closed on ${date.dayOfWeek.name.lowercase()}: $reason",
                details = mapOf(
                    "date" to date.toString(),
                    "dayOfWeek" to date.dayOfWeek.name,
                    "closureReason" to reason
                )
            )
        }

        // Check if requested time is within salon working hours
        val salonDay = salonHours.getWorkingHoursFor(date)
        if (salonDay == null || !isTimeSlotAvailable(requestedSlot, salonDay)) {
            val workingHours = salonDay?.let { "${it.startTime}-${it.endTime}" } ?: "Not configured"
            val breakTime = salonDay?.let { 
                if (it.breakStart != null && it.breakEnd != null) "${it.breakStart}-${it.breakEnd}" else "None"
            } ?: "None"
            
            logger.error("SALON HOURS CONFLICT: Requested {}-{} outside salon hours {} or during break {}", 
                requestedSlot.startTime, requestedSlot.endTime, workingHours, breakTime)
            
            return DetailedScheduleConflict(
                type = ScheduleConflictType.SALON_CLOSED,
                reason = "Requested time ${requestedSlot.startTime}-${requestedSlot.endTime} is outside salon hours ($workingHours) or during break ($breakTime)",
                details = mapOf(
                    "requestedTime" to "${requestedSlot.startTime}-${requestedSlot.endTime}",
                    "salonHours" to workingHours,
                    "breakTime" to breakTime
                )
            )
        }

        return null
    }

    private fun checkStaffAvailability(
        date: LocalDate,
        requestedSlot: TimeSlot,
        staffHours: StaffWorkingHoursSettings
    ): DetailedScheduleConflict? {
        
        // Check if staff is available on this date
        if (!staffHours.isAvailableOn(date)) {
            val reason = getStaffUnavailabilityReason(date, staffHours)
            logger.error("STAFF UNAVAILABLE: {} - {}", date, reason)
            return DetailedScheduleConflict(
                type = ScheduleConflictType.STAFF_UNAVAILABLE,
                reason = "Staff is unavailable on ${date.dayOfWeek.name.lowercase()}: $reason",
                details = mapOf(
                    "date" to date.toString(),
                    "dayOfWeek" to date.dayOfWeek.name,
                    "unavailabilityReason" to reason
                )
            )
        }

        // Check if requested time is within staff working hours
        val staffDay = staffHours.getWorkingHoursFor(date)
        if (staffDay == null || !isTimeSlotAvailable(requestedSlot, staffDay)) {
            val workingHours = staffDay?.let { "${it.startTime}-${it.endTime}" } ?: "Not configured"
            val breakTime = staffDay?.let { 
                if (it.breakStart != null && it.breakEnd != null) "${it.breakStart}-${it.breakEnd}" else "None"
            } ?: "None"
            
            logger.error("STAFF HOURS CONFLICT: Requested {}-{} outside staff hours {} or during break {}", 
                requestedSlot.startTime, requestedSlot.endTime, workingHours, breakTime)
            
            return DetailedScheduleConflict(
                type = ScheduleConflictType.STAFF_UNAVAILABLE,
                reason = "Requested time ${requestedSlot.startTime}-${requestedSlot.endTime} is outside staff hours ($workingHours) or during break ($breakTime)",
                details = mapOf(
                    "requestedTime" to "${requestedSlot.startTime}-${requestedSlot.endTime}",
                    "staffHours" to workingHours,
                    "breakTime" to breakTime
                )
            )
        }

        return null
    }

    private fun checkAppointmentOverlaps(
        date: LocalDate,
        requestedSlot: TimeSlot,
        staffId: StaffId,
        appointments: List<Appointment>
    ): DetailedScheduleConflict? {
        
        val conflictingAppointment = appointments
            .filter { it.staffId == staffId && it.appointmentDate == date && it.isActive() }
            .find { TimeSlot.of(it.startTime, it.endTime).overlaps(requestedSlot) }

        return conflictingAppointment?.let { appt ->
            logger.error("APPOINTMENT OVERLAP: Requested {}-{} overlaps with existing appointment {}-{} ({})", 
                requestedSlot.startTime, requestedSlot.endTime, appt.startTime, appt.endTime, appt.status)
            
            DetailedScheduleConflict(
                type = ScheduleConflictType.APPOINTMENT,
                reason = "Overlaps with existing appointment ${appt.startTime}-${appt.endTime} (${appt.status})",
                details = mapOf(
                    "conflictingAppointmentId" to appt.id.value,
                    "conflictingTime" to "${appt.startTime}-${appt.endTime}",
                    "conflictingStatus" to appt.status.name,
                    "requestedTime" to "${requestedSlot.startTime}-${requestedSlot.endTime}"
                )
            )
        }
    }

    private fun checkBlockTimeOverlaps(
        requestedSlot: TimeSlot,
        staffId: StaffId,
        blocks: List<BlockTime>
    ): DetailedScheduleConflict? {
        
        val conflictingBlock = blocks
            .filter { it.isActive() && it.affectsStaff(staffId) }
            .find { 
                val blockSlot = TimeSlot.of(it.startTime.toLocalTime(), it.endTime.toLocalTime())
                blockSlot.overlaps(requestedSlot)
            }

        return conflictingBlock?.let { block ->
            logger.error("BLOCK TIME OVERLAP: Requested {}-{} overlaps with blocked time {}-{}: {}", 
                requestedSlot.startTime, requestedSlot.endTime, 
                block.startTime.toLocalTime(), block.endTime.toLocalTime(), block.getDisplayReason())
            
            DetailedScheduleConflict(
                type = ScheduleConflictType.BLOCK_TIME,
                reason = "Overlaps with blocked time ${block.startTime.toLocalTime()}-${block.endTime.toLocalTime()}: ${block.getDisplayReason()}",
                details = mapOf(
                    "blockId" to block.id.value,
                    "blockTime" to "${block.startTime.toLocalTime()}-${block.endTime.toLocalTime()}",
                    "blockReason" to block.getDisplayReason(),
                    "requestedTime" to "${requestedSlot.startTime}-${requestedSlot.endTime}"
                )
            )
        }
    }

    // Helper methods

    private fun isTimeSlotAvailable(requestedSlot: TimeSlot, daySchedule: DaySchedule): Boolean {
        return daySchedule.isAvailableAt(requestedSlot.startTime) && 
               daySchedule.isAvailableAt(requestedSlot.endTime.minusMinutes(1))
    }

    private fun getSalonClosureReason(date: LocalDate, salonHours: WorkingHoursSettings): String {
        // Check custom closures first
        salonHours.customClosures.find { it.date == date }?.let { return it.reason }
        
        // Check holidays
        salonHours.holidays.find { it.date == date && !it.isWorkingDay }?.let { return it.name }
        
        // Check if it's a non-working day
        val daySchedule = salonHours.weeklySchedule[date.dayOfWeek]
        if (daySchedule == null || !daySchedule.isWorkingDay) {
            return "Non-working day"
        }
        
        return "Closed"
    }

    private fun getStaffUnavailabilityReason(date: LocalDate, staffHours: StaffWorkingHoursSettings): String {
        // Check custom closures first
        staffHours.customClosures.find { it.date == date }?.let { return it.reason }
        
        // Check holidays
        staffHours.holidays.find { it.date == date && !it.isWorkingDay }?.let { return it.name }
        
        // Check if it's a non-working day
        val daySchedule = staffHours.weeklySchedule[date.dayOfWeek]
        if (daySchedule == null || !daySchedule.isWorkingDay) {
            return "Non-working day"
        }
        
        return "Unavailable"
    }

    private fun logConflictResults(conflicts: List<DetailedScheduleConflict>, date: LocalDate, startTime: LocalTime, endTime: LocalTime) {
        if (conflicts.isEmpty()) {
            logger.debug("✅ AVAILABLE: {} {}-{}", date, startTime, endTime)
        } else {
            logger.warn("❌ CONFLICTS ({}): {} {}-{}", conflicts.size, date, startTime, endTime)
            conflicts.forEach { conflict ->
                logger.warn("   - {}: {}", conflict.type, conflict.reason)
            }
        }
    }
}

/**
 * Enhanced conflict information with detailed explanations
 */
data class DetailedScheduleConflict(
    val type: ScheduleConflictType,
    val reason: String,
    val details: Map<String, String> = emptyMap()
)

/** Types of schedule conflicts */
enum class ScheduleConflictType {
    SALON_CLOSED,
    STAFF_UNAVAILABLE,
    APPOINTMENT,
    BLOCK_TIME
}

data class ScheduleConflict(val type: ScheduleConflictType)

// Extension function to check if appointment is active
fun Appointment.isActive(): Boolean {
    return this.status in listOf(
        AppointmentStatus.SCHEDULED,
        AppointmentStatus.CONFIRMED,
        AppointmentStatus.IN_PROGRESS
    )
}
