package ro.animaliaprogramari.animalia.domain.service

import ro.animaliaprogramari.animalia.domain.exception.BusinessRuleViolationException
import ro.animaliaprogramari.animalia.domain.exception.TimeSlotUnavailableException
import ro.animaliaprogramari.animalia.domain.model.*
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime

/**
 * Domain service for appointment scheduling business logic
 * Contains pure business rules with no external dependencies
 */
class AppointmentSchedulingService {

    /**
     * Check if the time slot is available (no conflicting appointments)
     */
    fun validateTimeSlotAvailability(
        userId: StaffId,
        appointmentDate: LocalDate,
        startTime: LocalTime,
        endTime: LocalTime,
        existingAppointments: List<Appointment>,
        excludeAppointmentId: AppointmentId? = null
    ) {
        val conflictingAppointments = existingAppointments
            .filter { it.staffId.value == userId.value } // Note: Still using groomerId field until Appointment model is updated
            .filter { it.appointmentDate == appointmentDate }
            .filter { it.id != excludeAppointmentId }
            .filter { it.status !in listOf(AppointmentStatus.CANCELLED, AppointmentStatus.NO_SHOW) }
            .filter { appointment ->
                // Check for time overlap
                val appointmentStart = appointment.startTime
                val appointmentEnd = appointment.endTime
                
                !(endTime.isBefore(appointmentStart) || endTime == appointmentStart ||
                  startTime.isAfter(appointmentEnd) || startTime == appointmentEnd)
            }
        
        if (conflictingAppointments.isNotEmpty()) {
            throw TimeSlotUnavailableException(
                "Time slot conflicts with existing appointment(s): ${conflictingAppointments.map { it.id.value }}"
            )
        }
    }
    
    /**
     * Validate that the appointment duration matches the services
     */
    private fun validateServiceDuration(
        services: List<SalonService>,
        startTime: LocalTime,
        endTime: LocalTime
    ) {
        val totalServiceDuration = services.fold(Duration.ofMinutes(0)) { total, service ->
            total.add(service.duration)
        }
        
        val appointmentDuration = Duration.ofMinutes(
            java.time.Duration.between(startTime, endTime).toMinutes().toInt()
        )
        
        if (appointmentDuration.minutes < totalServiceDuration.minutes) {
            throw BusinessRuleViolationException(
                "Appointment duration (${appointmentDuration}) is less than total service duration (${totalServiceDuration})"
            )
        }
        
        // Allow some buffer time, but not too much
        val maxAllowedDuration = totalServiceDuration.add(Duration.ofMinutes(30))
        if (appointmentDuration.minutes > maxAllowedDuration.minutes) {
            throw BusinessRuleViolationException(
                "Appointment duration (${appointmentDuration}) is too much longer than service duration (${totalServiceDuration})"
            )
        }
    }
    
    /**
     * Validate appointment timing (not in the past, reasonable advance notice)
     */
    private fun validateAppointmentTiming(appointmentDate: LocalDate, startTime: LocalTime) {
        val now = LocalDateTime.now()
        val appointmentDateTime = LocalDateTime.of(appointmentDate, startTime)
        
        if (appointmentDateTime.isBefore(now)) {
            throw BusinessRuleViolationException("Cannot schedule appointments in the past")
        }
        
        // Require at least 1 hour advance notice for same-day appointments
        if (appointmentDate == now.toLocalDate() && 
            appointmentDateTime.isBefore(now.plusHours(1))) {
            throw BusinessRuleViolationException(
                "Same-day appointments require at least 1 hour advance notice"
            )
        }
    }
    
    /**
     * Calculate recommended appointment duration for given services
     */
    fun calculateRecommendedDuration(services: List<SalonService>): Duration {
        val totalServiceDuration = services.fold(Duration.ofMinutes(0)) { total, service ->
            total.add(service.duration)
        }
        
        // Add 15 minutes buffer for setup and cleanup
        return totalServiceDuration.add(Duration.ofMinutes(15))
    }

}
