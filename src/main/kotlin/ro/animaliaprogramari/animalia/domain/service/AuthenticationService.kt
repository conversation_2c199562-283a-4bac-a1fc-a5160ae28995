package ro.animaliaprogramari.animalia.domain.service

import ro.animaliaprogramari.animalia.domain.model.*

/**
 * Domain service for authentication business logic
 * Contains pure business rules with no infrastructure dependencies
 */
class AuthenticationService {

    /**
     * Validate user authentication requirements
     */
    fun validateUserForAuthentication(user: User): AuthenticationValidationResult {
        return when {
            !user.isActive -> AuthenticationValidationResult.UserInactive
            user.name.isBlank() -> AuthenticationValidationResult.InvalidUserData("User name is required")
            user.firebaseUid.isBlank() -> AuthenticationValidationResult.InvalidUserData("Firebase UID is required")
            else -> AuthenticationValidationResult.Valid
        }
    }

    /**
     * Determine user role based on email domain and existing data
     */
    fun determineUserRole(email: Email?, phoneNumber: String?, existingUser: User?): UserRole {
        // If user already exists, keep their existing role
        existingUser?.let { return it.role }

        // Business rules for role assignment
        return when {
            email != null && isAdminEmail(email) -> UserRole.ADMIN
            email != null && isGroomerEmail(email) -> UserRole.STAFF
            phoneNumber != null && isAdminPhone(phoneNumber) -> UserRole.ADMIN
            else -> UserRole.USER
        }
    }

    /**
     * Check if email belongs to admin domain
     */
    private fun isAdminEmail(email: Email): Boolean {
        val adminDomains = listOf("animalia-grooming.ro", "admin.animalia.ro")
        return adminDomains.any { domain ->
            email.value.endsWith("@$domain", ignoreCase = true)
        }
    }

    /**
     * Check if email belongs to groomer domain
     */
    private fun isGroomerEmail(email: Email): Boolean {
        val groomerDomains = listOf("groomer.animalia.ro")
        return groomerDomains.any { domain ->
            email.value.endsWith("@$domain", ignoreCase = true)
        }
    }

    /**
     * Check if phone number belongs to admin
     */
    private fun isAdminPhone(phoneNumber: String): Boolean {
        // Add specific admin phone numbers here if needed
        val adminPhones = listOf("+40123456789") // Example admin phone
        return adminPhones.contains(phoneNumber)
    }

    /**
     * Create or update user from Firebase authentication data
     */
    fun createOrUpdateUserFromFirebase(
        firebaseUid: String,
        email: Email?,
        phoneNumber: String?,
        name: String,
        existingUser: User?
    ): User {
        return if (existingUser != null) {
            // Update existing user with latest Firebase data
            existingUser.updateInfo(
                name = name,
                email = email,
                phoneNumber = phoneNumber
            )
        } else {
            // Create new user
            val role = determineUserRole(email, phoneNumber, null)
            User.createFromFirebase(
                firebaseUid = firebaseUid,
                email = email,
                phoneNumber = phoneNumber,
                name = name,
                role = role
            )
        }
    }
}

/**
 * Result of user authentication validation
 */
sealed class AuthenticationValidationResult {
    object Valid : AuthenticationValidationResult()
    object UserInactive : AuthenticationValidationResult()
    data class InvalidUserData(val reason: String) : AuthenticationValidationResult()

    fun isValid(): Boolean = this is Valid
    fun getErrorMessage(): String? = when (this) {
        is Valid -> null
        is UserInactive -> "User account is inactive"
        is InvalidUserData -> reason
    }
}
