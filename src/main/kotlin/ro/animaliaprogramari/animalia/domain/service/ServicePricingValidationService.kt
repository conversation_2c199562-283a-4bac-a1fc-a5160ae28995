package ro.animaliaprogramari.animalia.domain.service

import org.springframework.stereotype.Service
import ro.animaliaprogramari.animalia.domain.model.Money
import ro.animaliaprogramari.animalia.domain.model.Duration

/**
 * Service for validating service pricing configurations
 */
@Service
class ServicePricingValidationService {

    /**
     * Validate service pricing configuration
     */
    fun validatePricingConfiguration(
        basePrice: Money,
        baseDuration: Duration,
        sizePrices: Map<String, Money> = emptyMap(),
        sizeDurations: Map<String, Duration> = emptyMap(),
        minPrice: Money? = null,
        maxPrice: Money? = null,
        sizeMinPrices: Map<String, Money>? = null,
        sizeMaxPrices: Map<String, Money>? = null
    ): PricingValidationResult {
        val errors = mutableListOf<String>()

        // Validate variable pricing consistency
        if (sizePrices.isNotEmpty() || sizeDurations.isNotEmpty()) {
            if (sizePrices.keys != sizeDurations.keys) {
                errors.add("Size prices and durations must have the same keys")
            }
            
            if (sizePrices.isEmpty()) {
                errors.add("Size prices cannot be empty when size durations are provided")
            }
            
            if (sizeDurations.isEmpty()) {
                errors.add("Size durations cannot be empty when size prices are provided")
            }
        }

        // Validate min/max pricing
        if (minPrice != null && maxPrice != null) {
            if (minPrice > maxPrice) {
                errors.add("Min price cannot be greater than max price")
            }
        }

        // Validate size-based min/max pricing
        if (sizeMinPrices != null && sizeMaxPrices != null) {
            if (sizeMinPrices.keys != sizeMaxPrices.keys) {
                errors.add("Size min prices and max prices must have the same keys")
            }
            
            // Validate each size's min/max relationship
            for (size in sizeMinPrices.keys) {
                val min = sizeMinPrices[size]!!
                val max = sizeMaxPrices[size]!!
                if (min > max) {
                    errors.add("Min price for size $size cannot be greater than max price")
                }
            }
        }

        // Validate size consistency across all pricing models
        val allSizes = mutableSetOf<String>()
        allSizes.addAll(sizePrices.keys)
        allSizes.addAll(sizeDurations.keys)
        sizeMinPrices?.let { allSizes.addAll(it.keys) }
        sizeMaxPrices?.let { allSizes.addAll(it.keys) }

        if (allSizes.isNotEmpty()) {
            // If any size-based pricing is used, validate consistency
            if (sizePrices.isNotEmpty() && sizePrices.keys != allSizes) {
                errors.add("All size-based pricing configurations must use the same sizes")
            }
            
            if (sizeDurations.isNotEmpty() && sizeDurations.keys != allSizes) {
                errors.add("All size-based pricing configurations must use the same sizes")
            }
            
            if (sizeMinPrices != null && sizeMinPrices.keys != allSizes) {
                errors.add("All size-based pricing configurations must use the same sizes")
            }
            
            if (sizeMaxPrices != null && sizeMaxPrices.keys != allSizes) {
                errors.add("All size-based pricing configurations must use the same sizes")
            }
        }

        // Validate that base price is within min/max range if specified
        if (minPrice != null && basePrice < minPrice) {
            errors.add("Base price cannot be less than min price")
        }
        
        if (maxPrice != null && basePrice > maxPrice) {
            errors.add("Base price cannot be greater than max price")
        }

        return PricingValidationResult(
            isValid = errors.isEmpty(),
            errors = errors
        )
    }

    /**
     * Validate supported pet sizes
     */
    fun validatePetSizes(sizes: Set<String>): PricingValidationResult {
        val supportedSizes = setOf("S", "M", "L", "XS", "XL")
        val errors = mutableListOf<String>()
        
        for (size in sizes) {
            if (size.uppercase() !in supportedSizes) {
                errors.add("Unsupported pet size: $size. Supported sizes are: ${supportedSizes.joinToString()}")
            }
        }
        
        return PricingValidationResult(
            isValid = errors.isEmpty(),
            errors = errors
        )
    }
}

/**
 * Result of pricing validation operation
 */
data class PricingValidationResult(
    val isValid: Boolean,
    val errors: List<String>
) {
    fun getErrorMessage(): String = errors.joinToString("; ")
}
