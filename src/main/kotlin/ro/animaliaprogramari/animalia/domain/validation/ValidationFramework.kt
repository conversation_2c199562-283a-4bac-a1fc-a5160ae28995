package ro.animaliaprogramari.animalia.domain.validation

import org.springframework.stereotype.Service
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.CreatePetRequest
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.CreateSalonRequest
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.UpdateUserNameRequest
import ro.animaliaprogramari.animalia.application.command.ScheduleAppointmentCommand
import ro.animaliaprogramari.animalia.config.BusinessRulesConfig
import ro.animaliaprogramari.animalia.domain.exception.BusinessRuleViolationException
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.domain.validation.ValidationResult.Companion.success
import java.math.BigDecimal
import java.time.Duration
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime

/**
 * Centralized validation framework to eliminate code duplication
 * Replaces scattered validation logic across controllers and domain models
 */

// Core validation interfaces
interface Validator<T> {
    fun validate(input: T): ValidationResult
}

data class ValidationResult private constructor(
    val isValid: Boolean,
    val errors: List<String> = emptyList()
) {
    companion object {
        fun success() = ValidationResult(true)
        fun error(message: String) = ValidationResult(false, listOf(message))
        fun errors(messages: List<String>) = ValidationResult(false, messages)
    }
    
    fun and(other: ValidationResult): ValidationResult {
        return when {
            isValid && other.isValid -> success()
            else -> errors(errors + other.errors)
        }
    }
    
    fun throwIfInvalid() {
        if (!isValid) {
            throw BusinessRuleViolationException(errors.joinToString("; "))
        }
    }
    
    fun getErrorMessage(): String? = if (isValid) null else errors.joinToString("; ")
}

// Common validation rules
object ValidationRules {
    
    fun validateName(name: String?, entityType: String, required: Boolean = true): ValidationResult {
        return when {
            name == null && required -> error("${entityType} name is required")
            name == null && !required -> success()
            name!!.isBlank() && required -> error("${entityType} name cannot be blank")
            name.length > 255 -> error("${entityType} name cannot exceed 255 characters")
            else -> success()
        }
    }
    
    fun validateEmail(email: String?): ValidationResult {
        if (email == null) return success()
        return try {
            Email.of(email)
            success()
        } catch (e: Exception) {
            error("Invalid email format")
        }
    }
    
    fun validatePhoneNumber(phone: String?): ValidationResult {
        if (phone == null) return success()
        return try {
            PhoneNumber.of(phone)
            success()
        } catch (e: Exception) {
            error("Invalid phone number format")
        }
    }
    
    fun validateText(text: String?, fieldName: String, maxLength: Int, required: Boolean = false): ValidationResult {
        return when {
            text == null && required -> error("${fieldName} is required")
            text == null && !required -> success()
            text!!.length > maxLength -> error("${fieldName} cannot exceed ${maxLength} characters")
            else -> success()
        }
    }
    
    fun validatePositiveAmount(amount: BigDecimal?, fieldName: String): ValidationResult {
        return when {
            amount == null -> error("${fieldName} is required")
            amount <= BigDecimal.ZERO -> error("${fieldName} must be positive")
            else -> success()
        }
    }
    
    fun validateTimeRange(startTime: LocalTime?, endTime: LocalTime?): ValidationResult {
        return when {
            startTime == null -> error("Start time is required")
            endTime == null -> error("End time is required")
            !startTime.isBefore(endTime) -> error("Start time must be before end time")
            else -> success()
        }
    }
    
    fun validateDateRange(startDate: LocalDate?, endDate: LocalDate?): ValidationResult {
        return when {
            startDate == null -> error("Start date is required")
            endDate == null -> error("End date is required")
            startDate.isAfter(endDate) -> error("Start date must be before or equal to end date")
            else -> success()
        }
    }
    
    fun validateFutureDate(date: LocalDate?, fieldName: String): ValidationResult {
        return when {
            date == null -> error("${fieldName} is required")
            date.isBefore(LocalDate.now()) -> error("${fieldName} must be in the future")
            else -> success()
        }
    }
    
    fun validateAge(age: Int?): ValidationResult {
        return when {
            age == null -> success()
            age < 0 -> error("Age must be positive")
            age > 30 -> error("Age seems unrealistic for a pet")
            else -> success()
        }
    }
    
    fun validateWeight(weight: BigDecimal?): ValidationResult {
        return when {
            weight == null -> success()
            weight <= BigDecimal.ZERO -> error("Weight must be positive")
            weight > BigDecimal(200) -> error("Weight seems unrealistic for a pet")
            else -> success()
        }
    }
}

// Validation service for use cases
@Service
class ValidationService(
    private val config: BusinessRulesConfig
) {

    fun validateCreateSalon(request: CreateSalonRequest): ValidationResult {
        return ValidationRules.validateName(request.name, "Salon")
            .and(ValidationRules.validateText(request.address, "Address", 1000))
            .and(ValidationRules.validateText(request.city, "City", 255))
            .and(ValidationRules.validatePhoneNumber(request.phone))
            .and(ValidationRules.validateEmail(request.email))
    }

    fun validateCreatePet(request: CreatePetRequest): ValidationResult {
        return ValidationRules.validateName(request.name, "Pet")
            .and(ValidationRules.validateText(request.breed, "Breed", 255))
            .and(ValidationRules.validateText(request.color, "Color", 100))
            .and(ValidationRules.validateText(request.notes, "Notes", 2000))
            .and(ValidationRules.validateText(request.medicalConditions, "Medical conditions", 2000))
            .and(ValidationRules.validateAge(request.age))
            .and(ValidationRules.validateWeight(request.weight))
    }

    fun validateScheduleAppointment(command: ScheduleAppointmentCommand): ValidationResult {
        return validateBasicFields(command)
            .and(validateTimeSlot(command))
            .and(validateAdvanceBooking(command))
            .and(validateServiceCount(command))
    }

    fun validateUpdateUserName(request: UpdateUserNameRequest): ValidationResult {
        return ValidationRules.validateName(request.name, "User", required = true)
    }

    private fun validateBasicFields(input: ScheduleAppointmentCommand): ValidationResult {
        return ValidationRules.validateFutureDate(input.appointmentDate, "Appointment date")
            .and(ValidationRules.validateTimeRange(input.startTime, input.endTime))
            .and(ValidationRules.validateText(input.notes, "Notes", config.validation.maxNotesLength))
    }

    private fun validateTimeSlot(input: ScheduleAppointmentCommand): ValidationResult {
        val duration = Duration.between(input.startTime, input.endTime)
        return when {
            duration.toMinutes() < 15 -> ValidationResult.error("Appointment must be at least 15 minutes")
            duration.toMinutes() > 480 -> ValidationResult.error("Appointment cannot exceed 8 hours")
            else -> success()
        }
    }

    private fun validateAdvanceBooking(input: ScheduleAppointmentCommand): ValidationResult {
        val appointmentDateTime = LocalDateTime.of(input.appointmentDate, input.startTime)
        val now = LocalDateTime.now()
        val hoursInAdvance = Duration.between(now, appointmentDateTime).toHours()

        return when {
            hoursInAdvance < config.appointment.minimumAdvanceHours ->
                ValidationResult.error("Appointments must be scheduled at least ${config.appointment.minimumAdvanceHours} hours in advance")
            hoursInAdvance > config.appointment.maximumAdvanceDays * 24 ->
                ValidationResult.error("Appointments cannot be scheduled more than ${config.appointment.maximumAdvanceDays} days in advance")
            else -> success()
        }
    }

    private fun validateServiceCount(input: ScheduleAppointmentCommand): ValidationResult {
        return when {
            input.serviceIds.isEmpty() -> ValidationResult.error("At least one service must be selected")
            input.serviceIds.size > config.appointment.maxServicesPerAppointment ->
                ValidationResult.error("Cannot select more than ${config.appointment.maxServicesPerAppointment} services")
            else -> success()
        }
    }
}

// Extension functions for easier usage
fun <T> T.validateWith(validator: Validator<T>): ValidationResult {
    return validator.validate(this)
}

fun ValidationResult.orThrow(): ValidationResult {
    throwIfInvalid()
    return this
}
