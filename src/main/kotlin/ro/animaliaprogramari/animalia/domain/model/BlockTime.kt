package ro.animaliaprogramari.animalia.domain.model

import java.time.LocalDateTime
import java.time.ZonedDateTime
import java.time.temporal.ChronoUnit

/**
 * Domain entity representing a blocked time slot
 * Prevents appointments from being scheduled during specific periods
 */
data class BlockTime(
    val id: BlockTimeId,
    val salonId: SalonId,
    val startTime: ZonedDateTime,
    val endTime: ZonedDateTime,
    val reason: BlockReason,
    val customReason: String? = null,
    val staffIds: Set<StaffId>,
    val createdBy: UserId,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedBy: UserId? = null,
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    val isRecurring: Boolean = false,
    val recurrencePattern: RecurrencePattern? = null,
    val notes: String? = null,
    val status: BlockTimeStatus = BlockTimeStatus.ACTIVE
) {
    
    init {
        require(endTime.isAfter(startTime)) { "End time must be after start time" }
        require(staffIds.isNotEmpty()) { "At least one staff member must be specified" }
        require(getDurationMinutes() >= 15) { "Block duration must be at least 15 minutes" }
        require(getDurationMinutes() <= 720) { "Block duration cannot exceed 12 hours" }
        require(reason != BlockReason.ALTELE || !customReason.isNullOrBlank()) { 
            "Custom reason is required when reason is 'Altele'" 
        }
        if (isRecurring) {
            require(recurrencePattern != null) { "Recurrence pattern is required for recurring blocks" }
        }
    }

    /**
     * Get duration in minutes
     */
    fun getDurationMinutes(): Long {
        return ChronoUnit.MINUTES.between(startTime, endTime)
    }

    /**
     * Check if this block affects a specific staff member
     */
    fun affectsStaff(staffId: StaffId): Boolean {
        return staffIds.contains(staffId)
    }

    /**
     * Check if this block overlaps with a time period
     */
    fun overlapsWith(start: ZonedDateTime, end: ZonedDateTime): Boolean {
        return startTime.isBefore(end) && endTime.isAfter(start)
    }

    /**
     * Check if this block is active
     */
    fun isActive(): Boolean {
        return status == BlockTimeStatus.ACTIVE
    }

    /**
     * Cancel this block
     */
    fun cancel(cancelledBy: UserId, reason: String? = null): BlockTime {
        return copy(
            status = BlockTimeStatus.CANCELLED,
            updatedBy = cancelledBy,
            updatedAt = LocalDateTime.now(),
            notes = if (reason != null) "${notes ?: ""}\nCancelled: $reason".trim() else notes
        )
    }

    /**
     * Update this block
     */
    fun update(
        startTime: ZonedDateTime? = null,
        endTime: ZonedDateTime? = null,
        reason: BlockReason? = null,
        customReason: String? = null,
        staffIds: Set<StaffId>? = null,
        notes: String? = null,
        updatedBy: UserId
    ): BlockTime {
        return copy(
            startTime = startTime ?: this.startTime,
            endTime = endTime ?: this.endTime,
            reason = reason ?: this.reason,
            customReason = customReason ?: this.customReason,
            staffIds = staffIds ?: this.staffIds,
            notes = notes ?: this.notes,
            updatedBy = updatedBy,
            updatedAt = LocalDateTime.now()
        )
    }

    /**
     * Check if block is in the past
     */
    fun isInPast(): Boolean {
        return endTime.isBefore(ZonedDateTime.now())
    }

    /**
     * Get display reason (custom reason if available, otherwise standard reason)
     */
    fun getDisplayReason(): String {
        return if (reason == BlockReason.ALTELE && !customReason.isNullOrBlank()) {
            customReason
        } else {
            reason.displayName
        }
    }
}

/**
 * Value object for block time ID
 */
@JvmInline
value class BlockTimeId(val value: String) {
    companion object {
        fun of(value: String): BlockTimeId {
            require(value.isNotBlank()) { "Block time ID cannot be blank" }
            return BlockTimeId(value)
        }
        
        fun generate(): BlockTimeId {
            return BlockTimeId("block-${java.util.UUID.randomUUID()}")
        }
    }
}

/**
 * Enumeration of block time statuses
 */
enum class BlockTimeStatus(val displayName: String) {
    ACTIVE("Active"),
    CANCELLED("Cancelled"),
    EXPIRED("Expired");
    
    companion object {
        fun fromString(value: String): BlockTimeStatus {
            return values().find { it.name.equals(value, ignoreCase = true) }
                ?: throw IllegalArgumentException("Invalid block time status: $value")
        }
    }
}

/**
 * Enumeration of block reasons
 */
enum class BlockReason(val displayName: String) {
    PAUZA("Pauză"),
    INTALNIRE("Întâlnire"),
    CONCEDIU("Concediu"),
    PERSONAL("Personal"),
    TRAINING("Training"),
    ALTELE("Altele");
    
    companion object {
        fun fromString(value: String): BlockReason {
            return values().find { it.displayName == value || it.name.equals(value, ignoreCase = true) }
                ?: throw IllegalArgumentException("Invalid block reason: $value")
        }
    }
}
