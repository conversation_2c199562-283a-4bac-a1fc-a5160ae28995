package ro.animaliaprogramari.animalia.domain.model

import java.time.LocalDateTime
import java.time.LocalTime

/**
 * Domain entity representing notification settings for a salon
 * Pure domain model with no infrastructure dependencies
 */
data class NotificationSettings(
    val salonId: SalonId,
    val pushNotificationsEnabled: Boolean = true,
    val soundPreference: SoundPreference = SoundPreference.DEFAULT,
    val vibrationEnabled: Boolean = true,
    val doNotDisturb: DoNotDisturbSettings = DoNotDisturbSettings(),
    val notificationRules: NotificationRules = NotificationRules(),
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now()
) {

    /**
     * Check if currently in Do Not Disturb period
     */
    fun isInDoNotDisturbPeriod(currentTime: LocalTime = LocalTime.now()): Boolean {
        if (!doNotDisturb.enabled) {
            return false
        }

        val startTime = doNotDisturb.startTime
        val endTime = doNotDisturb.endTime

        return if (startTime.isBefore(endTime)) {
            // Same day period (e.g., 14:00 to 18:00)
            currentTime.isAfter(startTime) && currentTime.isBefore(endTime)
        } else {
            // Overnight period (e.g., 22:00 to 08:00)
            currentTime.isAfter(startTime) || currentTime.isBefore(endTime)
        }
    }

    /**
     * Check if a notification should be sent based on type and current settings
     */
    fun shouldSendNotification(
        notificationType: NotificationType,
        priority: NotificationPriority = NotificationPriority.NORMAL,
        currentTime: LocalTime = LocalTime.now()
    ): Boolean {
        // Check if push notifications are enabled
        if (!pushNotificationsEnabled) {
            return false
        }

        // Check if notification type is enabled
        if (!notificationRules.isNotificationTypeEnabled(notificationType)) {
            return false
        }

        // Check Do Not Disturb
        if (isInDoNotDisturbPeriod(currentTime)) {
            // Only allow critical notifications if configured
            return priority == NotificationPriority.CRITICAL && doNotDisturb.allowCritical
        }

        return true
    }

    /**
     * Update notification settings
     */
    fun updateSettings(
        pushNotificationsEnabled: Boolean? = null,
        soundPreference: SoundPreference? = null,
        vibrationEnabled: Boolean? = null,
        doNotDisturb: DoNotDisturbSettings? = null,
        notificationRules: NotificationRules? = null
    ): NotificationSettings {
        return copy(
            pushNotificationsEnabled = pushNotificationsEnabled ?: this.pushNotificationsEnabled,
            soundPreference = soundPreference ?: this.soundPreference,
            vibrationEnabled = vibrationEnabled ?: this.vibrationEnabled,
            doNotDisturb = doNotDisturb ?: this.doNotDisturb,
            notificationRules = notificationRules ?: this.notificationRules,
            updatedAt = LocalDateTime.now()
        )
    }

    companion object {
        /**
         * Create default notification settings for a salon
         */
        fun createDefault(salonId: SalonId): NotificationSettings {
            return NotificationSettings(
                salonId = salonId,
                pushNotificationsEnabled = true,
                soundPreference = SoundPreference.DEFAULT,
                vibrationEnabled = true,
                doNotDisturb = DoNotDisturbSettings(),
                notificationRules = NotificationRules()
            )
        }
    }
}

/**
 * Do Not Disturb settings
 */
data class DoNotDisturbSettings(
    val enabled: Boolean = false,
    val startTime: LocalTime = LocalTime.of(22, 0),
    val endTime: LocalTime = LocalTime.of(8, 0),
    val allowCritical: Boolean = true
) {
    init {
        require(startTime != endTime) { "Start time and end time cannot be the same" }
    }
}

/**
 * Notification rules configuration
 */
data class NotificationRules(
    val newAppointments: Boolean = true,
    val appointmentCancellations: Boolean = true,
    val paymentConfirmations: Boolean = true,
    val teamMemberUpdates: Boolean = true,
    val systemMaintenanceAlerts: Boolean = true,
    val defaultPriority: NotificationPriority = NotificationPriority.NORMAL
) {
    
    /**
     * Check if a specific notification type is enabled
     */
    fun isNotificationTypeEnabled(type: NotificationType): Boolean {
        return when (type) {
            NotificationType.NEW_APPOINTMENT -> newAppointments
            NotificationType.APPOINTMENT_CANCELLATION -> appointmentCancellations
            NotificationType.PAYMENT_CONFIRMATION -> paymentConfirmations
            NotificationType.TEAM_MEMBER_UPDATE -> teamMemberUpdates
            NotificationType.SYSTEM_MAINTENANCE -> systemMaintenanceAlerts
        }
    }
}

/**
 * Sound preference options
 */
enum class SoundPreference(val displayName: String) {
    DEFAULT("default"),
    SILENT("silent"),
    CUSTOM1("custom1"),
    CUSTOM2("custom2"),
    CUSTOM3("custom3");

    companion object {
        fun fromString(value: String): SoundPreference {
            return entries.find { it.displayName.equals(value, ignoreCase = true) }
                ?: throw IllegalArgumentException("Unknown sound preference: $value")
        }
    }
}

/**
 * Notification types
 */
enum class NotificationType {
    NEW_APPOINTMENT,
    APPOINTMENT_CANCELLATION,
    PAYMENT_CONFIRMATION,
    TEAM_MEMBER_UPDATE,
    SYSTEM_MAINTENANCE
}

/**
 * Notification priority levels
 */
enum class NotificationPriority {
    NORMAL,
    CRITICAL;

    companion object {
        fun fromString(value: String): NotificationPriority {
            return values().find { it.name.equals(value, ignoreCase = true) }
                ?: throw IllegalArgumentException("Unknown notification priority: $value")
        }
    }
}
