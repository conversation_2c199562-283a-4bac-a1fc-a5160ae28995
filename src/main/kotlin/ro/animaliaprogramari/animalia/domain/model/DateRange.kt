package ro.animaliaprogramari.animalia.domain.model

import java.time.LocalDate

/**
 * Date range value object
 */
data class DateRange(
    val startDate: LocalDate,
    val endDate: LocalDate
) {
    init {
        require(!endDate.isBefore(startDate)) { "End date cannot be before start date" }
    }

    fun contains(date: LocalDate): Boolean {
        return !date.isBefore(startDate) && !date.isAfter(endDate)
    }

    fun daysBetween(): Long {
        return java.time.temporal.ChronoUnit.DAYS.between(startDate, endDate) + 1
    }

    fun getWorkingDays(): Int {
        // Simplified - excludes weekends
        return startDate.datesUntil(endDate.plusDays(1))
            .filter { it.dayOfWeek.value <= 5 }
            .count()
            .toInt()
    }

    companion object {
        fun of(startDate: LocalDate, endDate: LocalDate): DateRange {
            return DateRange(startDate, endDate)
        }

        fun thisWeek(): DateRange {
            val today = LocalDate.now()
            val startOfWeek = today.minusDays(today.dayOfWeek.value - 1L)
            val endOfWeek = startOfWeek.plusDays(6)
            return DateRange(startOfWeek, endOfWeek)
        }

        fun thisMonth(): DateRange {
            val today = LocalDate.now()
            val startOfMonth = today.withDayOfMonth(1)
            val endOfMonth = today.withDayOfMonth(today.lengthOfMonth())
            return DateRange(startOfMonth, endOfMonth)
        }
    }
}
