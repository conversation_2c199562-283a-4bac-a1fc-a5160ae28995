package ro.animaliaprogramari.animalia.domain.model

import java.math.BigDecimal
import java.time.LocalDateTime

/**
 * Value object representing client statistics
 * Contains aggregated data about a client's activity in a salon
 */
data class ClientStatistics(
    val clientId: ClientId,
    val salonId: SalonId,
    val totalAppointments: Int,
    val completedAppointments: Int,
    val cancelledAppointments: Int,
    val noShowAppointments: Int,
    val upcomingAppointments: Int,
    val lastVisitDate: LocalDateTime?,
    val totalRevenue: BigDecimal,
    val averageAppointmentValue: BigDecimal,
    val totalPets: Int,
    val loyaltyScore: BigDecimal, // 0.0 to 100.0
    val favoriteServices: List<String> = emptyList(),
    val averageRating: BigDecimal? = null,
    val totalReviews: Int = 0,
    val activeSubscriptions: Int = 0,
    val calculatedAt: LocalDateTime = LocalDateTime.now()
) {

    init {
        require(totalAppointments >= 0) { "Total appointments cannot be negative" }
        require(completedAppointments >= 0) { "Completed appointments cannot be negative" }
        require(cancelledAppointments >= 0) { "Cancelled appointments cannot be negative" }
        require(noShowAppointments >= 0) { "No-show appointments cannot be negative" }
        require(upcomingAppointments >= 0) { "Upcoming appointments cannot be negative" }
        require(totalRevenue >= BigDecimal.ZERO) { "Total revenue cannot be negative" }
        require(averageAppointmentValue >= BigDecimal.ZERO) { "Average appointment value cannot be negative" }
        require(totalPets >= 0) { "Total pets cannot be negative" }
        require(loyaltyScore >= BigDecimal.ZERO && loyaltyScore <= BigDecimal("100.0")) { 
            "Loyalty score must be between 0.0 and 100.0" 
        }
        require(totalReviews >= 0) { "Total reviews cannot be negative" }
        require(activeSubscriptions >= 0) { "Active subscriptions cannot be negative" }
        averageRating?.let { 
            require(it >= BigDecimal.ZERO && it <= BigDecimal("5.0")) { 
                "Average rating must be between 0.0 and 5.0" 
            } 
        }
    }

    /**
     * Calculate completion rate as percentage
     */
    fun completionRate(): BigDecimal {
        return if (totalAppointments > 0) {
            BigDecimal(completedAppointments)
                .divide(BigDecimal(totalAppointments), 4, java.math.RoundingMode.HALF_UP)
                .multiply(BigDecimal("100"))
        } else {
            BigDecimal.ZERO
        }
    }

    /**
     * Calculate cancellation rate as percentage
     */
    fun cancellationRate(): BigDecimal {
        return if (totalAppointments > 0) {
            BigDecimal(cancelledAppointments)
                .divide(BigDecimal(totalAppointments), 4, java.math.RoundingMode.HALF_UP)
                .multiply(BigDecimal("100"))
        } else {
            BigDecimal.ZERO
        }
    }

    /**
     * Calculate no-show rate as percentage
     */
    fun noShowRate(): BigDecimal {
        return if (totalAppointments > 0) {
            BigDecimal(noShowAppointments)
                .divide(BigDecimal(totalAppointments), 4, java.math.RoundingMode.HALF_UP)
                .multiply(BigDecimal("100"))
        } else {
            BigDecimal.ZERO
        }
    }

    /**
     * Check if client is a VIP (high value customer)
     */
    fun isVip(): Boolean {
        return loyaltyScore >= BigDecimal("80.0") || 
               totalRevenue >= BigDecimal("1000.0") ||
               completedAppointments >= 20
    }

    /**
     * Get client tier based on activity
     */
    fun getClientTier(): ClientTier {
        return when {
            isVip() -> ClientTier.VIP
            completedAppointments >= 10 -> ClientTier.GOLD
            completedAppointments >= 5 -> ClientTier.SILVER
            else -> ClientTier.BRONZE
        }
    }

    companion object {
        /**
         * Create empty statistics for a new client
         */
        fun empty(clientId: ClientId, salonId: SalonId): ClientStatistics {
            return ClientStatistics(
                clientId = clientId,
                salonId = salonId,
                totalAppointments = 0,
                completedAppointments = 0,
                cancelledAppointments = 0,
                noShowAppointments = 0,
                upcomingAppointments = 0,
                lastVisitDate = null,
                totalRevenue = BigDecimal.ZERO,
                averageAppointmentValue = BigDecimal.ZERO,
                totalPets = 0,
                loyaltyScore = BigDecimal.ZERO
            )
        }
    }
}

/**
 * Enum representing client tiers
 */
enum class ClientTier {
    BRONZE,
    SILVER,
    GOLD,
    VIP
}
