package ro.animaliaprogramari.animalia.domain.model

import java.time.LocalTime
import java.time.temporal.TemporalAmount

/**
 * Value object representing duration in minutes
 */
@JvmInline
value class Duration(val minutes: Int) {
    
    init {
        require(minutes >= 0) { "Duration must be positive" }
    }
    
    fun add(other: Duration): Duration = Duration(minutes + other.minutes)

    fun subtract(other: Duration): Duration = Duration(minutes - other.minutes)

    fun toHours(): Double = minutes / 60.0

    fun toMinutes(): Int = minutes;




    fun toHoursAndMinutes(): Pair<Int, Int> = Pair(minutes / 60, minutes % 60)
    
    companion object {
        fun ofMinutes(minutes: Int): Duration = Duration(minutes)
        
        fun ofHours(hours: Int): Duration = Duration(hours * 60)
        
        fun ofHoursAndMinutes(hours: Int, minutes: Int): Duration = Duration(hours * 60 + minutes)

        fun between(startTime: LocalTime, endTime: LocalTime): Duration =
            ofMinutes(java.time.Duration.between(startTime, endTime).toMinutes().toInt())
    }
    
    override fun toString(): String {
        val (h, m) = toHoursAndMinutes()
        return when {
            h == 0 -> "${m}m"
            m == 0 -> "${h}h"
            else -> "${h}h ${m}m"
        }
    }

    fun toJavaDuration(): TemporalAmount? {
        return java.time.Duration.ofMinutes(minutes.toLong())
    }
}
