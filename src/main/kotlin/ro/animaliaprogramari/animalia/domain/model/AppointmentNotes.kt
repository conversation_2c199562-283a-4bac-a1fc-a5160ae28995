package ro.animaliaprogramari.animalia.domain.model

import java.time.LocalDateTime

/**
 * Value object representing appointment notes
 */
data class AppointmentNotes(
    val initialNotes: String?,
    val cancellationNotes: String? = null,
    val completionNotes: String? = null,
    val additionalNotes: List<NoteEntry> = emptyList()
) {
    fun addCancellationNote(note: String): AppointmentNotes {
        return copy(cancellationNotes = note)
    }
    
    fun addCompletionNote(note: String): AppointmentNotes {
        return copy(completionNotes = note)
    }
    
    fun addNote(note: String): AppointmentNotes {
        val newNote = NoteEntry(note, LocalDateTime.now())
        return copy(additionalNotes = additionalNotes + newNote)
    }
    
    companion object {
        fun of(initialNotes: String?): AppointmentNotes {
            return AppointmentNotes(initialNotes)
        }
    }
}

data class NoteEntry(
    val content: String,
    val timestamp: LocalDateTime
) {
    init {
        require(content.length <= 2000) { "Notes cannot exceed 2000 characters" }
    }
}
