package ro.animaliaprogramari.animalia.domain.model

import java.time.LocalDateTime

/**
 * Domain entity representing a salon invitation
 * Pure domain model with no infrastructure dependencies
 */
data class SalonInvitation(
    val id: InvitationId,
    val salonId: SalonId,
    val inviterUserId: UserId,
    val invitedUserPhone: String,
    val proposedRole: StaffRole,
    val proposedPermissions: StaffPermissions,
    val status: InvitationStatus,
    val message: String?,
    val invitedAt: LocalDateTime = LocalDateTime.now(),
    val respondedAt: LocalDateTime? = null,
    val expiresAt: LocalDateTime = LocalDateTime.now().plusDays(7), // 7 days expiry
    val resendCount: Int = 0, // Track how many times invitation was resent
    val lastResendAt: LocalDateTime? = null, // Timestamp of last resend
    val cancelledAt: LocalDateTime? = null, // Timestamp when cancelled
    val cancelledBy: UserId? = null, // User who cancelled the invitation
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now()
) {

    init {
        require(invitedUserPhone.isNotBlank()) { "Invited user phone cannot be blank" }
        require(expiresAt.isAfter(invitedAt)) { "Expiry date must be after invitation date" }
        message?.let { require(it.length <= 500) { "Message cannot exceed 500 characters" } }

        // Validate permissions for role
        require(proposedPermissions.isValidForRole(proposedRole)) {
            "Proposed permissions are not valid for the staff role: $proposedRole"
        }
    }

    /**
     * Accept the invitation
     */
    fun accept(): SalonInvitation {
        require(status == InvitationStatus.PENDING) { "Only pending invitations can be accepted" }
        require(!isExpired()) { "Cannot accept expired invitation" }

        return copy(
            status = InvitationStatus.ACCEPTED,
            respondedAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now()
        )
    }

    /**
     * Decline the invitation
     */
    fun decline(): SalonInvitation {
        require(status == InvitationStatus.PENDING) { "Only pending invitations can be declined" }

        return copy(
            status = InvitationStatus.DECLINED,
            respondedAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now()
        )
    }

    /**
     * Cancel the invitation (by inviter)
     */
    fun cancel(cancelledBy: UserId? = null): SalonInvitation {
        require(status == InvitationStatus.PENDING) { "Only pending invitations can be cancelled" }

        return copy(
            status = InvitationStatus.CANCELLED,
            cancelledAt = LocalDateTime.now(),
            cancelledBy = cancelledBy,
            updatedAt = LocalDateTime.now()
        )
    }

    /**
     * Resend the invitation with updated tracking
     */
    fun resend(): SalonInvitation {
        require(status == InvitationStatus.PENDING) { "Only pending invitations can be resent" }

        return copy(
            resendCount = resendCount + 1,
            lastResendAt = LocalDateTime.now(),
            expiresAt = LocalDateTime.now().plusDays(7), // Extend expiry by 7 days
            updatedAt = LocalDateTime.now()
        )
    }

    /**
     * Check if invitation is expired
     */
    fun isExpired(): Boolean = LocalDateTime.now().isAfter(expiresAt)

    /**
     * Check if invitation is still pending and not expired
     */
    fun isActive(): Boolean = status == InvitationStatus.PENDING && !isExpired()

    /**
     * Check if invitation can be responded to
     */
    fun canBeResponded(): Boolean = status == InvitationStatus.PENDING && !isExpired()

    companion object {
        /**
         * Create a new salon invitation
         */
        fun create(
            salonId: SalonId,
            inviterUserId: UserId,
            invitedUserPhone: String,
            proposedRole: StaffRole,
            proposedPermissions: StaffPermissions,
            message: String? = null,
            expiryDays: Long = 7
        ): SalonInvitation {
            return SalonInvitation(
                id = InvitationId.generate(),
                salonId = salonId,
                inviterUserId = inviterUserId,
                invitedUserPhone = invitedUserPhone,
                proposedRole = proposedRole,
                proposedPermissions = proposedPermissions,
                status = InvitationStatus.PENDING,
                message = message,
                expiresAt = LocalDateTime.now().plusDays(expiryDays)
            )
        }
    }
}

/**
 * Value object for invitation ID
 */
@JvmInline
value class InvitationId(val value: String) {
    init {
        require(value.isNotBlank()) { "Invitation ID cannot be blank" }
    }

    companion object {
        fun of(value: String): InvitationId = InvitationId(value)
        fun generate(): InvitationId = InvitationId(java.util.UUID.randomUUID().toString())
    }

    override fun toString(): String = value
}
