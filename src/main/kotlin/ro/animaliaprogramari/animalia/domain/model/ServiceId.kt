package ro.animaliaprogramari.animalia.domain.model

import java.util.UUID

/**
 * Value object representing a unique service identifier
 */
@JvmInline
value class ServiceId(val value: String) {
    
    init {
        require(value.isNotBlank()) { "Service ID cannot be blank" }
    }
    
    companion object {
        fun generate(): ServiceId = ServiceId(UUID.randomUUID().toString())
        
        fun of(value: String): ServiceId = ServiceId(value)
    }
    
    override fun toString(): String = value
}
