package ro.animaliaprogramari.animalia.domain.model.notification

import ro.animaliaprogramari.animalia.domain.model.PhoneNumber
import java.time.LocalDateTime

/**
 * Domain entity for SMS rate limiting per phone number
 */
data class SmsRateLimit(
    val id: SmsRateLimitId,
    val phoneNumber: PhoneNumber,
    val sentCount: Int,
    val windowStart: LocalDateTime,
    val windowDurationMinutes: Int,
    val maxSmsPerWindow: Int,
    val lastSentAt: LocalDateTime?,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now()
) {
    
    /**
     * Check if sending SMS is allowed within rate limit
     */
    fun canSendSms(): Boolean {
        val now = LocalDateTime.now()
        
        // Check if current window has expired
        if (isWindowExpired(now)) {
            return true // New window, can send
        }
        
        // Check if within rate limit for current window
        return sentCount < maxSmsPerWindow
    }
    
    /**
     * Record an SMS sent
     */
    fun recordSmsSent(): SmsRateLimit {
        val now = LocalDateTime.now()
        
        return if (isWindowExpired(now)) {
            // Start new window
            copy(
                sentCount = 1,
                windowStart = now,
                lastSentAt = now,
                updatedAt = now
            )
        } else {
            // Increment in current window
            copy(
                sentCount = sentCount + 1,
                lastSentAt = now,
                updatedAt = now
            )
        }
    }
    
    /**
     * Get remaining SMS count for current window
     */
    fun getRemainingCount(): Int {
        val now = LocalDateTime.now()
        
        return if (isWindowExpired(now)) {
            maxSmsPerWindow // New window
        } else {
            maxOf(0, maxSmsPerWindow - sentCount)
        }
    }
    
    /**
     * Get time until next SMS can be sent (if rate limited)
     */
    fun getTimeUntilNextSms(): LocalDateTime? {
        return if (canSendSms()) {
            null // Can send now
        } else {
            windowStart.plusMinutes(windowDurationMinutes.toLong())
        }
    }
    
    /**
     * Check if current window has expired
     */
    private fun isWindowExpired(now: LocalDateTime): Boolean {
        return now.isAfter(windowStart.plusMinutes(windowDurationMinutes.toLong()))
    }
    
    /**
     * Reset rate limit (admin function)
     */
    fun reset(): SmsRateLimit {
        val now = LocalDateTime.now()
        return copy(
            sentCount = 0,
            windowStart = now,
            lastSentAt = null,
            updatedAt = now
        )
    }
    
    companion object {
        /**
         * Create new rate limit for phone number
         */
        fun create(
            phoneNumber: PhoneNumber,
            maxSmsPerWindow: Int = 3,
            windowDurationMinutes: Int = 60
        ): SmsRateLimit {
            val now = LocalDateTime.now()
            return SmsRateLimit(
                id = SmsRateLimitId.generate(),
                phoneNumber = phoneNumber,
                sentCount = 0,
                windowStart = now,
                windowDurationMinutes = windowDurationMinutes,
                maxSmsPerWindow = maxSmsPerWindow,
                lastSentAt = null,
                createdAt = now,
                updatedAt = now
            )
        }
    }
}

/**
 * Value object for SMS rate limit ID
 */
@JvmInline
value class SmsRateLimitId(val value: String) {
    companion object {
        fun generate(): SmsRateLimitId = SmsRateLimitId(java.util.UUID.randomUUID().toString())
        fun of(value: String): SmsRateLimitId = SmsRateLimitId(value)
    }
    
    override fun toString(): String = value
}
