package ro.animaliaprogramari.animalia.domain.model

/**
 * Data class representing staff information within a salon context
 */
data class SalonStaffInfo(
    val staff: Staff,
    val userName: String,
    val userEmail: String?,
    val userPhone: String?
) {
    companion object {
        fun from(staff: Staff, user: User): SalonStaffInfo {
            return SalonStaffInfo(
                staff = staff,
                userName = user.name,
                userEmail = user.email?.value,
                userPhone = user.phoneNumber
            )
        }
    }
}
