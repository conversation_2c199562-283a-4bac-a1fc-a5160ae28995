package ro.animaliaprogramari.animalia.domain.model

import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime

/**
 * Value object representing an appointment schedule
 */
data class AppointmentSchedule(
    val date: LocalDate,
    val timeSlot: TimeSlot
) {
    fun getStartDateTime(): LocalDateTime {
        return LocalDateTime.of(date, timeSlot.startTime)
    }
    
    fun getEndDateTime(): LocalDateTime {
        return LocalDateTime.of(date, timeSlot.endTime)
    }
    
    fun overlaps(other: AppointmentSchedule): Boolean {
        return date == other.date && timeSlot.overlaps(other.timeSlot)
    }
    
    companion object {
        fun of(date: LocalDate, timeSlot: TimeSlot): AppointmentSchedule {
            return AppointmentSchedule(date, timeSlot)
        }
        
        fun of(date: LocalDate, startTime: LocalTime, endTime: LocalTime): AppointmentSchedule {
            return AppointmentSchedule(date, TimeSlot.of(startTime, endTime))
        }
    }
}
