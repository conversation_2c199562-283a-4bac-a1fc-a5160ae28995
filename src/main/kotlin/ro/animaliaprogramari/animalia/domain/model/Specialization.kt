package ro.animaliaprogramari.animalia.domain.model

/**
 * Staff specializations for service matching
 */
enum class Specialization(val displayName: String, val categories: Set<ServiceCategory>) {
    BASIC_GROOMING("Basic Grooming", setOf(ServiceCategory.GROOMING, ServiceCategory.NAIL_CARE)),
    ADVANCED_STYLING("Advanced Styling", setOf(ServiceCategory.GROOMING, ServiceCategory.STYLING)),
    MEDICAL_CARE("Medical Care", setOf(ServiceCategory.SPECIALTY)),
    LARGE_BREEDS("Large Breeds", setOf(ServiceCategory.GROOMING, ServiceCategory.NAIL_CARE)),
    SMALL_BREEDS("Small Breeds", setOf(ServiceCategory.GROOMING, ServiceCategory.NAIL_CARE)),
    NAIL_CARE("Nail Care", setOf(ServiceCategory.NAIL_CARE)),
    DENTAL_CARE("Dental Care", setOf(ServiceCategory.DENTAL_CARE, ServiceCategory.NAIL_CARE));

    fun covers(category: ServiceCategory): Boolean = category in categories
}
