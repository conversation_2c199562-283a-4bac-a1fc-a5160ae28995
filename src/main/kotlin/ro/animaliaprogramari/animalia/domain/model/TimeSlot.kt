package ro.animaliaprogramari.animalia.domain.model

import java.time.Duration
import java.time.LocalTime

/**
 * Value object representing a time slot
 */
data class TimeSlot(
    val startTime: LocalTime,
    val endTime: LocalTime
) {
    init {
        require(!endTime.isBefore(startTime)) { "End time cannot be before start time" }
    }
    
    fun duration(): Duration {
        return Duration.between(startTime, endTime)
    }
    
    fun isValidDuration(): Boolean {
        val minutes = duration().toMinutes()
        return minutes > 0 && minutes <= 240 // Max 4 hours
    }
    
    fun overlaps(other: TimeSlot): Boolean {
        return !(endTime.isBefore(other.startTime) || startTime.isAfter(other.endTime))
    }
    
    companion object {
        fun of(startTime: LocalTime, endTime: LocalTime): TimeSlot {
            return TimeSlot(startTime, endTime)
        }
    }
}