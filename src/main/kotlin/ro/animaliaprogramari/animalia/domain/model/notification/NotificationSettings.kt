package ro.animaliaprogramari.animalia.domain.model.notification

import ro.animaliaprogramari.animalia.domain.model.SalonId
import ro.animaliaprogramari.animalia.domain.model.UserId
import java.time.LocalDateTime

/**
 * Domain entity representing SMS and push notification settings for a salon
 */
data class SmsNotificationSettings(
    val id: NotificationSettingsId,
    val salonId: SalonId,
    val smsSettings: SmsSettings,
    val pushSettings: PushSettings,
    val updatedBy: UserId,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now()
) {
    
    /**
     * Update SMS settings
     */
    fun updateSmsSettings(
        newSmsSettings: SmsSettings,
        updatedBy: UserId
    ): SmsNotificationSettings {
        return copy(
            smsSettings = newSmsSettings,
            updatedBy = updatedBy,
            updatedAt = LocalDateTime.now()
        )
    }
    
    /**
     * Update push notification settings
     */
    fun updatePushSettings(
        newPushSettings: PushSettings,
        updatedBy: UserId
    ): SmsNotificationSettings {
        return copy(
            pushSettings = newPushSettings,
            updatedBy = updatedBy,
            updatedAt = LocalDateTime.now()
        )
    }
    
    companion object {
        /**
         * Create default notification settings for a salon
         */
        fun createDefault(salonId: SalonId, createdBy: UserId): SmsNotificationSettings {
            return SmsNotificationSettings(
                id = NotificationSettingsId.generate(),
                salonId = salonId,
                smsSettings = SmsSettings.default(),
                pushSettings = PushSettings.default(),
                updatedBy = createdBy
            )
        }
    }
}

/**
 * SMS notification settings
 */
data class SmsSettings(
    val enabled: Boolean,
    val appointmentConfirmation: Boolean,
    val appointmentCancellation: Boolean,
    val appointmentCompletion: Boolean,
    val dayBeforeReminder: Boolean,
    val hourBeforeReminder: Boolean,
    val templates: SmsTemplates
) {
    companion object {
        fun default(): SmsSettings {
            return SmsSettings(
                enabled = true,
                appointmentConfirmation = true,
                appointmentCancellation = true,
                appointmentCompletion = true,
                dayBeforeReminder = true,
                hourBeforeReminder = false,
                templates = SmsTemplates.defaultRomanian()
            )
        }
    }
}

/**
 * Push notification settings
 */
data class PushSettings(
    val enabled: Boolean,
    val appointmentAssigned: Boolean,
    val appointmentCancelled: Boolean,
    val appointmentCompleted: Boolean,
    val newAppointmentCreated: Boolean
) {
    companion object {
        fun default(): PushSettings {
            return PushSettings(
                enabled = true,
                appointmentAssigned = true,
                appointmentCancelled = true,
                appointmentCompleted = true,
                newAppointmentCreated = true
            )
        }
    }
}

/**
 * SMS message templates in Romanian
 */
data class SmsTemplates(
    val appointmentConfirmation: String,
    val appointmentCancellation: String,
    val appointmentCompletion: String,
    val dayBeforeReminder: String,
    val hourBeforeReminder: String
) {
    companion object {
        fun defaultRomanian(): SmsTemplates {
            return SmsTemplates(
                appointmentConfirmation = "Bună ziua {clientName}! Programarea pentru {petName} a fost confirmată pe {date} la {time} la {salonName}. Mulțumim!",
                appointmentCancellation = "Bună ziua {clientName}! Programarea pentru {petName} din {date} la {time} a fost anulată. Pentru reprogramare, vă rugăm să ne contactați.",
                appointmentCompletion = "Bună ziua {clientName}! Programarea pentru {petName} a fost finalizată cu succes. Mulțumim că ați ales {salonName}!",
                dayBeforeReminder = "Bună ziua {clientName}! Vă reamintim că mâine la {time} aveți programare pentru {petName} la {salonName}.",
                hourBeforeReminder = "Bună ziua {clientName}! Programarea pentru {petName} este în o oră la {salonName}. Vă așteptăm!"
            )
        }
    }
}

/**
 * Value object for notification settings ID
 */
@JvmInline
value class NotificationSettingsId(val value: String) {
    companion object {
        fun generate(): NotificationSettingsId = NotificationSettingsId(java.util.UUID.randomUUID().toString())
        fun of(value: String): NotificationSettingsId = NotificationSettingsId(value)
    }
    
    override fun toString(): String = value
}
