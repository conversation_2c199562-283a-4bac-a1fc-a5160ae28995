package ro.animaliaprogramari.animalia.domain.model

import java.time.LocalDate
import java.time.LocalTime

/**
 * Represents an item that conflicts with the requested appointment slot
 */
data class AppointmentConflictItem(
    val id: String,
    val staffId: StaffId,
    val staffName: String,
    val type: ConflictItemType,
    val date: LocalDate,
    val startTime: LocalTime,
    val endTime: LocalTime
)

/**
 * Types of conflicting items
 */
enum class ConflictItemType {
    APPOINTMENT, BLOCK_TIME
}

/**
 * Suggested alternative for a scheduling conflict
 */
data class AlternativeSuggestion(
    val type: AlternativeType,
    val priority: Int,
    val suggestion: AlternativeSlot,
    val reason: String,
    val confidence: Double
)

/**
 * Alternative suggestion type hierarchy
 */
enum class AlternativeType {
    TIME_ADJUSTMENT, DAY_ADJUSTMENT, STAFF_ALTERNATIVE
}

/**
 * Alternative slot information
 */
data class AlternativeSlot(
    val date: LocalDate,
    val startTime: LocalTime,
    val endTime: LocalTime,
    val staffId: String,
    val staffName: String
)
