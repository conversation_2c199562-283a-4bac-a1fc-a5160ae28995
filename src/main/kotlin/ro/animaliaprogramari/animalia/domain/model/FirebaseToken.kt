package ro.animaliaprogramari.animalia.domain.model

/**
 * Value object representing a Firebase ID token
 */
@JvmInline
value class FirebaseToken(val value: String) {
    
    init {
        require(value.isNotBlank()) { "Firebase token cannot be blank" }
        require(value.length > 10) { "Firebase token appears to be invalid" }
    }
    
    companion object {
        /**
         * Create Firebase token from string
         */
        fun of(value: String): FirebaseToken = FirebaseToken(value)
    }
}
