package ro.animaliaprogramari.animalia.domain.model

import java.time.DayOfWeek
import java.time.LocalDate
import java.time.ZonedDateTime

/**
 * Value object representing a recurrence pattern for block times
 */
data class RecurrencePattern(
    val type: RecurrenceType,
    val interval: Int,
    val daysOfWeek: Set<DayOfWeek>? = null,
    val dayOfMonth: Int? = null,
    val endDate: ZonedDateTime? = null,
    val occurrences: Int? = null
) {
    
    init {
        require(interval > 0) { "Interval must be positive" }
        require(interval <= 365) { "Interval cannot exceed 365" }
        
        when (type) {
            RecurrenceType.WEEKLY -> {
                require(!daysOfWeek.isNullOrEmpty()) { "Days of week must be specified for weekly recurrence" }
                require(daysOfWeek.size <= 7) { "Cannot specify more than 7 days of week" }
            }
            RecurrenceType.MONTHLY -> {
                require(dayOfMonth != null) { "Day of month must be specified for monthly recurrence" }
                require(dayOfMonth in 1..31) { "Day of month must be between 1 and 31" }
            }
            RecurrenceType.DAILY -> {
                // No additional validation needed for daily recurrence
            }
        }
        
        if (endDate != null && occurrences != null) {
            throw IllegalArgumentException("Cannot specify both end date and number of occurrences")
        }
        
        if (occurrences != null) {
            require(occurrences > 0) { "Number of occurrences must be positive" }
            require(occurrences <= 365) { "Number of occurrences cannot exceed 365" }
        }
    }

    /**
     * Generate the next occurrence date based on the current date
     */
    fun getNextOccurrence(currentDate: ZonedDateTime): ZonedDateTime? {
        if (endDate != null && currentDate.isAfter(endDate)) {
            return null
        }
        
        return when (type) {
            RecurrenceType.DAILY -> currentDate.plusDays(interval.toLong())
            RecurrenceType.WEEKLY -> getNextWeeklyOccurrence(currentDate)
            RecurrenceType.MONTHLY -> getNextMonthlyOccurrence(currentDate)
        }
    }

    /**
     * Generate all occurrences within a date range
     */
    fun generateOccurrences(
        startDate: ZonedDateTime,
        rangeStart: ZonedDateTime,
        rangeEnd: ZonedDateTime
    ): List<ZonedDateTime> {
        val occurrences = mutableListOf<ZonedDateTime>()
        var currentDate = startDate
        var count = 0
        
        while (currentDate.isBefore(rangeEnd) && 
               (endDate == null || currentDate.isBefore(endDate)) &&
               (this.occurrences == null || count < this.occurrences)) {
            
            if (!currentDate.isBefore(rangeStart)) {
                occurrences.add(currentDate)
            }
            
            val nextDate = getNextOccurrence(currentDate)
            if (nextDate == null || nextDate.isEqual(currentDate)) {
                break
            }
            
            currentDate = nextDate
            count++
        }
        
        return occurrences
    }

    /**
     * Check if the pattern is valid for the given start date
     */
    fun isValidForStartDate(startDate: ZonedDateTime): Boolean {
        return when (type) {
            RecurrenceType.WEEKLY -> {
                daysOfWeek?.contains(startDate.dayOfWeek) == true
            }
            RecurrenceType.MONTHLY -> {
                dayOfMonth == startDate.dayOfMonth
            }
            RecurrenceType.DAILY -> true
        }
    }

    private fun getNextWeeklyOccurrence(currentDate: ZonedDateTime): ZonedDateTime? {
        if (daysOfWeek.isNullOrEmpty()) return null
        
        val sortedDays = daysOfWeek.sorted()
        val currentDayOfWeek = currentDate.dayOfWeek
        
        // Find next day in the same week
        val nextDayInWeek = sortedDays.find { it.value > currentDayOfWeek.value }
        
        return if (nextDayInWeek != null) {
            // Next occurrence is in the same week
            val daysToAdd = nextDayInWeek.value - currentDayOfWeek.value
            currentDate.plusDays(daysToAdd.toLong())
        } else {
            // Next occurrence is in the next interval week
            val firstDayOfNextWeek = sortedDays.first()
            val daysToAdd = (7 - currentDayOfWeek.value + firstDayOfNextWeek.value) + (interval - 1) * 7
            currentDate.plusDays(daysToAdd.toLong())
        }
    }

    private fun getNextMonthlyOccurrence(currentDate: ZonedDateTime): ZonedDateTime? {
        if (dayOfMonth == null) return null
        
        var nextDate = currentDate.plusMonths(interval.toLong())
        
        // Adjust to the correct day of month
        return try {
            nextDate.withDayOfMonth(dayOfMonth)
        } catch (e: Exception) {
            // Handle cases where the day doesn't exist in the target month (e.g., Feb 31)
            nextDate.withDayOfMonth(nextDate.toLocalDate().lengthOfMonth())
        }
    }
}

/**
 * Enumeration of recurrence types
 */
enum class RecurrenceType(val displayName: String) {
    DAILY("Daily"),
    WEEKLY("Weekly"),
    MONTHLY("Monthly");
    
    companion object {
        fun fromString(value: String): RecurrenceType {
            return values().find { it.name.equals(value, ignoreCase = true) }
                ?: throw IllegalArgumentException("Invalid recurrence type: $value")
        }
    }
}

/**
 * Conflict information for block time scheduling
 */
data class BlockTimeConflict(
    val staffId: StaffId,
    val staffName: String,
    val conflictType: ConflictType,
    val conflictDetails: ConflictDetails
)

/**
 * Details about a scheduling conflict
 */
data class ConflictDetails(
    val id: String,
    val startTime: ZonedDateTime,
    val endTime: ZonedDateTime,
    val description: String
)

/**
 * Types of scheduling conflicts
 */
enum class ConflictType(val displayName: String) {
    APPOINTMENT("Appointment"),
    BLOCK("Block Time"),
    SCHEDULE("Working Schedule");
    
    companion object {
        fun fromString(value: String): ConflictType {
            return values().find { it.name.equals(value, ignoreCase = true) }
                ?: throw IllegalArgumentException("Invalid conflict type: $value")
        }
    }
}

/**
 * Suggested actions for resolving conflicts
 */
enum class SuggestedAction(val displayName: String) {
    FORCE_BLOCK("Force Block"),
    RESCHEDULE_APPOINTMENTS("Reschedule Appointments"),
    ADJUST_TIME_RANGE("Adjust Time Range"),
    EXCLUDE_STAFF("Exclude Staff");
    
    companion object {
        fun fromString(value: String): SuggestedAction {
            return values().find { it.name.equals(value, ignoreCase = true) }
                ?: throw IllegalArgumentException("Invalid suggested action: $value")
        }
    }
}
