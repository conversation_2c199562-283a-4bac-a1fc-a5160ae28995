package ro.animaliaprogramari.animalia.domain.model

/**
 * Value object representing an authenticated user context
 * Contains the essential information needed for authorization decisions
 */
data class AuthenticatedUser(
    val userId: UserId,
    val firebaseUid: String,
    val email: Email?,
    val phoneNumber: String?,
    val name: String,
    val role: UserRole,
    val currentSalonId: SalonId? = null,
    val isActive: Boolean,
    val staffAssociations: List<Staff> = emptyList()
) {

    init {
        require(firebaseUid.isNotBlank()) { "Firebase UID cannot be blank" }
        require(name.isNotBlank()) { "User name cannot be blank" }
        require(isActive) { "User must be active to be authenticated" }
    }

    /**
     * Check if user has admin privileges
     */
    fun isAdmin(): Boolean = role == UserRole.ADMIN

    /**
     * Check if user has groomer privileges
     */
    fun isGroomer(): Boolean = role == UserRole.STAFF

    /**
     * Check if user is a regular user
     */
    fun isRegularUser(): Boolean = role == UserRole.USER

    /**
     * Check if user has permission to access admin resources
     */
    fun canAccessAdminResources(): Boolean = isAdmin()

    /**
     * Check if user has permission to manage groomers
     */
    fun canManageGroomers(): Boolean = isAdmin()

    /**
     * Check if user has permission to manage appointments
     */
    fun canManageAppointments(): Boolean = isAdmin() || isGroomer()

    /**
     * Check if user has permission to view groomer schedules
     */
    fun canViewGroomerSchedules(): Boolean = isAdmin() || isGroomer()

    /**
     * Check if user can access specific groomer data
     */
    fun canAccessGroomerData(userId: UserId): Boolean {
        return when {
            isAdmin() -> true
            isGroomer() -> true // In a real implementation, check if this is the same groomer
            else -> false
        }
    }

    /**
     * Check if user has access to client data in a specific salon
     */
    fun hasClientDataAccessInSalon(salonId: SalonId): Boolean {
        if (isAdmin()) return true

        return staffAssociations
            .find { it.salonId == salonId && it.isActive }
            ?.permissions?.clientDataAccess != ClientDataAccess.NONE ?: false
    }

    /**
     * Check if user is chief groomer in a specific salon
     */
    fun isChiefGroomerInSalon(salonId: SalonId): Boolean {
        if (isAdmin()) return true

        return staffAssociations
            .find { it.salonId == salonId && it.isActive }
            ?.role == StaffRole.CHIEF_GROOMER ?: false
    }

    /**
     * Get client data permission level for a specific salon
     */
    fun getClientDataAccessInSalon(salonId: SalonId): ClientDataAccess {
        if (isAdmin()) return ClientDataAccess.FULL

        return staffAssociations
            .find { it.salonId == salonId && it.isActive }
            ?.permissions?.clientDataAccess ?: ClientDataAccess.NONE
    }

    /**
     * Get all salon IDs where user has any access
     */
    fun getAccessibleSalonIds(): List<SalonId> {
        return staffAssociations
            .filter { it.isActive }
            .map { it.salonId }
    }

    /**
     * Get all salon IDs where user has client data access
     */
    fun getSalonIdsWithClientAccess(): List<SalonId> {
        return staffAssociations
            .filter { it.isActive && it.permissions.clientDataAccess != ClientDataAccess.NONE }
            .map { it.salonId }
    }

    companion object {
        /**
         * Create authenticated user from domain user
         */
        fun from(user: User, staffAssociations: List<Staff> = emptyList()): AuthenticatedUser {
            require(user.isActive) { "Cannot create authenticated user from inactive user" }

            return AuthenticatedUser(
                userId = user.id,
                firebaseUid = user.firebaseUid,
                email = user.email,
                phoneNumber = user.phoneNumber,
                name = user.name,
                role = user.role,
                currentSalonId = user.currentSalonId,
                isActive = user.isActive,
                staffAssociations = staffAssociations
            )
        }
    }
}
