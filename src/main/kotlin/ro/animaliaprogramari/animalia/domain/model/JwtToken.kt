package ro.animaliaprogramari.animalia.domain.model

import java.time.LocalDateTime

/**
 * Value object representing a JWT token with metadata
 */
data class JwtToken(
    val token: String,
    val expiresAt: LocalDateTime,
    val issuedAt: LocalDateTime = LocalDateTime.now()
) {
    
    init {
        require(token.isNotBlank()) { "JWT token cannot be blank" }
        require(expiresAt.isAfter(issuedAt)) { "Token expiration must be after issue time" }
    }
    
    /**
     * Check if token is expired
     */
    fun isExpired(): Boolean = LocalDateTime.now().isAfter(expiresAt)
    
    /**
     * Check if token is valid (not expired)
     */
    fun isValid(): Boolean = !isExpired()
    
    /**
     * Get remaining validity duration in seconds
     */
    fun getRemainingValiditySeconds(): Long {
        val now = LocalDateTime.now()
        return if (now.isBefore(expiresAt)) {
            java.time.Duration.between(now, expiresAt).seconds
        } else {
            0L
        }
    }
    
    companion object {
        /**
         * Create JWT token with expiration
         */
        fun create(token: String, expiresAt: LocalDateTime): JwtToken {
            return JwtToken(token, expiresAt)
        }
        
        /**
         * Create JWT token with duration in seconds
         */
        fun createWithDuration(token: String, durationSeconds: Long): JwtToken {
            val expiresAt = LocalDateTime.now().plusSeconds(durationSeconds)
            return JwtToken(token, expiresAt)
        }
    }
}
