package ro.animaliaprogramari.animalia.domain.model

import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime

/**
 * Domain entity representing a schedule exception for a groomer
 * Pure domain model with no infrastructure dependencies
 */
data class GroomerScheduleException(
    val id: ScheduleExceptionId,
    val userId: UserId,
    val salonId: SalonId,
    val exceptionDate: LocalDate,
    val exceptionType: ExceptionType,
    val startTime: LocalTime?,
    val endTime: LocalTime?,
    val breakStartTime: LocalTime?,
    val breakEndTime: LocalTime?,
    val reason: String,
    val notes: String?,
    val createdBy: UserId,
    val approvedBy: UserId?,
    val approvalStatus: ApprovalStatus = ApprovalStatus.PENDING,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now()
) {

    init {
        require(reason.isNotBlank()) { "Exception reason cannot be blank" }
        require(reason.length <= 255) { "Exception reason cannot exceed 255 characters" }
        require(notes == null || notes.length <= 1000) { "Notes cannot exceed 1000 characters" }
        require(!exceptionDate.isBefore(LocalDate.now())) { "Exception date cannot be in the past" }

        // Validate working hours for custom hours exceptions
        if (exceptionType == ExceptionType.CUSTOM_HOURS) {
            require(startTime != null && endTime != null) { "Custom hours exception must have start and end times" }
            require(startTime.isBefore(endTime)) { "Start time must be before end time" }

            // Validate break times if present
            if (breakStartTime != null && breakEndTime != null) {
                require(breakStartTime.isBefore(breakEndTime)) { "Break start must be before break end" }
                require(!breakStartTime.isBefore(startTime)) { "Break start cannot be before work start" }
                require(!breakEndTime.isAfter(endTime)) { "Break end cannot be after work end" }
            }
        } else if (exceptionType == ExceptionType.DAY_OFF || exceptionType == ExceptionType.VACATION) {
            require(startTime == null && endTime == null) { "Day off exception cannot have working hours" }
            require(breakStartTime == null && breakEndTime == null) { "Day off exception cannot have break times" }
        }
    }

    /**
     * Check if this exception represents a working day
     */
    fun isWorkingDay(): Boolean {
        return exceptionType == ExceptionType.CUSTOM_HOURS && approvalStatus == ApprovalStatus.APPROVED
    }

    /**
     * Convert to daily schedule
     */
    fun toDailySchedule(): GroomerDailySchedule {
        return if (isWorkingDay()) {
            GroomerDailySchedule.workingDay(
                startTime = startTime!!,
                endTime = endTime!!,
                breakStart = breakStartTime,
                breakEnd = breakEndTime,
                notes = notes
            )
        } else {
            GroomerDailySchedule.dayOff(notes = notes)
        }
    }

    /**
     * Approve the exception
     */
    fun approve(approvedBy: UserId): GroomerScheduleException {
        require(approvalStatus == ApprovalStatus.PENDING) { "Only pending exceptions can be approved" }

        return copy(
            approvedBy = approvedBy,
            approvalStatus = ApprovalStatus.APPROVED,
            updatedAt = LocalDateTime.now()
        )
    }

    /**
     * Reject the exception
     */
    fun reject(rejectedBy: UserId): GroomerScheduleException {
        require(approvalStatus == ApprovalStatus.PENDING) { "Only pending exceptions can be rejected" }

        return copy(
            approvedBy = rejectedBy,
            approvalStatus = ApprovalStatus.REJECTED,
            updatedAt = LocalDateTime.now()
        )
    }

    /**
     * Update exception details
     */
    fun updateDetails(
        newReason: String,
        newNotes: String?,
        newStartTime: LocalTime? = null,
        newEndTime: LocalTime? = null,
        newBreakStart: LocalTime? = null,
        newBreakEnd: LocalTime? = null
    ): GroomerScheduleException {
        require(approvalStatus == ApprovalStatus.PENDING) { "Only pending exceptions can be updated" }
        require(newReason.isNotBlank()) { "Exception reason cannot be blank" }
        require(newReason.length <= 255) { "Exception reason cannot exceed 255 characters" }
        require(newNotes == null || newNotes.length <= 1000) { "Notes cannot exceed 1000 characters" }

        return copy(
            reason = newReason,
            notes = newNotes,
            startTime = newStartTime,
            endTime = newEndTime,
            breakStartTime = newBreakStart,
            breakEndTime = newBreakEnd,
            updatedAt = LocalDateTime.now()
        )
    }

    /**
     * Check if exception requires approval
     */
    fun requiresApproval(): Boolean {
        return exceptionType == ExceptionType.VACATION ||
               (exceptionType == ExceptionType.CUSTOM_HOURS &&
                java.time.Duration.between(LocalDateTime.now(), exceptionDate.atStartOfDay()).toDays() < 7)
    }

    companion object {
        /**
         * Create a day off exception
         */
        fun createDayOff(
            userId: UserId,
            salonId: SalonId,
            exceptionDate: LocalDate,
            reason: String,
            notes: String? = null,
            createdBy: UserId
        ): GroomerScheduleException {
            return GroomerScheduleException(
                id = ScheduleExceptionId.generate(),
                userId = userId,
                salonId = salonId,
                exceptionDate = exceptionDate,
                exceptionType = ExceptionType.DAY_OFF,
                startTime = null,
                endTime = null,
                breakStartTime = null,
                breakEndTime = null,
                reason = reason,
                notes = notes,
                createdBy = createdBy,
                approvedBy = null,
                approvalStatus = ApprovalStatus.APPROVED // Day off doesn't require approval
            )
        }

        /**
         * Create a custom hours exception
         */
        fun createCustomHours(
            userId: UserId,
            salonId: SalonId,
            exceptionDate: LocalDate,
            startTime: LocalTime,
            endTime: LocalTime,
            breakStart: LocalTime? = null,
            breakEnd: LocalTime? = null,
            reason: String,
            notes: String? = null,
            createdBy: UserId
        ): GroomerScheduleException {
            return GroomerScheduleException(
                id = ScheduleExceptionId.generate(),
                userId = userId,
                salonId = salonId,
                exceptionDate = exceptionDate,
                exceptionType = ExceptionType.CUSTOM_HOURS,
                startTime = startTime,
                endTime = endTime,
                breakStartTime = breakStart,
                breakEndTime = breakEnd,
                reason = reason,
                notes = notes,
                createdBy = createdBy,
                approvedBy = null,
                approvalStatus = ApprovalStatus.APPROVED // Will be updated based on business rules
            )
        }

        /**
         * Create a vacation exception
         */
        fun createVacation(
            userId: UserId,
            salonId: SalonId,
            exceptionDate: LocalDate,
            reason: String,
            notes: String? = null,
            createdBy: UserId
        ): GroomerScheduleException {
            return GroomerScheduleException(
                id = ScheduleExceptionId.generate(),
                userId = userId,
                salonId = salonId,
                exceptionDate = exceptionDate,
                exceptionType = ExceptionType.VACATION,
                startTime = null,
                endTime = null,
                breakStartTime = null,
                breakEndTime = null,
                reason = reason,
                notes = notes,
                createdBy = createdBy,
                approvedBy = null,
                approvalStatus = ApprovalStatus.PENDING // Vacation requires approval
            )
        }
    }
}

/**
 * Value object representing a schedule exception ID
 */
@JvmInline
value class ScheduleExceptionId(val value: String) {
    init {
        require(value.isNotBlank()) { "Schedule exception ID cannot be blank" }
    }

    companion object {
        fun generate(): ScheduleExceptionId = ScheduleExceptionId(java.util.UUID.randomUUID().toString())
        fun of(value: String): ScheduleExceptionId = ScheduleExceptionId(value)
    }
}

/**
 * Enumeration for exception types
 */
enum class ExceptionType {
    /**
     * Single day off
     */
    DAY_OFF,

    /**
     * Custom working hours for a specific day
     */
    CUSTOM_HOURS,

    /**
     * Vacation day (requires approval)
     */
    VACATION;

    companion object {
        fun fromString(type: String): ExceptionType {
            return valueOf(type.uppercase())
        }
    }
}

/**
 * Enumeration for approval status
 */
enum class ApprovalStatus {
    /**
     * Waiting for approval
     */
    PENDING,

    /**
     * Approved by chief groomer
     */
    APPROVED,

    /**
     * Rejected by chief groomer
     */
    REJECTED;

    companion object {
        fun fromString(status: String): ApprovalStatus {
            return valueOf(status.uppercase())
        }
    }
}
