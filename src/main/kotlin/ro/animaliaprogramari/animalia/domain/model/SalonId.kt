package ro.animaliaprogramari.animalia.domain.model

import java.util.UUID

/**
 * Value object representing a unique salon identifier
 */
@JvmInline
value class SalonId(val value: String) {
    
    init {
        require(value.isNotBlank()) { "Salon ID cannot be blank" }
    }
    
    companion object {
        fun generate(): SalonId = SalonId(UUID.randomUUID().toString())
        
        fun of(value: String): SalonId = SalonId(value)
    }
    
    override fun toString(): String = value
}
