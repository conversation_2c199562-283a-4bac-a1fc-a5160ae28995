package ro.animaliaprogramari.animalia.domain.model

import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime

/**
 * Domain entity representing an appointment
 * Pure domain model with no infrastructure dependencies
 */
data class Appointment(
    val id: AppointmentId,
    val salonId: SalonId,
    val clientId: ClientId,
    val petId: PetId,
    val staffId: StaffId, // Staff member (formerly userId)
    val appointmentDate: LocalDate,
    val startTime: LocalTime,
    val endTime: LocalTime,
    val status: AppointmentStatus,
    val serviceIds: List<ServiceId>,
    val totalPrice: Money,
    val totalDuration: Duration,
    val notes: String?,
    val repetitionFrequency: RepetitionFrequency?,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    val version: Long = 0 // For optimistic locking
) {
    
    init {
        require(startTime.isBefore(endTime)) { "Start time must be before end time" }
        require(serviceIds.isNotEmpty()) { "Appointment must have at least one service" }
        require(totalPrice.amount > BigDecimal.ZERO) { "Total price must be positive" }
        require(totalDuration.minutes > 0) { "Total duration must be positive" }
        notes?.let { require(it.length <= 2000) { "Notes cannot exceed 2000 characters" } }
    }

    /**
     * Update appointment status
     */
    fun updateStatus(newStatus: AppointmentStatus): Appointment {
        return copy(status = newStatus, updatedAt = LocalDateTime.now())
    }
    
    /**
     * Add a service to the appointment
     */
    fun addService(serviceId: ServiceId, price: Money, duration: Duration): Appointment {
        return copy(
            serviceIds = serviceIds + serviceId,
            totalPrice = totalPrice.add(price),
            totalDuration = totalDuration.add(duration),
            updatedAt = LocalDateTime.now()
        )
    }

    /**
     * Remove a service from the appointment
     */
    fun removeService(serviceId: ServiceId, price: Money, duration: Duration): Appointment {
        return copy(
            serviceIds = serviceIds.filterNot { it == serviceId },
            totalPrice = totalPrice.subtract(price),
            totalDuration = totalDuration.subtract(duration),
            updatedAt = LocalDateTime.now()
        )
    }
    
    /**
     * Update appointment notes
     */
    fun updateNotes(newNotes: String?): Appointment {
        return copy(notes = newNotes, updatedAt = LocalDateTime.now())
    }
    
    /**
     * Reschedule the appointment
     */
    fun reschedule(
        newDate: LocalDate,
        newStartTime: LocalTime,
        newEndTime: LocalTime,
        newStaffId: StaffId? = null
    ): Appointment {
        require(newStartTime.isBefore(newEndTime)) { "Start time must be before end time" }

        return copy(
            appointmentDate = newDate,
            startTime = newStartTime,
            endTime = newEndTime,
            staffId = newStaffId ?: staffId,
            status = AppointmentStatus.RESCHEDULED,
            updatedAt = LocalDateTime.now()
        )
    }

    /**
     * Cancel the appointment
     */
    fun cancel(reason: String? = null): Appointment {
        require(canBeCancelled()) { "Appointment cannot be cancelled in current status: $status" }

        return copy(
            status = AppointmentStatus.CANCELLED,
            notes = if (reason != null) {
                if (notes.isNullOrBlank()) "Cancelled: $reason" else "$notes\nCancelled: $reason"
            } else notes,
            updatedAt = LocalDateTime.now()
        )
    }

    /**
     * Complete the appointment
     */
    fun complete(completionNotes: String? = null): Appointment {
        require(canBeCompleted()) { "Appointment cannot be completed in current status: $status" }

        return copy(
            status = AppointmentStatus.COMPLETED,
            notes = if (completionNotes != null) {
                if (notes.isNullOrBlank()) completionNotes else "$notes\n$completionNotes"
            } else notes,
            updatedAt = LocalDateTime.now()
        )
    }

    /**
     * Mark appointment as no-show
     */
    fun markNoShow(noShowNotes: String? = null): Appointment {
        return copy(
            status = AppointmentStatus.NO_SHOW,
            notes = if (noShowNotes != null) {
                if (notes.isNullOrBlank()) "No-show: $noShowNotes" else "$notes\nNo-show: $noShowNotes"
            } else notes,
            updatedAt = LocalDateTime.now()
        )
    }

    /**
     * Mark appointment as in-progress
     */
    fun markInProgress(): Appointment {
        require(status == AppointmentStatus.SCHEDULED || status == AppointmentStatus.CONFIRMED) {
            "Appointment cannot be marked in-progress from status: $status"
        }

        return copy(
            status = AppointmentStatus.IN_PROGRESS,
            updatedAt = LocalDateTime.now()
        )
    }

    /**
     * Check if appointment overlaps with given time range
     */
    fun overlaps(otherStartTime: LocalTime, otherEndTime: LocalTime): Boolean {
        return !(endTime.isBefore(otherStartTime) || startTime.isAfter(otherEndTime))
    }

    /**
     * Check if appointment is active (not cancelled or completed)
     */
    fun isActive(): Boolean {
        return status !in listOf(AppointmentStatus.CANCELLED, AppointmentStatus.COMPLETED, AppointmentStatus.NO_SHOW)
    }
    
    /**
     * Check if appointment is in the past
     */
    fun isPast(): Boolean {
        val appointmentDateTime = LocalDateTime.of(appointmentDate, startTime)
        return appointmentDateTime.isBefore(LocalDateTime.now())
    }
    
    /**
     * Check if appointment is today
     */
    fun isToday(): Boolean = appointmentDate == LocalDate.now()
    
    /**
     * Check if appointment can be cancelled
     */
    fun canBeCancelled(): Boolean {
        return status in listOf(AppointmentStatus.SCHEDULED, AppointmentStatus.CONFIRMED, AppointmentStatus.RESCHEDULED) && !isPast()
    }
    
    /**
     * Check if appointment can be completed
     */
    fun canBeCompleted(): Boolean {
        return status in listOf(AppointmentStatus.SCHEDULED, AppointmentStatus.CONFIRMED, AppointmentStatus.IN_PROGRESS)
    }

    /**
     * Check if appointment can be rescheduled
     */
    fun canBeRescheduled(): Boolean {
        return status in listOf(AppointmentStatus.SCHEDULED, AppointmentStatus.CONFIRMED, AppointmentStatus.RESCHEDULED) && !isPast()
    }
    
    companion object {
        /**
         * Create a new appointment
         */
        fun create(
            salonId: SalonId,
            clientId: ClientId,
            petId: PetId,
            staffId: StaffId,
            appointmentDate: LocalDate,
            startTime: LocalTime,
            endTime: LocalTime,
            salonServices: List<SalonService>,
            notes: String? = null,
            repetitionFrequency: RepetitionFrequency? = null
        ): Appointment = Appointment(
            id = AppointmentId.generate(),
            salonId = salonId,
            clientId = clientId,
            petId = petId,
            staffId = staffId,
            appointmentDate = appointmentDate,
            startTime = startTime,
            endTime = endTime,
            status = AppointmentStatus.SCHEDULED,
            serviceIds = salonServices.map { it.id },
            totalPrice = salonServices.fold(Money.ZERO) { total, service ->
                total.add(service.basePrice)
            },
            totalDuration = salonServices.fold(Duration.ofMinutes(0)) { total, service ->
                total.add(service.duration)
            },
            notes = notes,
            repetitionFrequency = repetitionFrequency
        )
    }

}

