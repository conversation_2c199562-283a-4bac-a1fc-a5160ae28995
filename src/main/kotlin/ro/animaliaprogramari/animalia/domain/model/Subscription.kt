package ro.animaliaprogramari.animalia.domain.model

import java.math.BigDecimal
import java.time.LocalDateTime

/**
 * Domain entity representing a client subscription
 * Pure domain model with no infrastructure dependencies
 */
data class Subscription(
    val id: SubscriptionId,
    val clientId: ClientId,
    val salonId: SalonId,
    val name: String,
    val description: String,
    val price: BigDecimal,
    val frequency: SubscriptionFrequency,
    val startDate: LocalDateTime,
    val endDate: LocalDateTime?,
    val isActive: Boolean = true,
    val sessionsIncluded: Int,
    val sessionsUsed: Int = 0,
    val includedServices: List<String> = emptyList(),
    val notes: String = "",
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now()
) {

    init {
        require(name.isNotBlank()) { "Subscription name cannot be blank" }
        require(name.length <= 255) { "Subscription name cannot exceed 255 characters" }
        require(description.length <= 1000) { "Description cannot exceed 1000 characters" }
        require(price >= BigDecimal.ZERO) { "Price must be non-negative" }
        require(sessionsIncluded > 0) { "Sessions included must be positive" }
        require(sessionsUsed >= 0) { "Sessions used cannot be negative" }
        require(sessionsUsed <= sessionsIncluded) { "Sessions used cannot exceed sessions included" }
        require(notes.length <= 2000) { "Notes cannot exceed 2000 characters" }
        endDate?.let { require(it.isAfter(startDate)) { "End date must be after start date" } }
    }

    /**
     * Use a session from the subscription
     */
    fun useSession(): Subscription {
        require(hasRemainingSessions()) { "No remaining sessions" }
        require(isActive) { "Subscription is not active" }
        
        return copy(
            sessionsUsed = sessionsUsed + 1,
            updatedAt = LocalDateTime.now()
        )
    }

    /**
     * Check if subscription has remaining sessions
     */
    fun hasRemainingSessions(): Boolean = sessionsUsed < sessionsIncluded

    /**
     * Get remaining sessions count
     */
    fun remainingSessions(): Int = sessionsIncluded - sessionsUsed

    /**
     * Check if subscription is currently valid
     */
    fun isCurrentlyValid(): Boolean {
        val now = LocalDateTime.now()
        return isActive && 
               now.isAfter(startDate) && 
               (endDate == null || now.isBefore(endDate)) &&
               hasRemainingSessions()
    }

    /**
     * Deactivate the subscription
     */
    fun deactivate(): Subscription {
        return copy(
            isActive = false,
            updatedAt = LocalDateTime.now()
        )
    }

    /**
     * Activate the subscription
     */
    fun activate(): Subscription {
        return copy(
            isActive = true,
            updatedAt = LocalDateTime.now()
        )
    }

    /**
     * Update subscription details
     */
    fun update(
        name: String? = null,
        description: String? = null,
        price: BigDecimal? = null,
        endDate: LocalDateTime? = null,
        notes: String? = null
    ): Subscription {
        return copy(
            name = name ?: this.name,
            description = description ?: this.description,
            price = price ?: this.price,
            endDate = endDate ?: this.endDate,
            notes = notes ?: this.notes,
            updatedAt = LocalDateTime.now()
        )
    }

    companion object {
        /**
         * Create a new subscription
         */
        fun create(
            clientId: ClientId,
            salonId: SalonId,
            name: String,
            description: String,
            price: BigDecimal,
            frequency: SubscriptionFrequency,
            startDate: LocalDateTime,
            endDate: LocalDateTime?,
            sessionsIncluded: Int,
            includedServices: List<String> = emptyList(),
            notes: String = ""
        ): Subscription {
            return Subscription(
                id = SubscriptionId.generate(),
                clientId = clientId,
                salonId = salonId,
                name = name,
                description = description,
                price = price,
                frequency = frequency,
                startDate = startDate,
                endDate = endDate,
                sessionsIncluded = sessionsIncluded,
                includedServices = includedServices,
                notes = notes
            )
        }
    }
}

/**
 * Enum representing subscription frequency
 */
enum class SubscriptionFrequency {
    WEEKLY,
    BIWEEKLY,
    MONTHLY,
    QUARTERLY,
    YEARLY
}

/**
 * Value object representing a subscription identifier
 */
@JvmInline
value class SubscriptionId(val value: String) {
    
    init {
        require(value.isNotBlank()) { "Subscription ID cannot be blank" }
    }
    
    companion object {
        fun generate(): SubscriptionId = SubscriptionId(java.util.UUID.randomUUID().toString())
        fun of(value: String): SubscriptionId = SubscriptionId(value)
    }
    
    override fun toString(): String = value
}
