package ro.animaliaprogramari.animalia.domain.model

import org.slf4j.LoggerFactory
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime

/**
 * Value object representing working hours for a groomer
 */
data class WorkingHours(
    val schedule: Map<DayOfWeek, DaySchedule>
) {
    
    init {
        require(schedule.isNotEmpty()) { "Working hours must have at least one day defined" }
    }
    
    /**
     * Check if groomer is available on a specific day
     */
    fun isAvailableOn(dayOfWeek: DayOfWeek): Boolean {
        return schedule[dayOfWeek]?.isWorkingDay == true
    }
    
    /**
     * Get working hours for a specific day
     */
    fun getScheduleFor(dayOfWeek: DayOfWeek): DaySchedule? {
        return schedule[dayOfWeek]
    }
    
    /**
     * Check if groomer is available at a specific time on a specific day
     */
    fun isAvailableAt(dayOfWeek: DayOfWeek, time: LocalTime): Boolean {
        val daySchedule = schedule[dayOfWeek] ?: return false
        return daySchedule.isAvailableAt(time)
    }
    
    /**
     * Get all working days
     */
    fun getWorkingDays(): Set<DayOfWeek> {
        return schedule.filterValues { it.isWorkingDay }.keys
    }
    
    companion object {
        /**
         * Create standard working hours (Monday to Friday, 9 AM to 5 PM)
         */
        fun standard(): WorkingHours {
            val standardSchedule = mapOf(
                DayOfWeek.MONDAY to DaySchedule.workingDay(LocalTime.of(9, 0), LocalTime.of(17, 0)),
                DayOfWeek.TUESDAY to DaySchedule.workingDay(LocalTime.of(9, 0), LocalTime.of(17, 0)),
                DayOfWeek.WEDNESDAY to DaySchedule.workingDay(LocalTime.of(9, 0), LocalTime.of(17, 0)),
                DayOfWeek.THURSDAY to DaySchedule.workingDay(LocalTime.of(9, 0), LocalTime.of(17, 0)),
                DayOfWeek.FRIDAY to DaySchedule.workingDay(LocalTime.of(9, 0), LocalTime.of(17, 0)),
                DayOfWeek.SATURDAY to DaySchedule.dayOff(),
                DayOfWeek.SUNDAY to DaySchedule.dayOff()
            )
            return WorkingHours(standardSchedule)
        }
        
        /**
         * Create working hours with custom schedule
         */
        fun custom(schedule: Map<DayOfWeek, DaySchedule>): WorkingHours {
            return WorkingHours(schedule)
        }
    }
}

/**
 * Value object representing schedule for a single day
 */
data class DaySchedule(
    val isWorkingDay: Boolean,
    val startTime: LocalTime?,
    val endTime: LocalTime?,
    val breakStart: LocalTime? = null,
    val breakEnd: LocalTime? = null
) {
    
    init {
        if (isWorkingDay) {
            requireNotNull(startTime) { "Working day must have start time" }
            requireNotNull(endTime) { "Working day must have end time" }
            require(startTime.isBefore(endTime)) { "Start time must be before end time" }
            
            if (breakStart != null && breakEnd != null) {
                require(breakStart.isBefore(breakEnd)) { "Break start must be before break end" }
                require(!breakStart.isBefore(startTime)) { "Break start cannot be before work start" }
                require(!breakEnd.isAfter(endTime)) { "Break end cannot be after work end" }
            }
        }
    }
    
    /**
     * Check if available at a specific time
     */
    fun isAvailableAt(time: LocalTime): Boolean {
        val logger = LoggerFactory.getLogger(DaySchedule::class.java)

        if (!isWorkingDay || startTime == null || endTime == null) {
            logger.debug("Time {} not available: not a working day or missing start/end times", time)
            return false
        }

        if (time.isBefore(startTime) || time.isAfter(endTime)) {
            logger.debug("Time {} not available: outside working hours {}-{}", time, startTime, endTime)
            return false
        }

        // Check if time is during break (break time is inclusive of start, exclusive of end)
        if (breakStart != null && breakEnd != null) {
            if (!time.isBefore(breakStart) && time.isBefore(breakEnd)) {
                logger.debug("Time {} not available: during break time {}-{}", time, breakStart, breakEnd)
                return false
            }
        }

        logger.debug("Time {} is available (working hours: {}-{}, break: {}-{})",
            time, startTime, endTime, breakStart, breakEnd)
        return true
    }
    
    companion object {
        /**
         * Create a working day schedule
         */
        fun workingDay(
            startTime: LocalTime,
            endTime: LocalTime,
            breakStart: LocalTime? = null,
            breakEnd: LocalTime? = null
        ): DaySchedule {
            return DaySchedule(true, startTime, endTime, breakStart, breakEnd)
        }
        
        /**
         * Create a day off schedule
         */
        fun dayOff(): DaySchedule {
            return DaySchedule(false, null, null)
        }
    }
}

/**
 * Domain entity representing comprehensive working hours settings for a staff member
 * Includes weekly schedule, holidays, and custom closures
 */
data class StaffWorkingHoursSettings(
    val staffId: StaffId,
    val salonId: SalonId,
    val weeklySchedule: Map<DayOfWeek, DaySchedule>,
    val holidays: List<StaffHoliday>,
    val customClosures: List<StaffCustomClosure>,
    val updatedAt: LocalDateTime = LocalDateTime.now()
) {

    init {
        require(weeklySchedule.isNotEmpty()) { "Weekly schedule must have at least one day defined" }
        require(weeklySchedule.size == 7) { "Weekly schedule must have all 7 days defined" }
    }

    /**
     * Check if staff is available on a specific date
     */
    fun isAvailableOn(date: LocalDate): Boolean {
        val dayOfWeek = date.dayOfWeek

        // Check if it's a custom closure
        if (customClosures.any { it.date == date }) {
            return false
        }

        // Check if it's a holiday (and not marked as working day)
        val holiday = holidays.find { it.date == date }
        if (holiday != null && !holiday.isWorkingDay) {
            return false
        }

        // Check weekly schedule
        val daySchedule = weeklySchedule[dayOfWeek] ?: return false
        return daySchedule.isWorkingDay
    }

    /**
     * Get working hours for a specific date
     */
    fun getWorkingHoursFor(date: LocalDate): DaySchedule? {
        if (!isAvailableOn(date)) {
            return null
        }

        val dayOfWeek = date.dayOfWeek
        return weeklySchedule[dayOfWeek]
    }

    /**
     * Update weekly schedule
     */
    fun updateWeeklySchedule(newSchedule: Map<DayOfWeek, DaySchedule>): StaffWorkingHoursSettings {
        require(newSchedule.size == 7) { "Weekly schedule must have all 7 days defined" }
        return copy(
            weeklySchedule = newSchedule,
            updatedAt = LocalDateTime.now()
        )
    }

    /**
     * Update holidays
     */
    fun updateHolidays(newHolidays: List<StaffHoliday>): StaffWorkingHoursSettings {
        return copy(
            holidays = newHolidays,
            updatedAt = LocalDateTime.now()
        )
    }

    /**
     * Update custom closures
     */
    fun updateCustomClosures(newClosures: List<StaffCustomClosure>): StaffWorkingHoursSettings {
        return copy(
            customClosures = newClosures,
            updatedAt = LocalDateTime.now()
        )
    }

    companion object {
        /**
         * Create default working hours settings for a staff member
         */
        fun createDefault(staffId: StaffId, salonId: SalonId): StaffWorkingHoursSettings {
            val defaultSchedule = mapOf(
                DayOfWeek.MONDAY to DaySchedule.workingDay(
                    LocalTime.of(9, 0), LocalTime.of(17, 0),
                    LocalTime.of(12, 0), LocalTime.of(13, 0)
                ),
                DayOfWeek.TUESDAY to DaySchedule.workingDay(
                    LocalTime.of(9, 0), LocalTime.of(17, 0),
                    LocalTime.of(12, 0), LocalTime.of(13, 0)
                ),
                DayOfWeek.WEDNESDAY to DaySchedule.workingDay(
                    LocalTime.of(9, 0), LocalTime.of(17, 0),
                    LocalTime.of(12, 0), LocalTime.of(13, 0)
                ),
                DayOfWeek.THURSDAY to DaySchedule.workingDay(
                    LocalTime.of(9, 0), LocalTime.of(17, 0),
                    LocalTime.of(12, 0), LocalTime.of(13, 0)
                ),
                DayOfWeek.FRIDAY to DaySchedule.workingDay(
                    LocalTime.of(9, 0), LocalTime.of(17, 0),
                    LocalTime.of(12, 0), LocalTime.of(13, 0)
                ),
                DayOfWeek.SATURDAY to DaySchedule.workingDay(
                    LocalTime.of(10, 0), LocalTime.of(15, 0)
                ),
                DayOfWeek.SUNDAY to DaySchedule.dayOff()
            )

            val defaultHolidays = createDefaultRomanianHolidays(salonId)

            return StaffWorkingHoursSettings(
                staffId = staffId,
                salonId = salonId,
                weeklySchedule = defaultSchedule,
                holidays = defaultHolidays,
                customClosures = emptyList()
            )
        }

        /**
         * Create default Romanian holidays for current and next year
         */
        private fun createDefaultRomanianHolidays(salonId: SalonId): List<StaffHoliday> {
            val currentYear = LocalDate.now().year
            val holidays = mutableListOf<StaffHoliday>()

            // Add holidays for current and next year
            for (year in currentYear..currentYear + 1) {
                holidays.addAll(
                    listOf(
                        StaffHoliday.create(salonId, "Anul Nou", LocalDate.of(year, 1, 1)),
                        StaffHoliday.create(salonId, "Bobotează", LocalDate.of(year, 1, 6)),
                        StaffHoliday.create(salonId, "Ziua Muncii", LocalDate.of(year, 5, 1)),
                        StaffHoliday.create(salonId, "Ziua Copilului", LocalDate.of(year, 6, 1)),
                        StaffHoliday.create(salonId, "Adormirea Maicii Domnului", LocalDate.of(year, 8, 15)),
                        StaffHoliday.create(salonId, "Sfântul Andrei", LocalDate.of(year, 11, 30)),
                        StaffHoliday.create(salonId, "Ziua Națională", LocalDate.of(year, 12, 1)),
                        StaffHoliday.create(salonId, "Crăciunul", LocalDate.of(year, 12, 25)),
                        StaffHoliday.create(salonId, "A doua zi de Crăciun", LocalDate.of(year, 12, 26))
                    )
                )
            }

            return holidays
        }
    }
}
