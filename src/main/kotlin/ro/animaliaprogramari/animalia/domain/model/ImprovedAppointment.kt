//package ro.animaliaprogramari.animalia.domain.model
//
//import java.time.Duration
//import java.time.LocalDate
//import java.time.LocalDateTime
//import java.time.LocalTime
//
///**
// * Improved Appointment domain entity using value objects
// * Eliminates data duplication by removing totalPrice and totalDuration
// * Uses rich value objects for better domain expressiveness
// */
//data class ImprovedAppointment(
//    val id: AppointmentId,
//    val salonId: SalonId,
//    val clientId: ClientId,
//    val petId: PetId,
//    val staffId: StaffId, // Will be changed to StaffId when Staff entity is implemented
//    val schedule: AppointmentSchedule,
//    val status: AppointmentStatus,
//    val serviceIds: List<ServiceId>,
//    val notes: AppointmentNotes?,
//    val repetitionFrequency: RepetitionFrequency?,
//    val createdAt: LocalDateTime = LocalDateTime.now(),
//    val updatedAt: LocalDateTime = LocalDateTime.now()
//) {
//
//    init {
//        require(serviceIds.isNotEmpty()) { "Appointment must have at least one service" }
//        require(schedule.timeSlot.isValidDuration()) { "Invalid appointment duration" }
//    }
//
//    /**
//     * Calculate total price from services - eliminates data duplication
//     */
//    fun calculateTotalPrice(services: List<SalonService>): Money {
//        val appointmentServices = services.filter { service ->
//            serviceIds.contains(service.id)
//        }
//        return appointmentServices.fold(Money.ZERO) { total, service ->
//            total.add(service.basePrice)
//        }
//    }
//
//    /**
//     * Calculate total duration from services - eliminates data duplication
//     */
//    fun calculateTotalDuration(services: List<SalonService>): Duration {
//        val appointmentServices = services.filter { service ->
//            serviceIds.contains(service.id)
//        }
//        return appointmentServices.fold(Duration.ofMinutes(0)) { total, service ->
//            total.add(service.duration)
//        }
//    }
//
//    /**
//     * Validate that the appointment time matches the service duration
//     */
//    fun validateTimeMatchesServices(services: List<SalonService>): Boolean {
//        val calculatedDuration = calculateTotalDuration(services)
//        return schedule.timeSlot.duration() == calculatedDuration
//    }
//
//    /**
//     * Update appointment status
//     */
//    fun updateStatus(newStatus: AppointmentStatus): ImprovedAppointment {
//        return copy(status = newStatus, updatedAt = LocalDateTime.now())
//    }
//
//    /**
//     * Add a service to the appointment
//     */
//    fun addService(serviceId: ServiceId): ImprovedAppointment {
//        return copy(
//            serviceIds = serviceIds + serviceId,
//            updatedAt = LocalDateTime.now()
//        )
//    }
//
//    /**
//     * Remove a service from the appointment
//     */
//    fun removeService(serviceId: ServiceId): ImprovedAppointment {
//        return copy(
//            serviceIds = serviceIds.filterNot { it == serviceId },
//            updatedAt = LocalDateTime.now()
//        )
//    }
//
//    /**
//     * Update appointment notes using value object
//     */
//    fun updateNotes(newNotes: String?): ImprovedAppointment {
//        val appointmentNotes = if (newNotes.isNullOrBlank()) null else AppointmentNotes.of(newNotes)
//        return copy(notes = appointmentNotes, updatedAt = LocalDateTime.now())
//    }
//
//    /**
//     * Add a note to existing notes
//     */
//    fun addNote(note: String): ImprovedAppointment {
//        val updatedNotes = notes?.addNote(note) ?: AppointmentNotes.of(note)
//        return copy(notes = updatedNotes, updatedAt = LocalDateTime.now())
//    }
//
//    /**
//     * Reschedule the appointment using value objects
//     */
//    fun reschedule(
//        newSchedule: AppointmentSchedule,
//        newstaffId: StaffId? = null
//    ): ImprovedAppointment {
//        return copy(
//            schedule = newSchedule,
//            staffId = newStaffId ?: staffId,
//            status = AppointmentStatus.RESCHEDULED,
//            updatedAt = LocalDateTime.now()
//        )
//    }
//
//    /**
//     * Cancel the appointment with improved notes handling
//     */
//    fun cancel(reason: String? = null): ImprovedAppointment {
//        require(canBeCancelled()) { "Appointment cannot be cancelled in current status: $status" }
//
//        val updatedNotes = if (reason != null) {
//            notes?.addCancellationNote(reason) ?: AppointmentNotes.of("Cancelled: $reason")
//        } else notes
//
//        return copy(
//            status = AppointmentStatus.CANCELLED,
//            notes = updatedNotes,
//            updatedAt = LocalDateTime.now()
//        )
//    }
//
//    /**
//     * Complete the appointment with improved notes handling
//     */
//    fun complete(completionNotes: String? = null): ImprovedAppointment {
//        require(canBeCompleted()) { "Appointment cannot be completed in current status: $status" }
//
//        val updatedNotes = if (completionNotes != null) {
//            notes?.addCompletionNote(completionNotes) ?: AppointmentNotes.of(completionNotes)
//        } else notes
//
//        return copy(
//            status = AppointmentStatus.COMPLETED,
//            notes = updatedNotes,
//            updatedAt = LocalDateTime.now()
//        )
//    }
//
//    /**
//     * Mark appointment as no-show with improved notes handling
//     */
//    fun markNoShow(noShowNotes: String? = null): ImprovedAppointment {
//        val updatedNotes = if (noShowNotes != null) {
//            notes?.addNoShowNote(noShowNotes) ?: AppointmentNotes.of("No-show: $noShowNotes")
//        } else notes
//
//        return copy(
//            status = AppointmentStatus.NO_SHOW,
//            notes = updatedNotes,
//            updatedAt = LocalDateTime.now()
//        )
//    }
//
//    /**
//     * Mark appointment as in-progress
//     */
//    fun markInProgress(): ImprovedAppointment {
//        require(status == AppointmentStatus.SCHEDULED || status == AppointmentStatus.CONFIRMED) {
//            "Appointment cannot be marked in-progress from status: $status"
//        }
//
//        return copy(
//            status = AppointmentStatus.IN_PROGRESS,
//            updatedAt = LocalDateTime.now()
//        )
//    }
//
//    /**
//     * Check if appointment overlaps with another appointment
//     */
//    fun overlaps(other: ImprovedAppointment): Boolean {
//        return schedule.overlaps(other.schedule)
//    }
//
//    /**
//     * Check if appointment overlaps with given schedule
//     */
//    fun overlaps(otherSchedule: AppointmentSchedule): Boolean {
//        return schedule.overlaps(otherSchedule)
//    }
//
//    /**
//     * Check if appointment is active (not cancelled or completed)
//     */
//    fun isActive(): Boolean {
//        return status !in listOf(AppointmentStatus.CANCELLED, AppointmentStatus.COMPLETED, AppointmentStatus.NO_SHOW)
//    }
//
//    /**
//     * Check if appointment is in the past
//     */
//    fun isPast(): Boolean = schedule.isInPast()
//
//    /**
//     * Check if appointment is today
//     */
//    fun isToday(): Boolean = schedule.isToday()
//
//    /**
//     * Check if appointment can be cancelled
//     */
//    fun canBeCancelled(minimumHours: Long = 2): Boolean {
//        return status in listOf(AppointmentStatus.SCHEDULED, AppointmentStatus.CONFIRMED) &&
//               schedule.canBeCancelled(minimumHours)
//    }
//
//    /**
//     * Check if appointment can be completed
//     */
//    fun canBeCompleted(): Boolean {
//        return status in listOf(AppointmentStatus.SCHEDULED, AppointmentStatus.CONFIRMED, AppointmentStatus.IN_PROGRESS)
//    }
//
//    /**
//     * Get formatted schedule
//     */
//    fun formattedSchedule(): String = schedule.formatted()
//
//    /**
//     * Get notes preview
//     */
//    fun notesPreview(maxLength: Int = 100): String? = notes?.preview(maxLength)
//
//    companion object {
//        /**
//         * Create a new appointment with automatic time calculation
//         */
//        fun create(
//            salonId: SalonId,
//            clientId: ClientId,
//            petId: PetId,
//            staffId: StaffId,
//            schedule: AppointmentSchedule,
//            salonServices: List<SalonService>,
//            notes: String? = null,
//            repetitionFrequency: RepetitionFrequency? = null
//        ): ImprovedAppointment {
//            val appointmentNotes = if (notes.isNullOrBlank()) null else AppointmentNotes.of(notes)
//
//            return ImprovedAppointment(
//                id = AppointmentId.generate(),
//                salonId = salonId,
//                clientId = clientId,
//                petId = petId,
//                staffId = staffId,
//                schedule = schedule,
//                status = AppointmentStatus.SCHEDULED,
//                serviceIds = salonServices.map { it.id },
//                notes = appointmentNotes,
//                repetitionFrequency = repetitionFrequency
//            )
//        }
//
//        /**
//         * Create appointment with date and time parameters
//         */
//        fun create(
//            salonId: SalonId,
//            clientId: ClientId,
//            petId: PetId,
//            staffId: StaffId,
//            appointmentDate: LocalDate,
//            startTime: LocalTime,
//            salonServices: List<SalonService>,
//            notes: String? = null,
//            repetitionFrequency: RepetitionFrequency? = null
//        ): ImprovedAppointment {
//            // Calculate end time from services
//            val totalDuration = salonServices.fold(Duration.ofMinutes(0)) { total, service ->
//                total.plus(service.duration)
//            }
//            val endTime = startTime.plus(totalDuration)
//
//            val schedule = AppointmentSchedule.of(appointmentDate, startTime, endTime)
//
//            return create(
//                salonId = salonId,
//                clientId = clientId,
//                petId = petId,
//                staffId = staffId,
//                schedule = schedule,
//                salonServices = salonServices,
//                notes = notes,
//                repetitionFrequency = repetitionFrequency
//            )
//        }
//    }
//}
