package ro.animaliaprogramari.animalia.domain.model

import java.time.LocalDateTime

/**
 * Domain entity representing SMS reminder settings for a salon
 * Pure domain model with no infrastructure dependencies
 */
data class SmsReminderSettings(
    val salonId: SalonId,
    val appointmentConfirmations: Boolean = true,
    val dayBeforeReminders: Boolean = true,
    val followUpMessages: Boolean = false,
    val selectedProvider: SmsProvider = SmsProvider.ORANGE,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now()
) {

    /**
     * Update SMS reminder settings
     */
    fun updateSettings(
        appointmentConfirmations: Boolean? = null,
        dayBeforeReminders: Boolean? = null,
        followUpMessages: Boolean? = null,
        selectedProvider: SmsProvider? = null
    ): SmsReminderSettings {
        return copy(
            appointmentConfirmations = appointmentConfirmations ?: this.appointmentConfirmations,
            dayBeforeReminders = dayBeforeReminders ?: this.dayBeforeReminders,
            followUpMessages = followUpMessages ?: this.followUpMessages,
            selectedProvider = selectedProvider ?: this.selectedProvider,
            updatedAt = LocalDateTime.now()
        )
    }

    /**
     * Check if any reminder type is enabled
     */
    fun hasAnyReminderEnabled(): Boolean {
        return appointmentConfirmations || dayBeforeReminders || followUpMessages
    }

    companion object {
        /**
         * Create default SMS reminder settings for a salon
         */
        fun createDefault(salonId: SalonId): SmsReminderSettings {
            return SmsReminderSettings(
                salonId = salonId,
                appointmentConfirmations = true,
                dayBeforeReminders = true,
                followUpMessages = false,
                selectedProvider = SmsProvider.ORANGE
            )
        }
    }
}

/**
 * Enumeration of supported SMS providers
 */
enum class SmsProvider(val displayName: String) {
    ORANGE("orange"),
    VODAFONE("vodafone"),
    TELEKOM("telekom"),
    DIGI("digi");

    companion object {
        fun fromString(value: String): SmsProvider {
            return values().find { it.displayName.equals(value, ignoreCase = true) }
                ?: throw IllegalArgumentException("Unknown SMS provider: $value")
        }
    }
}
