package ro.animaliaprogramari.animalia.domain.model

import java.time.LocalTime

/**
 * Value object representing lunch break settings
 */
data class LunchBreak(
    val startTime: LocalTime,
    val endTime: LocalTime,
    val enabled: Boolean
) {

    init {
        if (enabled) {
            require(startTime.isBefore(endTime)) { "Lunch break start time must be before end time" }
        }
    }

    companion object {
        /**
         * Create disabled lunch break
         */
        fun disabled(): LunchBreak {
            return LunchBreak(
                startTime = LocalTime.of(12, 0),
                endTime = LocalTime.of(13, 0),
                enabled = false
            )
        }

        /**
         * Create enabled lunch break
         */
        fun enabled(startTime: LocalTime, endTime: LocalTime): LunchBreak {
            return LunchBreak(
                startTime = startTime,
                endTime = endTime,
                enabled = true
            )
        }
    }
}
