package ro.animaliaprogramari.animalia.domain.model

import java.time.LocalDateTime

/**
 * Domain entity representing a grooming service
 * Pure domain model with no infrastructure dependencies
 * Each service is scoped to a specific salon
 */
data class SalonService(
    val id: ServiceId,
    val salonId: SalonId,
    val name: String,
    val description: String?,
    val basePrice: Money,
    val duration: Duration,
    val category: ServiceCategory,
    val displayOrder: Int = 0,
    val requirements: List<String> = emptyList(),
    val isActive: Boolean = true,
    // Variable pricing fields
    val sizePrices: Map<String, Money> = emptyMap(),
    val sizeDurations: Map<String, Duration> = emptyMap(),
    // Min-max pricing fields
    val minPrice: Money? = null,
    val maxPrice: Money? = null,
    val sizeMinPrices: Map<String, Money>? = null,
    val sizeMaxPrices: Map<String, Money>? = null,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now()
) {

    // Validation is now handled by ValidationService in the application layer
    // This keeps the domain model clean and validation logic centralized

    /**
     * Update service information
     */
    fun update(
        name: String? = null,
        description: String? = null,
        basePrice: Money? = null,
        duration: Duration? = null,
        category: ServiceCategory? = null,
        displayOrder: Int? = null,
        requirements: List<String>? = null,
        isActive: Boolean? = null,
        sizePrices: Map<String, Money>? = null,
        sizeDurations: Map<String, Duration>? = null,
        minPrice: Money? = null,
        maxPrice: Money? = null,
        sizeMinPrices: Map<String, Money>? = null,
        sizeMaxPrices: Map<String, Money>? = null
    ): SalonService {
        return copy(
            name = name ?: this.name,
            description = description ?: this.description,
            basePrice = basePrice ?: this.basePrice,
            duration = duration ?: this.duration,
            category = category ?: this.category,
            displayOrder = displayOrder ?: this.displayOrder,
            requirements = requirements ?: this.requirements,
            isActive = isActive ?: this.isActive,
            sizePrices = sizePrices ?: this.sizePrices,
            sizeDurations = sizeDurations ?: this.sizeDurations,
            minPrice = minPrice ?: this.minPrice,
            maxPrice = maxPrice ?: this.maxPrice,
            sizeMinPrices = sizeMinPrices ?: this.sizeMinPrices,
            sizeMaxPrices = sizeMaxPrices ?: this.sizeMaxPrices,
            updatedAt = LocalDateTime.now()
        )
    }

    /**
     * Deactivate the service
     */
    fun deactivate(): SalonService = copy(isActive = false, updatedAt = LocalDateTime.now())

    /**
     * Activate the service
     */
    fun activate(): SalonService = copy(isActive = true, updatedAt = LocalDateTime.now())

    /**
     * Get formatted price as string
     */
    fun getFormattedPrice(): String = "${basePrice.amount} RON"

    /**
     * Get formatted duration as string
     */
    fun getFormattedDuration(): String {
        val totalMinutes = duration.minutes
        val hours = totalMinutes / 60
        val minutes = totalMinutes % 60
        return when {
            hours > 0 && minutes > 0 -> "${hours}h ${minutes}min"
            hours > 0 -> "${hours}h"
            else -> "${minutes}min"
        }
    }

    companion object {
        /**
         * Create a new service for a salon
         */
        fun create(
            salonId: SalonId,
            name: String,
            description: String?,
            basePrice: Money,
            duration: Duration,
            category: ServiceCategory,
            displayOrder: Int = 0,
            requirements: List<String> = emptyList(),
            sizePrices: Map<String, Money> = emptyMap(),
            sizeDurations: Map<String, Duration> = emptyMap(),
            minPrice: Money? = null,
            maxPrice: Money? = null,
            sizeMinPrices: Map<String, Money>? = null,
            sizeMaxPrices: Map<String, Money>? = null
        ): SalonService {
            return SalonService(
                id = ServiceId.generate(),
                salonId = salonId,
                name = name,
                description = description,
                basePrice = basePrice,
                duration = duration,
                category = category,
                displayOrder = displayOrder,
                requirements = requirements,
                sizePrices = sizePrices,
                sizeDurations = sizeDurations,
                minPrice = minPrice,
                maxPrice = maxPrice,
                sizeMinPrices = sizeMinPrices,
                sizeMaxPrices = sizeMaxPrices
            )
        }
    }

    /**
     * Calculate price with potential discount
     */
    fun calculatePrice(discountPercentage: Double = 0.0): Money {
        require(discountPercentage >= 0.0 && discountPercentage <= 100.0) {
            "Discount percentage must be between 0 and 100"
        }

        return if (discountPercentage > 0.0) {
            val discountFactor = 1.0 - (discountPercentage / 100.0)
            basePrice.multiply(discountFactor)
        } else {
            basePrice
        }
    }

    /**
     * Check if this service uses variable pricing based on pet size
     */
    fun hasVariablePricing(): Boolean = sizePrices.isNotEmpty()

    /**
     * Check if this service uses min/max pricing
     */
    fun hasMinMaxPricing(): Boolean = minPrice != null && maxPrice != null

    /**
     * Check if this service uses size-based min/max pricing
     */
    fun hasSizeBasedMinMaxPricing(): Boolean = sizeMinPrices != null && sizeMaxPrices != null

    /**
     * Get price for specific pet size, fallback to base price if not found
     */
    fun getPriceForSize(petSize: String): Money {
        return sizePrices[petSize.uppercase()] ?: basePrice
    }

    /**
     * Get duration for specific pet size, fallback to base duration if not found
     */
    fun getDurationForSize(petSize: String): Duration {
        return sizeDurations[petSize.uppercase()] ?: duration
    }

    /**
     * Get all available pet sizes for this service
     */
    fun getAvailableSizes(): Set<String> = sizePrices.keys

    /**
     * Get min price for specific pet size, fallback to general min price if not found
     */
    fun getMinPriceForSize(petSize: String): Money? {
        return sizeMinPrices?.get(petSize.uppercase()) ?: minPrice
    }

    /**
     * Get max price for specific pet size, fallback to general max price if not found
     */
    fun getMaxPriceForSize(petSize: String): Money? {
        return sizeMaxPrices?.get(petSize.uppercase()) ?: maxPrice
    }

    /**
     * Get price range for specific pet size
     */
    fun getPriceRangeForSize(petSize: String): Pair<Money?, Money?> {
        return Pair(getMinPriceForSize(petSize), getMaxPriceForSize(petSize))
    }

    /**
     * Validate pricing configuration
     */
    fun validatePricing(): Boolean {
        // If variable pricing is used, ensure all sizes have both price and duration
        if (hasVariablePricing()) {
            if (sizePrices.keys != sizeDurations.keys || sizePrices.isEmpty()) {
                return false
            }
        }

        // If min/max pricing is used, ensure min <= max
        if (hasMinMaxPricing()) {
            if (minPrice!! > maxPrice!!) {
                return false
            }
        }

        // If size-based min/max pricing is used, validate each size
        if (hasSizeBasedMinMaxPricing()) {
            val minPrices = sizeMinPrices!!
            val maxPrices = sizeMaxPrices!!

            // Ensure same keys for min and max prices
            if (minPrices.keys != maxPrices.keys) {
                return false
            }

            // Ensure min <= max for each size
            for (size in minPrices.keys) {
                val min = minPrices[size]!!
                val max = maxPrices[size]!!
                if (min > max) {
                    return false
                }
            }
        }

        return true
    }
}

