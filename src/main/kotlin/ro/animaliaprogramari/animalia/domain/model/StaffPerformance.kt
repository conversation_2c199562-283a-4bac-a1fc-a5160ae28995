package ro.animaliaprogramari.animalia.domain.model

import java.time.LocalDateTime

/**
 * Staff performance metrics
 */
data class StaffPerformance(
    val averageRating: Double,
    val totalAppointments: Int,
    val completionRate: Double,
    val clientSatisfactionScore: Double,
    val lastUpdated: LocalDateTime
) {
    init {
        require(averageRating in 0.0..5.0) { "Average rating must be between 0.0 and 5.0" }
        require(totalAppointments >= 0) { "Total appointments cannot be negative" }
        require(completionRate in 0.0..1.0) { "Completion rate must be between 0.0 and 1.0" }
        require(clientSatisfactionScore in 0.0..5.0) { "Client satisfaction score must be between 0.0 and 5.0" }
    }

    companion object {
        fun initial(): StaffPerformance = StaffPerformance(
            averageRating = 0.0,
            totalAppointments = 0,
            completionRate = 0.0,
            clientSatisfactionScore = 0.0,
            lastUpdated = LocalDateTime.now()
        )
    }
}
