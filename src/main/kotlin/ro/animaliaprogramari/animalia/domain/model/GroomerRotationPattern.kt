package ro.animaliaprogramari.animalia.domain.model

import java.time.LocalDate
import java.time.LocalTime

/**
 * Value object representing a rotation pattern for groomer scheduling
 */
data class GroomerRotationPattern(
    val patternName: String,
    val cycleLengthDays: Int,
    val startDate: LocalDate,
    val patternSequence: List<RotationDay>,
    val defaultWorkingHours: GroomerDailySchedule
) {

    init {
        require(patternName.isNotBlank()) { "Pattern name cannot be blank" }
        require(patternName.length <= 255) { "Pattern name cannot exceed 255 characters" }
        require(cycleLengthDays > 0) { "Cycle length must be positive" }
        require(cycleLengthDays <= 365) { "Cycle length cannot exceed 365 days" }
        require(patternSequence.size == cycleLengthDays) { "Pattern sequence must match cycle length" }
        require(defaultWorkingHours.isWorkingDay) { "Default working hours must be for a working day" }
    }

    /**
     * Check if groomer is working on a specific date
     */
    fun isWorkingDay(date: LocalDate): Boolean {
        val dayIndex = calculateDayIndex(date)
        return patternSequence[dayIndex] == RotationDay.WORK
    }

    /**
     * Get working hours for a specific date
     */
    fun getWorkingHoursFor(date: LocalDate): GroomerDailySchedule? {
        return if (isWorkingDay(date)) {
            defaultWorkingHours
        } else {
            null
        }
    }

    /**
     * Get the next working day from a given date
     */
    fun getNextWorkingDay(fromDate: LocalDate): LocalDate? {
        var currentDate = fromDate.plusDays(1)
        val maxDaysToCheck = cycleLengthDays * 2 // Check up to 2 full cycles
        
        repeat(maxDaysToCheck) {
            if (isWorkingDay(currentDate)) {
                return currentDate
            }
            currentDate = currentDate.plusDays(1)
        }
        
        return null
    }

    /**
     * Get working days in a date range
     */
    fun getWorkingDaysInRange(startDate: LocalDate, endDate: LocalDate): List<LocalDate> {
        val workingDays = mutableListOf<LocalDate>()
        var currentDate = startDate
        
        while (!currentDate.isAfter(endDate)) {
            if (isWorkingDay(currentDate)) {
                workingDays.add(currentDate)
            }
            currentDate = currentDate.plusDays(1)
        }
        
        return workingDays
    }

    /**
     * Calculate the index in the pattern sequence for a given date
     */
    private fun calculateDayIndex(date: LocalDate): Int {
        val daysSinceStart = java.time.temporal.ChronoUnit.DAYS.between(startDate, date)
        return (daysSinceStart % cycleLengthDays).toInt()
    }

    /**
     * Get pattern statistics
     */
    fun getPatternStatistics(): RotationPatternStatistics {
        val workDays = patternSequence.count { it == RotationDay.WORK }
        val offDays = patternSequence.count { it == RotationDay.OFF }
        val workPercentage = (workDays.toDouble() / cycleLengthDays) * 100
        
        return RotationPatternStatistics(
            cycleLengthDays = cycleLengthDays,
            workDays = workDays,
            offDays = offDays,
            workPercentage = workPercentage
        )
    }

    /**
     * Validate pattern against salon working hours
     */
    fun validateAgainstSalonHours(salonWorkingHours: WorkingHoursSettings): ValidationResult {
        val errors = mutableListOf<String>()
        
        // Check a full cycle to ensure pattern is compatible with salon hours
        var testDate = startDate
        repeat(cycleLengthDays) {
            if (isWorkingDay(testDate)) {
                if (!salonWorkingHours.isOpenOn(testDate)) {
                    errors.add("Modelul de rotație include zile de lucru când salonul este închis")
                    return@repeat
                }
                
                val salonDay = salonWorkingHours.getWorkingHoursFor(testDate)
                if (salonDay != null && defaultWorkingHours.startTime != null && defaultWorkingHours.endTime != null) {
                    if (salonDay.startTime != null && salonDay.endTime != null) {
                        if (defaultWorkingHours.startTime!!.isBefore(salonDay.startTime!!) ||
                            defaultWorkingHours.endTime!!.isAfter(salonDay.endTime!!)) {
                            errors.add("Orele de lucru din modelul de rotație depășesc programul salonului")
                        }
                    }
                }
            }
            testDate = testDate.plusDays(1)
        }
        
        return if (errors.isEmpty()) {
            ValidationResult.success()
        } else {
            ValidationResult.error(errors.joinToString("; "))
        }
    }

    companion object {
        /**
         * Create a 2-on-2-off rotation pattern
         */
        fun create2On2Off(
            startDate: LocalDate,
            workingHours: GroomerDailySchedule
        ): GroomerRotationPattern {
            return GroomerRotationPattern(
                patternName = "2-2 (2 zile lucru, 2 zile libere)",
                cycleLengthDays = 4,
                startDate = startDate,
                patternSequence = listOf(RotationDay.WORK, RotationDay.WORK, RotationDay.OFF, RotationDay.OFF),
                defaultWorkingHours = workingHours
            )
        }

        /**
         * Create a 3-on-1-off rotation pattern
         */
        fun create3On1Off(
            startDate: LocalDate,
            workingHours: GroomerDailySchedule
        ): GroomerRotationPattern {
            return GroomerRotationPattern(
                patternName = "3-1 (3 zile lucru, 1 zi liberă)",
                cycleLengthDays = 4,
                startDate = startDate,
                patternSequence = listOf(RotationDay.WORK, RotationDay.WORK, RotationDay.WORK, RotationDay.OFF),
                defaultWorkingHours = workingHours
            )
        }

        /**
         * Create a 4-on-2-off rotation pattern
         */
        fun create4On2Off(
            startDate: LocalDate,
            workingHours: GroomerDailySchedule
        ): GroomerRotationPattern {
            return GroomerRotationPattern(
                patternName = "4-2 (4 zile lucru, 2 zile libere)",
                cycleLengthDays = 6,
                startDate = startDate,
                patternSequence = listOf(
                    RotationDay.WORK, RotationDay.WORK, RotationDay.WORK, RotationDay.WORK,
                    RotationDay.OFF, RotationDay.OFF
                ),
                defaultWorkingHours = workingHours
            )
        }

        /**
         * Create a custom rotation pattern
         */
        fun createCustom(
            patternName: String,
            startDate: LocalDate,
            patternSequence: List<RotationDay>,
            workingHours: GroomerDailySchedule
        ): GroomerRotationPattern {
            return GroomerRotationPattern(
                patternName = patternName,
                cycleLengthDays = patternSequence.size,
                startDate = startDate,
                patternSequence = patternSequence,
                defaultWorkingHours = workingHours
            )
        }
    }
}

/**
 * Enumeration for rotation day types
 */
enum class RotationDay {
    /**
     * Working day
     */
    WORK,
    
    /**
     * Day off
     */
    OFF;

    companion object {
        fun fromString(day: String): RotationDay {
            return valueOf(day.uppercase())
        }
    }
}

/**
 * Data class for rotation pattern statistics
 */
data class RotationPatternStatistics(
    val cycleLengthDays: Int,
    val workDays: Int,
    val offDays: Int,
    val workPercentage: Double
) {
    /**
     * Get formatted statistics string
     */
    fun getFormattedStatistics(): String {
        return "Ciclu: $cycleLengthDays zile | Lucru: $workDays zile | Libere: $offDays zile | Procent lucru: ${"%.1f".format(workPercentage)}%"
    }
}

/**
 * Validation result for pattern validation
 */
data class ValidationResult(
    val isValid: Boolean,
    val errorMessage: String? = null
) {
    companion object {
        fun success(): ValidationResult = ValidationResult(true)
        fun error(message: String): ValidationResult = ValidationResult(false, message)
    }
}
