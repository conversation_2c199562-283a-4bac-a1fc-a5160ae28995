package ro.animaliaprogramari.animalia.domain.model

import java.time.LocalDate

/**
 * Enum representing appointment repetition frequency
 */
enum class RepetitionFrequency(val days: Int) {
    WEEKLY(7),
    BI_WEEKLY(14),
    MONTHLY(30),
    QUARTERLY(90);
    
    fun getNextDate(currentDate: LocalDate): LocalDate {
        return currentDate.plusDays(days.toLong())
    }
    
    fun generateDates(startDate: LocalDate, count: Int): List<LocalDate> {
        val dates = mutableListOf<LocalDate>()
        var currentDate = startDate
        
        repeat(count) {
            currentDate = getNextDate(currentDate)
            dates.add(currentDate)
        }
        
        return dates
    }
}
