package ro.animaliaprogramari.animalia.domain.model

import java.time.LocalDate
import java.time.LocalDateTime

/**
 * Domain entity representing a custom closure
 * Pure domain model with no infrastructure dependencies
 */
data class CustomClosure(
    val id: CustomClosureId,
    val salonId: SalonId,
    val reason: String,
    val date: LocalDate,
    val description: String?,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now()
) {

    init {
        require(reason.isNotBlank()) { "Custom closure reason cannot be blank" }
        require(reason.length <= 255) { "Custom closure reason cannot exceed 255 characters" }
        require(description == null || description.length <= 1000) { "Description cannot exceed 1000 characters" }
    }

    /**
     * Update closure reason
     */
    fun updateReason(reason: String): CustomClosure {
        require(reason.isNotBlank()) { "Custom closure reason cannot be blank" }
        require(reason.length <= 255) { "Custom closure reason cannot exceed 255 characters" }
        
        return copy(
            reason = reason,
            updatedAt = LocalDateTime.now()
        )
    }

    /**
     * Update closure description
     */
    fun updateDescription(description: String?): CustomClosure {
        require(description == null || description.length <= 1000) { "Description cannot exceed 1000 characters" }
        
        return copy(
            description = description,
            updatedAt = LocalDateTime.now()
        )
    }

    /**
     * Update closure date
     */
    fun updateDate(date: LocalDate): CustomClosure {
        return copy(
            date = date,
            updatedAt = LocalDateTime.now()
        )
    }

    companion object {
        /**
         * Create a new custom closure
         */
        fun create(
            salonId: SalonId,
            reason: String,
            date: LocalDate,
            description: String? = null
        ): CustomClosure {
            return CustomClosure(
                id = CustomClosureId.generate(),
                salonId = salonId,
                reason = reason,
                date = date,
                description = description
            )
        }
    }
}

/**
 * Value object representing a custom closure ID
 */
@JvmInline
value class CustomClosureId(val value: String) {
    init {
        require(value.isNotBlank()) { "Custom closure ID cannot be blank" }
    }

    companion object {
        fun generate(): CustomClosureId = CustomClosureId(java.util.UUID.randomUUID().toString())
        fun of(value: String): CustomClosureId = CustomClosureId(value)
    }
}
