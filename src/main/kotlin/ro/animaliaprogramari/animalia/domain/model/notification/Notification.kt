package ro.animaliaprogramari.animalia.domain.model.notification

import ro.animaliaprogramari.animalia.domain.model.AppointmentId
import ro.animaliaprogramari.animalia.domain.model.PhoneNumber
import ro.animaliaprogramari.animalia.domain.model.SalonId
import ro.animaliaprogramari.animalia.domain.model.UserId
import java.time.LocalDateTime

/**
 * Domain entity representing a notification
 */
data class Notification(
    val id: NotificationId,
    val type: SmsNotificationType,
    val recipient: NotificationRecipient,
    val content: NotificationContent,
    val status: NotificationStatus,
    val appointmentId: AppointmentId?,
    val salonId: SalonId,
    val sentAt: LocalDateTime?,
    val deliveredAt: LocalDateTime?,
    val failureReason: String?,
    val retryCount: Int = 0,
    val maxRetries: Int = 3,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now()
) {
    
    /**
     * Mark notification as sent
     */
    fun markAsSent(): Notification {
        return copy(
            status = NotificationStatus.SENT,
            sentAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now()
        )
    }
    
    /**
     * Mark notification as delivered
     */
    fun markAsDelivered(): Notification {
        return copy(
            status = NotificationStatus.DELIVERED,
            deliveredAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now()
        )
    }
    
    /**
     * Mark notification as failed
     */
    fun markAsFailed(reason: String): Notification {
        return copy(
            status = NotificationStatus.FAILED,
            failureReason = reason,
            updatedAt = LocalDateTime.now()
        )
    }
    
    /**
     * Increment retry count
     */
    fun incrementRetry(): Notification {
        return copy(
            retryCount = retryCount + 1,
            status = NotificationStatus.PENDING,
            updatedAt = LocalDateTime.now()
        )
    }
    
    /**
     * Check if notification can be retried
     */
    fun canRetry(): Boolean {
        return retryCount < maxRetries && status == NotificationStatus.FAILED
    }
    
    companion object {
        /**
         * Create SMS notification
         */
        fun createSms(
            recipient: PhoneNumber,
            message: String,
            appointmentId: AppointmentId?,
            salonId: SalonId
        ): Notification {
            return Notification(
                id = NotificationId.generate(),
                type = SmsNotificationType.SMS,
                recipient = NotificationRecipient.Phone(recipient),
                content = NotificationContent.Text(message),
                status = NotificationStatus.PENDING,
                appointmentId = appointmentId,
                salonId = salonId,
                sentAt = null,
                deliveredAt = null,
                failureReason = null
            )
        }
        
        /**
         * Create push notification
         */
        fun createPush(
            recipient: UserId,
            title: String,
            body: String,
            data: Map<String, String> = emptyMap(),
            appointmentId: AppointmentId?,
            salonId: SalonId
        ): Notification {
            return Notification(
                id = NotificationId.generate(),
                type = SmsNotificationType.PUSH,
                recipient = NotificationRecipient.User(recipient),
                content = NotificationContent.Push(title, body, data),
                status = NotificationStatus.PENDING,
                appointmentId = appointmentId,
                salonId = salonId,
                sentAt = null,
                deliveredAt = null,
                failureReason = null
            )
        }
    }
}

/**
 * SMS and Push notification types
 */
enum class SmsNotificationType {
    SMS,
    PUSH,
    EMAIL
}

/**
 * Notification status
 */
enum class NotificationStatus {
    PENDING,
    SENT,
    DELIVERED,
    FAILED,
    CANCELLED
}

/**
 * Notification recipient
 */
sealed class NotificationRecipient {
    data class Phone(val phoneNumber: PhoneNumber) : NotificationRecipient()
    data class User(val userId: UserId) : NotificationRecipient()
    data class Email(val email: ro.animaliaprogramari.animalia.domain.model.Email) : NotificationRecipient()
}

/**
 * Notification content
 */
sealed class NotificationContent {
    data class Text(val message: String) : NotificationContent()
    data class Push(
        val title: String,
        val body: String,
        val data: Map<String, String> = emptyMap()
    ) : NotificationContent()
    data class Email(
        val subject: String,
        val body: String,
        val isHtml: Boolean = false
    ) : NotificationContent()
}

/**
 * Value object for notification ID
 */
@JvmInline
value class NotificationId(val value: String) {
    companion object {
        fun generate(): NotificationId = NotificationId(java.util.UUID.randomUUID().toString())
        fun of(value: String): NotificationId = NotificationId(value)
    }
    
    override fun toString(): String = value
}
