package ro.animaliaprogramari.animalia.domain.model

import org.slf4j.LoggerFactory
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime

/**
 * Domain entity representing working hours settings for a salon
 * Pure domain model with no infrastructure dependencies
 */
data class WorkingHoursSettings(
    val salonId: SalonId,
    val weeklySchedule: Map<DayOfWeek, DaySchedule>,
    val holidays: List<Holiday>,
    val customClosures: List<CustomClosure>,
    val updatedAt: LocalDateTime = LocalDateTime.now()
) {

    private val logger = LoggerFactory.getLogger(WorkingHoursSettings::class.java)

    init {
        require(weeklySchedule.isNotEmpty()) { "Weekly schedule must have at least one day defined" }
        require(weeklySchedule.size == 7) { "Weekly schedule must have all 7 days defined" }
    }

    /**
     * Check if salon is open on a specific date
     */
    fun isOpenOn(date: LocalDate): Boolean {
        val dayOfWeek = date.dayOfWeek
        logger.debug("Checking if salon {} is open on {} ({})", salonId.value, date, dayOfWeek)

        // Check if it's a custom closure
        val customClosure = customClosures.find { it.date == date }
        if (customClosure != null) {
            logger.debug("Salon {} has custom closure on {}: {}", salonId.value, date, customClosure.reason)
            return false
        }

        // Check if it's a holiday (and not marked as working day)
        val holiday = holidays.find { it.date == date }
        if (holiday != null && !holiday.isWorkingDay) {
            logger.debug("Salon {} has holiday on {} (not working): {}", salonId.value, date, holiday.name)
            return false
        }

        // Check weekly schedule
        val daySchedule = weeklySchedule[dayOfWeek]
        if (daySchedule == null) {
            logger.debug("Salon {} has no schedule configured for {}", salonId.value, dayOfWeek)
            return false
        }

        val isOpen = daySchedule.isWorkingDay
        logger.debug("Salon {} weekly schedule for {}: isWorkingDay={}, hours={}-{}",
            salonId.value, dayOfWeek, isOpen, daySchedule.startTime, daySchedule.endTime)
        return isOpen
    }

    /**
     * Get working hours for a specific date
     */
    fun getWorkingHoursFor(date: LocalDate): DaySchedule? {
        if (!isOpenOn(date)) {
            return null
        }

        val dayOfWeek = date.dayOfWeek
        return weeklySchedule[dayOfWeek]
    }

    /**
     * Get next working day from a given date
     */
    fun getNextWorkingDay(fromDate: LocalDate): LocalDate? {
        var currentDate = fromDate.plusDays(1)
        val maxDaysToCheck = 30 // Prevent infinite loop

        repeat(maxDaysToCheck) {
            if (isOpenOn(currentDate)) {
                return currentDate
            }
            currentDate = currentDate.plusDays(1)
        }

        return null
    }

    /**
     * Update weekly schedule
     */
    fun updateWeeklySchedule(newSchedule: Map<DayOfWeek, DaySchedule>): WorkingHoursSettings {
        require(newSchedule.size == 7) { "Weekly schedule must have all 7 days defined" }

        return copy(
            weeklySchedule = newSchedule,
            updatedAt = LocalDateTime.now()
        )
    }

    /**
     * Update holidays
     */
    fun updateHolidays(newHolidays: List<Holiday>): WorkingHoursSettings {
        return copy(
            holidays = newHolidays,
            updatedAt = LocalDateTime.now()
        )
    }

    /**
     * Update custom closures
     */
    fun updateCustomClosures(newClosures: List<CustomClosure>): WorkingHoursSettings {
        return copy(
            customClosures = newClosures,
            updatedAt = LocalDateTime.now()
        )
    }

    companion object {
        /**
         * Create default working hours settings for a salon
         */
        fun createDefault(salonId: SalonId): WorkingHoursSettings {
            val defaultSchedule = mapOf(
                DayOfWeek.MONDAY to DaySchedule.workingDay(LocalTime.of(9, 0), LocalTime.of(17, 0), LocalTime.of(12, 0), LocalTime.of(13, 0)),
                DayOfWeek.TUESDAY to DaySchedule.workingDay(LocalTime.of(9, 0), LocalTime.of(17, 0), LocalTime.of(12, 0), LocalTime.of(13, 0)),
                DayOfWeek.WEDNESDAY to DaySchedule.workingDay(LocalTime.of(9, 0), LocalTime.of(17, 0), LocalTime.of(12, 0), LocalTime.of(13, 0)),
                DayOfWeek.THURSDAY to DaySchedule.workingDay(LocalTime.of(9, 0), LocalTime.of(17, 0), LocalTime.of(12, 0), LocalTime.of(13, 0)),
                DayOfWeek.FRIDAY to DaySchedule.workingDay(LocalTime.of(9, 0), LocalTime.of(17, 0), LocalTime.of(12, 0), LocalTime.of(13, 0)),
                DayOfWeek.SATURDAY to DaySchedule.workingDay(LocalTime.of(10, 0), LocalTime.of(15, 0)),
                DayOfWeek.SUNDAY to DaySchedule.dayOff()
            )

            return WorkingHoursSettings(
                salonId = salonId,
                weeklySchedule = defaultSchedule,
                holidays = emptyList(),
                customClosures = emptyList()
            )
        }
    }
}
