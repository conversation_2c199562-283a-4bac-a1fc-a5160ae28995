package ro.animaliaprogramari.animalia.domain.model

import java.time.LocalDate
import java.time.LocalDateTime

/**
 * Domain entity representing a holiday for a staff member
 * Pure domain model with no infrastructure dependencies
 */
data class StaffHoliday(
    val id: StaffHolidayId,
    val salonId: SalonId,
    val name: String,
    val date: LocalDate,
    val isWorkingDay: Boolean,
    val type: HolidayType,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now()
) {

    init {
        require(name.isNotBlank()) { "Holiday name cannot be blank" }
        require(name.length <= 255) { "Holiday name cannot exceed 255 characters" }
    }

    /**
     * Update holiday working status
     */
    fun updateWorkingStatus(isWorkingDay: Boolean): StaffHoliday {
        return copy(
            isWorkingDay = isWorkingDay,
            updatedAt = LocalDateTime.now()
        )
    }

    /**
     * Update holiday name
     */
    fun updateName(name: String): StaffHoliday {
        require(name.isNotBlank()) { "Holiday name cannot be blank" }
        require(name.length <= 255) { "Holiday name cannot exceed 255 characters" }
        
        return copy(
            name = name,
            updatedAt = LocalDateTime.now()
        )
    }

    companion object {
        /**
         * Create a new holiday
         */
        fun create(
            salonId: SalonId,
            name: String,
            date: LocalDate,
            isWorkingDay: Boolean = false,
            type: HolidayType = HolidayType.LEGAL
        ): StaffHoliday {
            return StaffHoliday(
                id = StaffHolidayId.generate(),
                salonId = salonId,
                name = name,
                date = date,
                isWorkingDay = isWorkingDay,
                type = type
            )
        }
    }
}

/**
 * Value object representing a staff holiday ID
 */
@JvmInline
value class StaffHolidayId(val value: String) {
    init {
        require(value.isNotBlank()) { "Staff holiday ID cannot be blank" }
    }

    companion object {
        fun generate(): StaffHolidayId = StaffHolidayId(java.util.UUID.randomUUID().toString())
        fun of(value: String): StaffHolidayId = StaffHolidayId(value)
    }
}

/**
 * Domain entity representing a custom closure for a staff member
 * Pure domain model with no infrastructure dependencies
 */
data class StaffCustomClosure(
    val id: StaffCustomClosureId,
    val salonId: SalonId,
    val reason: String,
    val date: LocalDate,
    val description: String?,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now()
) {

    init {
        require(reason.isNotBlank()) { "Custom closure reason cannot be blank" }
        require(reason.length <= 255) { "Custom closure reason cannot exceed 255 characters" }
        require(description == null || description.length <= 1000) { "Description cannot exceed 1000 characters" }
    }

    /**
     * Update closure reason
     */
    fun updateReason(reason: String): StaffCustomClosure {
        require(reason.isNotBlank()) { "Custom closure reason cannot be blank" }
        require(reason.length <= 255) { "Custom closure reason cannot exceed 255 characters" }
        
        return copy(
            reason = reason,
            updatedAt = LocalDateTime.now()
        )
    }

    /**
     * Update closure description
     */
    fun updateDescription(description: String?): StaffCustomClosure {
        require(description == null || description.length <= 1000) { "Description cannot exceed 1000 characters" }
        
        return copy(
            description = description,
            updatedAt = LocalDateTime.now()
        )
    }

    /**
     * Update closure date
     */
    fun updateDate(date: LocalDate): StaffCustomClosure {
        return copy(
            date = date,
            updatedAt = LocalDateTime.now()
        )
    }

    companion object {
        /**
         * Create a new custom closure
         */
        fun create(
            salonId: SalonId,
            reason: String,
            date: LocalDate,
            description: String? = null
        ): StaffCustomClosure {
            return StaffCustomClosure(
                id = StaffCustomClosureId.generate(),
                salonId = salonId,
                reason = reason,
                date = date,
                description = description
            )
        }
    }
}

/**
 * Value object representing a staff custom closure ID
 */
@JvmInline
value class StaffCustomClosureId(val value: String) {
    init {
        require(value.isNotBlank()) { "Staff custom closure ID cannot be blank" }
    }

    companion object {
        fun generate(): StaffCustomClosureId = StaffCustomClosureId(java.util.UUID.randomUUID().toString())
        fun of(value: String): StaffCustomClosureId = StaffCustomClosureId(value)
    }
}

/**
 * Domain model representing staff availability report
 */
data class StaffAvailabilityReport(
    val date: LocalDate,
    val staffAvailability: List<StaffMemberAvailability>
)

/**
 * Domain model representing individual staff member availability
 */
data class StaffMemberAvailability(
    val staff: Staff,
    val isAvailable: Boolean,
    val workingHours: DaySchedule?,
    val conflictingAppointments: List<Appointment>,
    val reason: String?
)
