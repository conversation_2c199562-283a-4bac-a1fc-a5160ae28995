package ro.animaliaprogramari.animalia.domain.model

import java.time.LocalTime

/**
 * Value object representing a groomer's schedule for a single day
 */
data class GroomerDailySchedule(
    val isWorkingDay: Boolean,
    val startTime: LocalTime?,
    val endTime: LocalTime?,
    val breakStartTime: LocalTime?,
    val breakEndTime: LocalTime?,
    val breakDurationMinutes: Int = 60,
    val notes: String? = null
) {

    init {
        if (isWorkingDay) {
            require(startTime != null) { "Working day must have start time" }
            require(endTime != null) { "Working day must have end time" }
            require(startTime.isBefore(endTime)) { "Start time must be before end time" }

            // Validate break times if present
            if (breakStartTime != null && breakEndTime != null) {
                require(breakStartTime.isBefore(breakEndTime)) { "Break start must be before break end" }
                require(!breakStartTime.isBefore(startTime)) { "Break start cannot be before work start" }
                require(!breakEndTime.isAfter(endTime)) { "Break end cannot be after work end" }
                require(breakDurationMinutes > 0) { "Break duration must be positive" }
            } else if (breakStartTime != null || breakEndTime != null) {
                require(false) { "Both break start and end times must be specified" }
            }
        } else {
            require(startTime == null && endTime == null) { "Non-working day cannot have working hours" }
            require(breakStartTime == null && breakEndTime == null) { "Non-working day cannot have break times" }
        }

        require(notes == null || notes.length <= 500) { "Notes cannot exceed 500 characters" }
    }

    /**
     * Check if available at a specific time range
     */
    fun isAvailableAt(requestStartTime: LocalTime, requestEndTime: LocalTime): Boolean {
        if (!isWorkingDay || startTime == null || endTime == null) {
            return false
        }

        // Check if requested time is within working hours
        if (requestStartTime.isBefore(startTime) || requestEndTime.isAfter(endTime)) {
            return false
        }

        // Check if requested time overlaps with break
        if (breakStartTime != null && breakEndTime != null) {
            // Check if any part of the requested time overlaps with break time
            if (!(requestEndTime.isBefore(breakStartTime) || requestStartTime.isAfter(breakEndTime))) {
                return false
            }
        }

        return true
    }

    /**
     * Get total working minutes for the day (excluding break)
     */
    fun getTotalWorkingMinutes(): Int {
        if (!isWorkingDay || startTime == null || endTime == null) {
            return 0
        }

        val totalMinutes = java.time.Duration.between(startTime!!, endTime!!).toMinutes().toInt()
        val breakMinutes = if (breakStartTime != null && breakEndTime != null) {
            java.time.Duration.between(breakStartTime!!, breakEndTime!!).toMinutes().toInt()
        } else {
            0
        }

        return totalMinutes - breakMinutes
    }

    /**
     * Get available time slots for appointments
     */
    fun getAvailableSlots(
        slotDurationMinutes: Int,
        existingAppointments: List<TimeSlot> = emptyList()
    ): List<TimeSlot> {
        if (!isWorkingDay || startTime == null || endTime == null) {
            return emptyList()
        }

        val slots = mutableListOf<TimeSlot>()
        var currentTime = startTime!!

        while (currentTime.plusMinutes(slotDurationMinutes.toLong()).isBefore(endTime!!) ||
               currentTime.plusMinutes(slotDurationMinutes.toLong()) == endTime!!) {

            val slotEnd = currentTime.plusMinutes(slotDurationMinutes.toLong())

            // Check if slot is available (not during break and not conflicting with appointments)
            if (isAvailableAt(currentTime, slotEnd) &&
                !hasConflictWithAppointments(currentTime, slotEnd, existingAppointments)) {
                slots.add(TimeSlot(currentTime, slotEnd))
            }

            currentTime = currentTime.plusMinutes(15) // 15-minute intervals
        }

        return slots
    }

    /**
     * Check if time range conflicts with existing appointments
     */
    private fun hasConflictWithAppointments(
        startTime: LocalTime,
        endTime: LocalTime,
        appointments: List<TimeSlot>
    ): Boolean {
        return appointments.any { appointment ->
            !(endTime.isBefore(appointment.startTime) || startTime.isAfter(appointment.endTime))
        }
    }

    /**
     * Get formatted working hours string
     */
    fun getFormattedHours(): String {
        return if (isWorkingDay && startTime != null && endTime != null) {
            val timeFormat = java.time.format.DateTimeFormatter.ofPattern("HH:mm")
            val hours = "${startTime.format(timeFormat)} - ${endTime.format(timeFormat)}"

            if (breakStartTime != null && breakEndTime != null) {
                "$hours (Pauză: ${breakStartTime.format(timeFormat)} - ${breakEndTime.format(timeFormat)})"
            } else {
                hours
            }
        } else {
            "Zi liberă"
        }
    }

    /**
     * Update working hours
     */
    fun updateWorkingHours(newStartTime: LocalTime, newEndTime: LocalTime): GroomerDailySchedule {
        require(newStartTime.isBefore(newEndTime)) { "Start time must be before end time" }

        return copy(
            isWorkingDay = true,
            startTime = newStartTime,
            endTime = newEndTime
        )
    }

    /**
     * Update break times
     */
    fun updateBreakTimes(newBreakStart: LocalTime?, newBreakEnd: LocalTime?): GroomerDailySchedule {
        if (newBreakStart != null && newBreakEnd != null) {
            require(isWorkingDay) { "Cannot set break times for non-working day" }
            require(startTime != null && endTime != null) { "Working hours must be set before break times" }
            require(newBreakStart.isBefore(newBreakEnd)) { "Break start must be before break end" }
            require(!newBreakStart.isBefore(startTime!!)) { "Break start cannot be before work start" }
            require(!newBreakEnd.isAfter(endTime!!)) { "Break end cannot be after work end" }
        }

        return copy(
            breakStartTime = newBreakStart,
            breakEndTime = newBreakEnd
        )
    }

    companion object {
        /**
         * Create a working day schedule
         */
        fun workingDay(
            startTime: LocalTime,
            endTime: LocalTime,
            breakStart: LocalTime? = null,
            breakEnd: LocalTime? = null,
            notes: String? = null
        ): GroomerDailySchedule {
            return GroomerDailySchedule(
                isWorkingDay = true,
                startTime = startTime,
                endTime = endTime,
                breakStartTime = breakStart,
                breakEndTime = breakEnd,
                notes = notes
            )
        }

        /**
         * Create a day off schedule
         */
        fun dayOff(notes: String? = null): GroomerDailySchedule {
            return GroomerDailySchedule(
                isWorkingDay = false,
                startTime = null,
                endTime = null,
                breakStartTime = null,
                breakEndTime = null,
                notes = notes
            )
        }
    }
}