package ro.animaliaprogramari.animalia.domain.model.notification

import ro.animaliaprogramari.animalia.domain.model.SalonId
import ro.animaliaprogramari.animalia.domain.model.UserId
import java.time.LocalDateTime

/**
 * Domain entity representing an FCM token for push notifications
 */
data class FcmToken(
    val id: FcmTokenId,
    val userId: UserId,
    val salonId: SalonId,
    val token: String,
    val deviceId: String?,
    val deviceType: DeviceType,
    val isActive: Boolean = true,
    val lastUsed: LocalDateTime = LocalDateTime.now(),
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now()
) {
    
    init {
        require(token.isNotBlank()) { "FCM token cannot be blank" }
    }
    
    /**
     * Update the token (when app refreshes FCM token)
     */
    fun updateToken(newToken: String): FcmToken {
        require(newToken.isNotBlank()) { "FCM token cannot be blank" }
        return copy(
            token = newToken,
            lastUsed = LocalDateTime.now(),
            updatedAt = LocalDateTime.now()
        )
    }
    
    /**
     * Mark token as used (when notification is sent)
     */
    fun markAsUsed(): FcmToken {
        return copy(
            lastUsed = LocalDateTime.now(),
            updatedAt = LocalDateTime.now()
        )
    }
    
    /**
     * Deactivate the token (when device unregisters)
     */
    fun deactivate(): FcmToken {
        return copy(
            isActive = false,
            updatedAt = LocalDateTime.now()
        )
    }
    
    /**
     * Reactivate the token
     */
    fun reactivate(): FcmToken {
        return copy(
            isActive = true,
            lastUsed = LocalDateTime.now(),
            updatedAt = LocalDateTime.now()
        )
    }
    
    /**
     * Check if token is expired (not used for more than 60 days)
     */
    fun isExpired(): Boolean {
        return lastUsed.isBefore(LocalDateTime.now().minusDays(60))
    }
    
    companion object {
        /**
         * Create a new FCM token
         */
        fun create(
            userId: UserId,
            salonId: SalonId,
            token: String,
            deviceId: String? = null,
            deviceType: DeviceType = DeviceType.MOBILE
        ): FcmToken {
            return FcmToken(
                id = FcmTokenId.generate(),
                userId = userId,
                salonId = salonId,
                token = token,
                deviceId = deviceId,
                deviceType = deviceType
            )
        }
    }
}

/**
 * Device type for FCM tokens
 */
enum class DeviceType {
    MOBILE,
    TABLET,
    WEB,
    DESKTOP
}

/**
 * Value object for FCM token ID
 */
@JvmInline
value class FcmTokenId(val value: String) {
    companion object {
        fun generate(): FcmTokenId = FcmTokenId(java.util.UUID.randomUUID().toString())
        fun of(value: String): FcmTokenId = FcmTokenId(value)
    }
    
    override fun toString(): String = value
}
