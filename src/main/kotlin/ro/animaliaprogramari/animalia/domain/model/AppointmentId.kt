package ro.animaliaprogramari.animalia.domain.model

import java.util.UUID

/**
 * Value object representing a unique appointment identifier
 */
@JvmInline
value class AppointmentId(val value: String) {
    
    init {
        require(value.isNotBlank()) { "Appointment ID cannot be blank" }
    }
    
    companion object {
        fun generate(): AppointmentId = AppointmentId(UUID.randomUUID().toString())
        
        fun of(value: String): AppointmentId = AppointmentId(value)
    }
    
    override fun toString(): String = value
}
