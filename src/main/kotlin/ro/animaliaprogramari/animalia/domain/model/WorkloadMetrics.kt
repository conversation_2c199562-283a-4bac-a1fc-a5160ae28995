package ro.animaliaprogramari.animalia.domain.model

import java.time.LocalDateTime

/**
 * Workload metrics for reporting
 */
data class WorkloadMetrics(
    val totalAppointments: Int,
    val totalHours: Double,
    val averageAppointmentsPerDay: Double,
    val utilizationRate: Double,
    val peakDayAppointments: Int,
    val calculatedAt: LocalDateTime = LocalDateTime.now()
) {
    init {
        require(totalAppointments >= 0) { "Total appointments cannot be negative" }
        require(totalHours >= 0.0) { "Total hours cannot be negative" }
        require(averageAppointmentsPerDay >= 0.0) { "Average appointments per day cannot be negative" }
        require(utilizationRate in 0.0..1.0) { "Utilization rate must be between 0.0 and 1.0" }
        require(peakDayAppointments >= 0) { "Peak day appointments cannot be negative" }
    }

    companion object {
        fun empty(): WorkloadMetrics = WorkloadMetrics(
            totalAppointments = 0,
            totalHours = 0.0,
            averageAppointmentsPerDay = 0.0,
            utilizationRate = 0.0,
            peakDayAppointments = 0
        )
    }
}
