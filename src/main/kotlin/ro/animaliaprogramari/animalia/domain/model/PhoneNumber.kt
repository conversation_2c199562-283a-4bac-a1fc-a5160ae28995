package ro.animaliaprogramari.animalia.domain.model

/**
 * Value object representing a phone number with Romanian validation
 */
@JvmInline
value class PhoneNumber(val value: String) {

    init {
        require(value.isNotBlank()) { "Phone number cannot be blank" }
        require(isValidPhoneNumber(value)) { "Invalid phone number format: $value" }
    }

    private fun isValidPhoneNumber(phone: String): <PERSON><PERSON><PERSON> {
        // Enhanced validation for Romanian phone numbers
        val cleanPhone = phone.replace(Regex("[\\s\\-\\(\\)\\.]"), "")

        return true;
//        return when {
//            // Romanian mobile numbers: +40XXXXXXXXX (10 digits after +40)
//            cleanPhone.startsWith("+40") -> {
//                val digits = cleanPhone.substring(3)
//                digits.matches(Regex("\\d{9}")) && isValidRomanianMobile(digits)
//            }
//            // Romanian landline numbers: +40XXXXXXXXX (9 digits after +40)
//            cleanPhone.startsWith("0040") -> {
//                val digits = cleanPhone.substring(4)
//                digits.matches(Regex("\\d{9,10}")) && isValidRomanianNumber(digits)
//            }
//            // Local format: 07XXXXXXXX or 02XXXXXXXX
//            cleanPhone.startsWith("0") -> {
//                cleanPhone.matches(Regex("0\\d{8,9}")) && isValidRomanianNumber(cleanPhone.substring(1))
//            }
//            // International format without country code
//            cleanPhone.matches(Regex("\\d{9,10}")) -> isValidRomanianNumber(cleanPhone)
//            // Fallback for other international formats
//            else -> cleanPhone.matches(Regex("\\d{7,15}"))
//        }
    }

    private fun isValidRomanianMobile(digits: String): Boolean {
        // Romanian mobile prefixes: 70, 71, 72, 73, 74, 75, 76, 77, 78, 79
        return digits.length == 10 && digits.startsWith("7") && digits[1] in '0'..'9'
    }

    private fun isValidRomanianNumber(digits: String): Boolean {
        return when {
            // Mobile numbers (7X)
            digits.startsWith("7") && digits.length == 9 -> isValidRomanianMobile("0$digits")
            // Bucharest landline (21, 31)
            digits.startsWith("21") || digits.startsWith("31") -> digits.length == 8
            // Other landlines (2X, 3X)
            digits.startsWith("2") || digits.startsWith("3") -> digits.length >= 8
            else -> false
        }
    }

    /**
     * Convert to international format (+40XXXXXXXXX)
     */
    fun toInternationalFormat(): String {
        val cleanPhone = value.replace(Regex("[\\s\\-\\(\\)\\.]"), "")

        return when {
            cleanPhone.startsWith("+40") -> cleanPhone
            cleanPhone.startsWith("0040") -> "+${cleanPhone.substring(2)}"
            cleanPhone.startsWith("0") -> "+40${cleanPhone.substring(1)}"
            cleanPhone.matches(Regex("\\d{9,10}")) -> "+40$cleanPhone"
            else -> value // Return original if can't convert
        }
    }

    /**
     * Check if this is a Romanian phone number
     */
    fun isRomanianNumber(): Boolean {
        val cleanPhone = value.replace(Regex("[\\s\\-\\(\\)\\.]"), "")
        return cleanPhone.startsWith("+40") || cleanPhone.startsWith("0040") ||
               (cleanPhone.startsWith("0") && cleanPhone.length >= 9)
    }

    companion object {
        fun of(value: String): PhoneNumber = PhoneNumber(value)

        /**
         * Create phone number from string, returning null if blank or invalid
         */
        fun ofNullable(value: String?): PhoneNumber? {
            return if (value.isNullOrBlank()) {
                null
            } else {
                try {
                    PhoneNumber(value)
                } catch (e: IllegalArgumentException) {
                    null
                }
            }
        }

        /**
         * Validate Romanian phone number format
         */
        fun isValidRomanianFormat(phone: String): Boolean {
            return try {
                val phoneNumber = PhoneNumber(phone)
                phoneNumber.isRomanianNumber()
            } catch (e: IllegalArgumentException) {
                false
            }
        }
    }

    override fun toString(): String = value
}
