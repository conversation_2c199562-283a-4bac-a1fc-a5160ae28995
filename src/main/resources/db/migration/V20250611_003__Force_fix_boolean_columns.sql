-- Aggressive migration to force fix boolean column types
-- This will drop and recreate columns if necessary to ensure proper boolean type

DO $$
DECLARE
    table_name_var TEXT;
    column_exists BOOLEAN;
BEGIN
    -- Array of tables to fix
    FOR table_name_var IN SELECT unnest(ARRAY['clients', 'users', 'pets', 'salons', 'staff'])
    LOOP
        -- Check if table exists
        IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = table_name_var) THEN
            
            -- Check if is_active column exists
            SELECT EXISTS (
                SELECT 1 FROM information_schema.columns 
                WHERE table_name = table_name_var AND column_name = 'is_active'
            ) INTO column_exists;
            
            IF column_exists THEN
                -- Get current data type
                DECLARE
                    current_type TEXT;
                BEGIN
                    SELECT data_type INTO current_type
                    FROM information_schema.columns 
                    WHERE table_name = table_name_var AND column_name = 'is_active';
                    
                    -- If not boolean, fix it
                    IF current_type != 'boolean' THEN
                        RAISE NOTICE 'Fixing is_active column in table % (current type: %)', table_name_var, current_type;
                        
                        -- First, try to update problematic values
                        EXECUTE format('UPDATE %I SET is_active = true WHERE is_active IS NULL', table_name_var);
                        
                        -- Try direct conversion first
                        BEGIN
                            EXECUTE format('ALTER TABLE %I ALTER COLUMN is_active TYPE BOOLEAN USING 
                                CASE 
                                    WHEN is_active::text IN (''true'', ''t'', ''1'', ''yes'', ''y'') THEN true
                                    WHEN is_active::text IN (''false'', ''f'', ''0'', ''no'', ''n'') THEN false
                                    ELSE true
                                END', table_name_var);
                            
                            EXECUTE format('ALTER TABLE %I ALTER COLUMN is_active SET DEFAULT true', table_name_var);
                            EXECUTE format('ALTER TABLE %I ALTER COLUMN is_active SET NOT NULL', table_name_var);
                            
                            RAISE NOTICE 'Successfully converted is_active column in table %', table_name_var;
                            
                        EXCEPTION WHEN OTHERS THEN
                            -- If conversion fails, drop and recreate column
                            RAISE NOTICE 'Direct conversion failed for table %, dropping and recreating column', table_name_var;
                            
                            -- Drop the problematic column
                            EXECUTE format('ALTER TABLE %I DROP COLUMN is_active', table_name_var);
                            
                            -- Add it back as proper boolean
                            EXECUTE format('ALTER TABLE %I ADD COLUMN is_active BOOLEAN NOT NULL DEFAULT true', table_name_var);
                            
                            RAISE NOTICE 'Recreated is_active column in table %', table_name_var;
                        END;
                    ELSE
                        RAISE NOTICE 'Table % already has correct boolean type for is_active', table_name_var;
                    END IF;
                END;
            ELSE
                -- Column doesn't exist, create it
                EXECUTE format('ALTER TABLE %I ADD COLUMN is_active BOOLEAN NOT NULL DEFAULT true', table_name_var);
                RAISE NOTICE 'Added is_active column to table %', table_name_var;
            END IF;
        ELSE
            RAISE NOTICE 'Table % does not exist, skipping', table_name_var;
        END IF;
    END LOOP;
    
    -- Add comments
    COMMENT ON COLUMN clients.is_active IS 'Boolean flag indicating if the client is active';
    COMMENT ON COLUMN users.is_active IS 'Boolean flag indicating if the user is active';
    COMMENT ON COLUMN pets.is_active IS 'Boolean flag indicating if the pet is active';
    COMMENT ON COLUMN salons.is_active IS 'Boolean flag indicating if the salon is active';
    COMMENT ON COLUMN staff.is_active IS 'Boolean flag indicating if the staff member is active';
    
    RAISE NOTICE 'Boolean column fix migration completed successfully';
    
END $$;
