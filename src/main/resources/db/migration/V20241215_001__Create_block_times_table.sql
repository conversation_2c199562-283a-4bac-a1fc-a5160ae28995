-- Migration to create block_times table for the block time feature
-- This table stores blocked time slots that prevent appointments from being scheduled

CREATE TABLE IF NOT EXISTS block_times (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    salon_id UUID NOT NULL,
    start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    end_time TIMESTAMP WITH TIME ZONE NOT NULL,
    reason VARCHAR(50) NOT NULL,
    custom_reason TEXT,
    staff_ids UUID[] NOT NULL,
    created_by UUID NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_by <PERSON><PERSON><PERSON>,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_recurring BOOLEAN DEFAULT FALSE,
    recurrence_pattern JSONB,
    notes TEXT,
    status VARCHAR(20) DEFAULT 'ACTIVE',
    
    -- Constraints
    CONSTRAINT valid_time_range CHECK (end_time > start_time),
    CONSTRAINT valid_status CHECK (status IN ('ACTIVE', 'CANCELLED', 'EXPIRED')),
    CONSTRAINT valid_reason CHECK (reason IN ('PAUZA', 'INTALNIRE', 'CONCEDIU', 'PERSONAL', 'TRAINING', 'ALTELE')),
    CONSTRAINT non_empty_staff_ids CHECK (array_length(staff_ids, 1) > 0),
    CONSTRAINT custom_reason_when_altele CHECK (
        (reason != 'ALTELE') OR (reason = 'ALTELE' AND custom_reason IS NOT NULL AND custom_reason != '')
    )
);

-- Indexes for performance
CREATE INDEX idx_block_times_salon_time ON block_times(salon_id, start_time, end_time);
CREATE INDEX idx_block_times_staff ON block_times USING GIN(staff_ids);
CREATE INDEX idx_block_times_status ON block_times(status) WHERE status = 'ACTIVE';
CREATE INDEX idx_block_times_salon_status ON block_times(salon_id, status);
CREATE INDEX idx_block_times_created_by ON block_times(created_by);
CREATE INDEX idx_block_times_reason ON block_times(reason);
CREATE INDEX idx_block_times_date_range ON block_times(salon_id, start_time, end_time) WHERE status = 'ACTIVE';

-- Comments for documentation
COMMENT ON TABLE block_times IS 'Stores blocked time slots that prevent appointments from being scheduled';
COMMENT ON COLUMN block_times.id IS 'Unique identifier for the block time';
COMMENT ON COLUMN block_times.salon_id IS 'Reference to the salon where time is blocked';
COMMENT ON COLUMN block_times.start_time IS 'Start time of the blocked period (with timezone)';
COMMENT ON COLUMN block_times.end_time IS 'End time of the blocked period (with timezone)';
COMMENT ON COLUMN block_times.reason IS 'Predefined reason for blocking time';
COMMENT ON COLUMN block_times.custom_reason IS 'Custom reason when reason is ALTELE';
COMMENT ON COLUMN block_times.staff_ids IS 'Array of staff member UUIDs affected by this block';
COMMENT ON COLUMN block_times.created_by IS 'User who created this block time';
COMMENT ON COLUMN block_times.created_at IS 'Timestamp when block was created';
COMMENT ON COLUMN block_times.updated_by IS 'User who last updated this block time';
COMMENT ON COLUMN block_times.updated_at IS 'Timestamp when block was last updated';
COMMENT ON COLUMN block_times.is_recurring IS 'Whether this block repeats according to a pattern';
COMMENT ON COLUMN block_times.recurrence_pattern IS 'JSON configuration for recurring blocks';
COMMENT ON COLUMN block_times.notes IS 'Additional notes about the block';
COMMENT ON COLUMN block_times.status IS 'Current status of the block (ACTIVE, CANCELLED, EXPIRED)';

-- Function to automatically update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_block_times_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update updated_at on row updates
CREATE TRIGGER trigger_update_block_times_updated_at
    BEFORE UPDATE ON block_times
    FOR EACH ROW
    EXECUTE FUNCTION update_block_times_updated_at();

-- Function to automatically expire old block times
CREATE OR REPLACE FUNCTION expire_old_block_times()
RETURNS INTEGER AS $$
DECLARE
    expired_count INTEGER;
BEGIN
    UPDATE block_times 
    SET status = 'EXPIRED', updated_at = NOW()
    WHERE status = 'ACTIVE' 
    AND end_time < NOW() - INTERVAL '1 hour';
    
    GET DIAGNOSTICS expired_count = ROW_COUNT;
    RETURN expired_count;
END;
$$ LANGUAGE plpgsql;

-- Create a scheduled job to run the expiration function (optional, depends on your scheduler)
-- This would typically be handled by a scheduled task in the application
COMMENT ON FUNCTION expire_old_block_times() IS 'Function to automatically expire block times that have ended';

-- Sample data for testing (remove in production)
-- INSERT INTO block_times (
--     id, salon_id, start_time, end_time, reason, staff_ids, created_by, notes
-- ) VALUES (
--     gen_random_uuid(),
--     'sample-salon-id'::uuid,
--     NOW() + INTERVAL '1 day',
--     NOW() + INTERVAL '1 day 2 hours',
--     'PAUZA',
--     ARRAY['sample-staff-id'::uuid],
--     'sample-user-id'::uuid,
--     'Sample block time for testing'
-- );
