-- Migration to fix boolean column types that might be stored as bytea
-- This addresses casting issues between bytea and boolean types

DO $$
BEGIN
    -- Fix clients.is_active column
    IF EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'clients' 
        AND column_name = 'is_active' 
        AND data_type != 'boolean'
    ) THEN
        -- First, update any problematic values
        UPDATE clients 
        SET is_active = true 
        WHERE is_active IS NULL;
        
        -- Convert the column to boolean
        ALTER TABLE clients 
        ALTER COLUMN is_active 
        TYPE BOOLEAN 
        USING CASE 
            WHEN is_active::text = 'true' OR is_active::text = '1' OR is_active::text = 't' THEN true
            WHEN is_active::text = 'false' OR is_active::text = '0' OR is_active::text = 'f' THEN false
            ELSE true
        END;
        
        -- Set default value
        ALTER TABLE clients 
        ALTER COLUMN is_active 
        SET DEFAULT true;
        
        RAISE NOTICE 'Successfully converted clients.is_active column to BOOLEAN';
    ELSE
        RAISE NOTICE 'clients.is_active column is already BOOLEAN';
    END IF;

    -- Fix users.is_active column
    IF EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'users' 
        AND column_name = 'is_active' 
        AND data_type != 'boolean'
    ) THEN
        -- First, update any problematic values
        UPDATE users 
        SET is_active = true 
        WHERE is_active IS NULL;
        
        -- Convert the column to boolean
        ALTER TABLE users 
        ALTER COLUMN is_active 
        TYPE BOOLEAN 
        USING CASE 
            WHEN is_active::text = 'true' OR is_active::text = '1' OR is_active::text = 't' THEN true
            WHEN is_active::text = 'false' OR is_active::text = '0' OR is_active::text = 'f' THEN false
            ELSE true
        END;
        
        -- Set default value
        ALTER TABLE users 
        ALTER COLUMN is_active 
        SET DEFAULT true;
        
        RAISE NOTICE 'Successfully converted users.is_active column to BOOLEAN';
    ELSE
        RAISE NOTICE 'users.is_active column is already BOOLEAN';
    END IF;

    -- Fix pets.is_active column
    IF EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'pets' 
        AND column_name = 'is_active' 
        AND data_type != 'boolean'
    ) THEN
        -- First, update any problematic values
        UPDATE pets 
        SET is_active = true 
        WHERE is_active IS NULL;
        
        -- Convert the column to boolean
        ALTER TABLE pets 
        ALTER COLUMN is_active 
        TYPE BOOLEAN 
        USING CASE 
            WHEN is_active::text = 'true' OR is_active::text = '1' OR is_active::text = 't' THEN true
            WHEN is_active::text = 'false' OR is_active::text = '0' OR is_active::text = 'f' THEN false
            ELSE true
        END;
        
        -- Set default value
        ALTER TABLE pets 
        ALTER COLUMN is_active 
        SET DEFAULT true;
        
        RAISE NOTICE 'Successfully converted pets.is_active column to BOOLEAN';
    ELSE
        RAISE NOTICE 'pets.is_active column is already BOOLEAN';
    END IF;

    -- Fix salons.is_active column
    IF EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'salons' 
        AND column_name = 'is_active' 
        AND data_type != 'boolean'
    ) THEN
        -- First, update any problematic values
        UPDATE salons 
        SET is_active = true 
        WHERE is_active IS NULL;
        
        -- Convert the column to boolean
        ALTER TABLE salons 
        ALTER COLUMN is_active 
        TYPE BOOLEAN 
        USING CASE 
            WHEN is_active::text = 'true' OR is_active::text = '1' OR is_active::text = 't' THEN true
            WHEN is_active::text = 'false' OR is_active::text = '0' OR is_active::text = 'f' THEN false
            ELSE true
        END;
        
        -- Set default value
        ALTER TABLE salons 
        ALTER COLUMN is_active 
        SET DEFAULT true;
        
        RAISE NOTICE 'Successfully converted salons.is_active column to BOOLEAN';
    ELSE
        RAISE NOTICE 'salons.is_active column is already BOOLEAN';
    END IF;

    -- Fix staff.is_active column
    IF EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'staff' 
        AND column_name = 'is_active' 
        AND data_type != 'boolean'
    ) THEN
        -- First, update any problematic values
        UPDATE staff 
        SET is_active = true 
        WHERE is_active IS NULL;
        
        -- Convert the column to boolean
        ALTER TABLE staff 
        ALTER COLUMN is_active 
        TYPE BOOLEAN 
        USING CASE 
            WHEN is_active::text = 'true' OR is_active::text = '1' OR is_active::text = 't' THEN true
            WHEN is_active::text = 'false' OR is_active::text = '0' OR is_active::text = 'f' THEN false
            ELSE true
        END;
        
        -- Set default value
        ALTER TABLE staff 
        ALTER COLUMN is_active 
        SET DEFAULT true;
        
        RAISE NOTICE 'Successfully converted staff.is_active column to BOOLEAN';
    ELSE
        RAISE NOTICE 'staff.is_active column is already BOOLEAN';
    END IF;

END $$;

-- Add comments for documentation
COMMENT ON COLUMN clients.is_active IS 'Boolean flag indicating if the client is active';
COMMENT ON COLUMN users.is_active IS 'Boolean flag indicating if the user is active';
COMMENT ON COLUMN pets.is_active IS 'Boolean flag indicating if the pet is active';
COMMENT ON COLUMN salons.is_active IS 'Boolean flag indicating if the salon is active';
COMMENT ON COLUMN staff.is_active IS 'Boolean flag indicating if the staff member is active';
