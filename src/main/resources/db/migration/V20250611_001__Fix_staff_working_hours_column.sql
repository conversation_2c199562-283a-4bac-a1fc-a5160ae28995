-- Migration to fix the working_hours column type in staff table
-- Convert from TEXT/VARCHAR to JSONB for proper JSON handling

-- First, check if the column exists and what type it is
-- If it's not JSONB, convert it

DO $$
BEGIN
    -- Check if the column exists and is not already JSONB
    IF EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'staff' 
        AND column_name = 'working_hours' 
        AND data_type != 'jsonb'
    ) THEN
        -- Convert the column to JSONB
        -- First, ensure all values are valid JSON or set to default
        UPDATE staff 
        SET working_hours = '{}' 
        WHERE working_hours IS NULL 
        OR working_hours = '' 
        OR working_hours NOT LIKE '{%}';
        
        -- Now alter the column type
        ALTER TABLE staff 
        ALTER COLUMN working_hours 
        TYPE JSONB 
        USING working_hours::JSONB;
        
        RAISE NOTICE 'Successfully converted working_hours column to JSONB';
    ELSE
        RAISE NOTICE 'working_hours column is already JSONB or does not exist';
    END IF;
END $$;

-- Add a comment to document the change
COMMENT ON COLUMN staff.working_hours IS 'JSON representation of staff working hours schedule';
