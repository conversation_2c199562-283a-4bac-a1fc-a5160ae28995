-- Migration to fix data integrity issues in staff working hours
-- This script corrects cases where userId was incorrectly stored as staffId

-- Step 1: Identify and log corrupted records
-- Records where staff_working_hours_settings.staff_id matches staff.user_id instead of staff.id
CREATE TEMP TABLE corrupted_working_hours AS
SELECT 
    swhs.id as working_hours_id,
    swhs.staff_id as incorrect_staff_id,
    swhs.salon_id,
    s.id as correct_staff_id,
    s.user_id,
    u.name as user_name
FROM staff_working_hours_settings swhs
JOIN staff s ON swhs.staff_id = s.user_id  -- This is the problem: staff_id should match staff.id, not staff.user_id
JOIN users u ON s.user_id = u.id
WHERE swhs.salon_id = s.salon_id;

-- Step 2: Log the corrupted records for audit purposes
INSERT INTO migration_log (migration_name, operation, table_name, record_id, old_value, new_value, created_at)
SELECT 
    'V999__fix_staff_working_hours_data_integrity',
    'UPDATE',
    'staff_working_hours_settings',
    working_hours_id,
    'staff_id=' || incorrect_staff_id,
    'staff_id=' || correct_staff_id,
    NOW()
FROM corrupted_working_hours;

-- Step 3: Update the corrupted records
UPDATE staff_working_hours_settings 
SET staff_id = (
    SELECT s.id 
    FROM staff s 
    WHERE s.user_id = staff_working_hours_settings.staff_id 
    AND s.salon_id = staff_working_hours_settings.salon_id
)
WHERE id IN (SELECT working_hours_id FROM corrupted_working_hours);

-- Step 4: Verify the fix
-- This query should return 0 rows after the fix
SELECT 
    swhs.id,
    swhs.staff_id,
    'STILL_CORRUPTED' as status
FROM staff_working_hours_settings swhs
JOIN staff s ON swhs.staff_id = s.user_id
WHERE swhs.salon_id = s.salon_id;

-- Step 5: Create migration log table if it doesn't exist
CREATE TABLE IF NOT EXISTS migration_log (
    id SERIAL PRIMARY KEY,
    migration_name VARCHAR(255) NOT NULL,
    operation VARCHAR(50) NOT NULL,
    table_name VARCHAR(100) NOT NULL,
    record_id VARCHAR(255),
    old_value TEXT,
    new_value TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Step 6: Add a comment for documentation
COMMENT ON TABLE staff_working_hours_settings IS 'Staff working hours settings - staff_id should reference staff.id, not staff.user_id';

-- Step 7: Clean up temporary table
DROP TABLE corrupted_working_hours;

-- Step 8: Add a constraint to prevent future data integrity issues
-- This constraint ensures staff_id references an actual staff record in the same salon
ALTER TABLE staff_working_hours_settings 
ADD CONSTRAINT fk_staff_working_hours_staff_salon 
FOREIGN KEY (staff_id, salon_id) 
REFERENCES staff(id, salon_id) 
ON DELETE CASCADE;

-- Step 9: Log completion
INSERT INTO migration_log (migration_name, operation, table_name, record_id, old_value, new_value, created_at)
VALUES (
    'V999__fix_staff_working_hours_data_integrity',
    'COMPLETED',
    'migration',
    'V999',
    'Data integrity issues in staff working hours',
    'Fixed staff_id references and added constraints',
    NOW()
);
