# Automated Notification System Implementation Guide

## Overview
This document provides a comprehensive guide for the automated notification system with SMSO SMS integration and FCM push notifications that has been implemented in the Animalia backend.

## ✅ Completed Implementation

### 1. Core Infrastructure
- **Dependencies Added**: HTTP client, async processing, rate limiting, caching
- **Configuration**: SMSO API, FCM, notification preferences, Romanian phone validation
- **Enhanced PhoneNumber**: Romanian validation (+40XXXXXXXXX format)

### 2. Domain Models
- **NotificationSettings**: SMS/Push settings per salon with Romanian templates
- **FcmToken**: FCM token management with device tracking
- **Notification**: SMS/Push notification entities with status tracking
- **SmsRateLimit**: 3 SMS/hour rate limiting per phone number

### 3. External Service Integration
- **SmsoSmsService**: SMSO API integration with retry logic and Romanian templates
- **FcmPushNotificationService**: Firebase Cloud Messaging for staff notifications
- **SmsRateLimitService**: Cached rate limiting with automatic cleanup

### 4. Business Logic
- **NotificationManagementUseCase**: Complete notification orchestration
- **NotificationTemplateService**: Romanian SMS templates with appointment data
- **AppointmentNotificationEventHandler**: Automatic triggers for appointment lifecycle

### 5. API Endpoints
- **NotificationController**: FCM token management, settings configuration
- **Comprehensive DTOs**: Request/response models with validation
- **Role-based Security**: Chief Groomer settings management

### 6. Automation & Scheduling
- **AppointmentReminderScheduler**: Day-before (18:00) and hour-before reminders
- **Automatic Cleanup**: Old notifications, expired tokens, rate limits

## 🔧 Configuration Required

### Environment Variables (Production)
```bash
# SMSO API Configuration
SMSO_API_KEY=uT4FMktcZ3Dk9bON9tGfn7pe3Pg8WtSHgmAAW7B6
SMSO_API_URL=https://api.smso.ro/v1

# FCM Configuration
FCM_ENABLED=true
FCM_SERVICE_ACCOUNT_KEY_PATH=/path/to/firebase-service-account.json

# Notification Settings
SMS_NOTIFICATIONS_ENABLED=true
PUSH_NOTIFICATIONS_ENABLED=true
SMS_RATE_LIMIT_PER_HOUR=3
```

### Firebase Setup
1. Download Firebase service account key JSON
2. Set FCM_SERVICE_ACCOUNT_KEY_PATH environment variable
3. Ensure Firebase project has FCM enabled

## 📱 Frontend Integration

### FCM Token Registration
```typescript
// Register FCM token when app starts
const registerFcmToken = async (token: string, salonId: string) => {
  await fetch('/api/notifications/fcm/register', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      salonId,
      token,
      deviceType: 'MOBILE'
    })
  });
};
```

### Notification Settings Management
```typescript
// Update SMS settings (Chief Groomer only)
const updateSmsSettings = async (salonId: string, settings: SmsSettings) => {
  await fetch(`/api/notifications/settings/${salonId}`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ smsSettings: settings })
  });
};
```

## 🔄 Automatic Notification Triggers

### Appointment Lifecycle Events
- **Creation**: SMS to client + Push to assigned staff + Chief Groomers
- **Cancellation**: SMS to client + Push to staff
- **Completion**: SMS to client + Push to staff
- **Day Before**: SMS reminder at 18:00 daily
- **Hour Before**: SMS reminder every hour

### Error Handling
- **Non-Critical**: Appointment operations never fail due to notification errors
- **Async Processing**: All notifications sent in background
- **Retry Logic**: Failed notifications automatically retried
- **Rate Limiting**: Prevents SMS spam (3/hour per phone)

## 🧪 Testing Strategy

### Unit Tests
```kotlin
// Example test structure
@Nested
inner class SendSmsNotificationTests {
    @Test
    fun `should send SMS notification when valid request`() {
        // Given - setup mocks and test data
        // When - execute use case
        // Then - verify behavior and state
    }
}
```

### Integration Tests
1. **Appointment + Notification Flow**: Create appointment → verify SMS/Push sent
2. **Rate Limiting**: Send 4 SMS → verify 4th fails with rate limit error
3. **Settings Management**: Update settings → verify notifications respect new settings
4. **FCM Token Management**: Register/update/deregister tokens

### Manual Testing with Mobile App
1. Register FCM token from Flutter app
2. Create test appointment with Romanian phone number
3. Verify SMS received and push notification appears
4. Test cancellation and completion flows
5. Verify reminder scheduling

## 🚀 Deployment Steps

### 1. Database Migration
The system uses Hibernate auto-update, so new tables will be created automatically:
- `notifications`
- `fcm_tokens` 
- `notification_settings`
- `sms_rate_limits`

### 2. Environment Configuration
Set all required environment variables in production environment.

### 3. Firebase Setup
Ensure Firebase service account key is accessible and FCM is enabled.

### 4. SMSO API Testing
Test SMSO integration with a few test SMS before going live.

### 5. Monitoring
- Check application logs for notification errors
- Monitor SMS rate limiting effectiveness
- Track FCM token registration/deregistration

## 📊 Monitoring & Maintenance

### Key Metrics
- SMS delivery success rate
- Push notification delivery rate
- Rate limiting effectiveness
- Failed notification retry success

### Scheduled Maintenance
- **Daily 03:00**: Cleanup old notifications (30+ days)
- **Daily 03:00**: Remove expired FCM tokens (60+ days)
- **Daily 03:00**: Clean expired rate limit records

### Troubleshooting
- **SMS not sent**: Check SMSO API key, phone number format, rate limits
- **Push not received**: Verify FCM token registration, Firebase configuration
- **Rate limit issues**: Check SMS rate limit settings, reset if needed

## 🔒 Security Considerations

### Data Protection
- Phone numbers stored securely with validation
- FCM tokens encrypted in transit
- Rate limiting prevents abuse

### Access Control
- Only Chief Groomers can modify notification settings
- Staff can only register their own FCM tokens
- Notification history restricted by salon access

### API Security
- SMSO API key secured via environment variables
- Firebase service account key protected
- All endpoints require authentication

## 📞 Support & Maintenance

### Common Issues
1. **Romanian phone validation**: Ensure +40 format
2. **FCM token expiry**: Automatic cleanup handles this
3. **Rate limiting**: 3 SMS/hour limit is configurable
4. **Template customization**: Chief Groomers can modify via API

### Future Enhancements
- Email notifications
- WhatsApp integration
- Advanced scheduling options
- Notification analytics dashboard

This implementation provides a robust, scalable notification system that integrates seamlessly with the existing appointment management workflow while maintaining high reliability and user experience.
