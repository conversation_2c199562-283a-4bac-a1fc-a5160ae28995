#!/bin/bash

# Workflow and Method Processing Time Monitoring Demo
# This script demonstrates the comprehensive monitoring capabilities for workflows and method execution

set -e

# Configuration
BASE_URL="http://localhost:8080"
ACTUATOR_URL="${BASE_URL}/actuator"
MONITORING_URL="${BASE_URL}/api/monitoring"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${CYAN}ℹ️  $1${NC}"
}

# Function to check if application is running
check_app_status() {
    print_info "Checking if application is running..."
    if curl -s "${ACTUATOR_URL}/health" > /dev/null 2>&1; then
        print_success "Application is running"
        return 0
    else
        print_error "Application is not running. Please start the application first."
        print_info "Run: ./gradlew bootRun"
        return 1
    fi
}

# Function to show workflow metrics
show_workflow_metrics() {
    print_header "WORKFLOW PROCESSING TIMES & SUCCESS RATES"
    
    echo "Fetching workflow metrics..."
    workflow_response=$(curl -s "${MONITORING_URL}/workflows" 2>/dev/null || echo '{"error": "endpoint not available"}')
    
    if echo "$workflow_response" | jq -e '.workflows' > /dev/null 2>&1; then
        echo "$workflow_response" | jq -r '
            .workflows[] | 
            "📋 Workflow: \(.name)
            📊 Total Executions: \(.totalExecutions)
            ✅ Successful: \(.successfulExecutions) (\(.successRate | floor)%)
            ❌ Failed: \(.failedExecutions)
            ⏱️  Avg Processing Time: \(.averageProcessingTimeMs | floor)ms
            🔥 Max Processing Time: \(.maxProcessingTimeMs | floor)ms
            ⚡ Min Processing Time: \(.minProcessingTimeMs | floor)ms
            ────────────────────────────────────────"
        '
    else
        print_warning "Workflow metrics endpoint not available or no data yet"
        print_info "Try making some API calls to generate workflow data"
    fi
}

# Function to show method execution metrics
show_method_metrics() {
    print_header "METHOD EXECUTION TIMES & SUCCESS RATES"
    
    echo "Fetching method execution metrics..."
    method_response=$(curl -s "${MONITORING_URL}/methods" 2>/dev/null || echo '{"error": "endpoint not available"}')
    
    if echo "$method_response" | jq -e '.methods' > /dev/null 2>&1; then
        echo "$method_response" | jq -r '
            .methods[] | 
            "🔧 Method: \(.className).\(.methodName)
            📊 Total Executions: \(.totalExecutions)
            ✅ Successful: \(.successfulExecutions) (\(.successRate | floor)%)
            ❌ Failed: \(.failedExecutions)
            ⏱️  Avg Processing Time: \(.averageProcessingTimeMs | floor)ms
            🔥 Max Processing Time: \(.maxProcessingTimeMs | floor)ms
            ⚡ Min Processing Time: \(.minProcessingTimeMs | floor)ms
            ────────────────────────────────────────"
        '
    else
        print_warning "Method metrics endpoint not available or no data yet"
        print_info "Try making some API calls to generate method execution data"
    fi
}

# Function to show error metrics
show_error_metrics() {
    print_header "ERROR TRACKING & ANALYSIS"
    
    echo "Fetching error metrics..."
    error_response=$(curl -s "${MONITORING_URL}/errors" 2>/dev/null || echo '{"error": "endpoint not available"}')
    
    if echo "$error_response" | jq -e '.errors' > /dev/null 2>&1; then
        echo "$error_response" | jq -r '
            .errors[] | 
            "🚨 Error Type: \(.errorType)
            📊 Total Occurrences: \(.totalOccurrences)
            🎯 Affected Operations: \(.affectedOperations | join(", "))
            ────────────────────────────────────────"
        '
    else
        print_warning "Error metrics endpoint not available or no errors recorded yet"
        print_success "This is good - no errors means your system is running smoothly!"
    fi
}

# Function to show appointment-specific metrics
show_appointment_metrics() {
    print_header "APPOINTMENT WORKFLOW METRICS"
    
    echo "Fetching appointment-specific metrics..."
    appointment_response=$(curl -s "${MONITORING_URL}/appointments" 2>/dev/null || echo '{"error": "endpoint not available"}')
    
    if echo "$appointment_response" | jq -e '.operations' > /dev/null 2>&1; then
        echo "$appointment_response" | jq -r '
            .operations[] | 
            "📅 Operation: \(.operation)
            📊 Total Executions: \(.totalExecutions)
            ⏱️  Avg Processing Time: \(.averageProcessingTimeMs | floor)ms
            🔥 Max Processing Time: \(.maxProcessingTimeMs | floor)ms
            ⚠️  Conflicts Detected: \(.conflictsDetected)
            💡 Alternatives Generated: \(.alternativesGenerated)
            ────────────────────────────────────────"
        '
    else
        print_warning "Appointment metrics endpoint not available or no data yet"
        print_info "Try scheduling some appointments to generate data"
    fi
}

# Function to show overall health metrics
show_health_metrics() {
    print_header "OVERALL SYSTEM HEALTH"
    
    echo "Fetching system health metrics..."
    health_response=$(curl -s "${MONITORING_URL}/health" 2>/dev/null || echo '{"error": "endpoint not available"}')
    
    if echo "$health_response" | jq -e '.totalOperations' > /dev/null 2>&1; then
        echo "$health_response" | jq -r '
            "🏥 System Health Overview
            📊 Total Operations: \(.totalOperations)
            ✅ Successful Operations: \(.successfulOperations)
            ❌ Failed Operations: \(.failedOperations)
            📈 Success Rate: \(.successRate | floor)%
            ⏱️  Average Response Time: \(.averageResponseTimeMs | floor)ms
            ────────────────────────────────────────"
        '
        
        # Health status based on success rate
        success_rate=$(echo "$health_response" | jq -r '.successRate')
        if (( $(echo "$success_rate >= 95" | bc -l) )); then
            print_success "System health is EXCELLENT (≥95% success rate)"
        elif (( $(echo "$success_rate >= 90" | bc -l) )); then
            print_warning "System health is GOOD (≥90% success rate)"
        elif (( $(echo "$success_rate >= 80" | bc -l) )); then
            print_warning "System health is FAIR (≥80% success rate)"
        else
            print_error "System health needs ATTENTION (<80% success rate)"
        fi
    else
        print_warning "Health metrics endpoint not available or no data yet"
    fi
}

# Function to show Prometheus metrics related to workflows
show_prometheus_workflow_metrics() {
    print_header "PROMETHEUS WORKFLOW METRICS"
    
    echo "Fetching Prometheus metrics for workflows..."
    prometheus_metrics=$(curl -s "${ACTUATOR_URL}/prometheus" 2>/dev/null || echo "")
    
    if [ -n "$prometheus_metrics" ]; then
        echo "🔍 Workflow Execution Metrics:"
        echo "$prometheus_metrics" | grep -E "(workflow_execution|method_execution|appointment_)" | head -20
        echo ""
        echo "🔍 Error Metrics:"
        echo "$prometheus_metrics" | grep -E "(error|exception|failure)" | head -10
        echo ""
        echo "📊 Use these metrics in Grafana for real-time dashboards!"
    else
        print_warning "Prometheus metrics not available"
    fi
}

# Function to generate test load for demonstration
generate_test_load() {
    print_header "GENERATING TEST LOAD"
    
    print_info "Generating test API calls to demonstrate monitoring..."
    
    # Make some test calls to generate metrics
    for i in {1..5}; do
        echo "Making test call $i/5..."
        
        # Test health endpoint (should always succeed)
        curl -s "${ACTUATOR_URL}/health" > /dev/null
        
        # Test metrics endpoint
        curl -s "${ACTUATOR_URL}/metrics" > /dev/null
        
        # Test our monitoring endpoints
        curl -s "${MONITORING_URL}/health" > /dev/null 2>&1 || true
        curl -s "${MONITORING_URL}/workflows" > /dev/null 2>&1 || true
        
        sleep 1
    done
    
    print_success "Test load generation completed"
    print_info "Metrics should now show some data"
}

# Function to show monitoring URLs
show_monitoring_urls() {
    print_header "MONITORING ENDPOINTS"
    
    echo "📊 Workflow Metrics:"
    echo "   ${MONITORING_URL}/workflows"
    echo ""
    echo "🔧 Method Metrics:"
    echo "   ${MONITORING_URL}/methods"
    echo ""
    echo "🚨 Error Metrics:"
    echo "   ${MONITORING_URL}/errors"
    echo ""
    echo "📅 Appointment Metrics:"
    echo "   ${MONITORING_URL}/appointments"
    echo ""
    echo "🏥 Health Metrics:"
    echo "   ${MONITORING_URL}/health"
    echo ""
    echo "📈 Prometheus Metrics:"
    echo "   ${ACTUATOR_URL}/prometheus"
    echo ""
    echo "🔍 Spring Boot Actuator:"
    echo "   ${ACTUATOR_URL}/health"
    echo "   ${ACTUATOR_URL}/metrics"
    echo "   ${ACTUATOR_URL}/info"
}

# Function to show Grafana dashboard queries
show_grafana_queries() {
    print_header "GRAFANA DASHBOARD QUERIES"
    
    echo "📊 Workflow Success Rate:"
    echo "   rate(workflow_executions_total{success=\"true\"}[5m]) / rate(workflow_executions_total[5m]) * 100"
    echo ""
    echo "⏱️  Workflow Processing Time (95th percentile):"
    echo "   histogram_quantile(0.95, rate(workflow_execution_seconds_bucket[5m]))"
    echo ""
    echo "🚨 Error Rate by Type:"
    echo "   rate(workflow_executions_total{success=\"false\"}[5m]) by (error_type)"
    echo ""
    echo "📅 Appointment Operations:"
    echo "   rate(appointment_operations_total[5m]) by (operation)"
    echo ""
    echo "🔧 Method Execution Time:"
    echo "   histogram_quantile(0.95, rate(method_execution_seconds_bucket[5m])) by (class, method)"
}

# Main execution
main() {
    echo -e "${PURPLE}"
    echo "🚀 Animalia Workflow & Method Processing Time Monitoring Demo"
    echo "============================================================"
    echo -e "${NC}"
    
    # Check if application is running
    if ! check_app_status; then
        exit 1
    fi
    
    # Show all monitoring information
    show_workflow_metrics
    echo ""
    show_method_metrics
    echo ""
    show_error_metrics
    echo ""
    show_appointment_metrics
    echo ""
    show_health_metrics
    echo ""
    show_prometheus_workflow_metrics
    echo ""
    
    # Generate test load if requested
    read -p "🤔 Would you like to generate test load to see metrics in action? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        generate_test_load
        echo ""
        print_info "Refreshing metrics after test load..."
        show_health_metrics
    fi
    
    # Show useful information
    show_monitoring_urls
    echo ""
    show_grafana_queries
    
    echo ""
    print_success "🎉 Workflow monitoring demonstration completed!"
    echo ""
    print_info "💡 Key Features Demonstrated:"
    echo "   • Workflow execution timing and success rates"
    echo "   • Method-level performance monitoring"
    echo "   • Error tracking and categorization"
    echo "   • Appointment-specific business metrics"
    echo "   • Overall system health monitoring"
    echo "   • Prometheus metrics for Grafana dashboards"
    echo ""
    print_info "🔧 Next Steps:"
    echo "   • Set up Grafana dashboards using the provided queries"
    echo "   • Configure alerts for high error rates or slow performance"
    echo "   • Monitor trends over time to identify performance issues"
    echo "   • Use metrics to optimize business workflows"
}

# Check if jq is installed
if ! command -v jq &> /dev/null; then
    print_error "jq is required but not installed. Please install jq first."
    print_info "On macOS: brew install jq"
    print_info "On Ubuntu: sudo apt-get install jq"
    exit 1
fi

# Check if bc is installed (for floating point comparison)
if ! command -v bc &> /dev/null; then
    print_error "bc is required but not installed. Please install bc first."
    print_info "On macOS: brew install bc"
    print_info "On Ubuntu: sudo apt-get install bc"
    exit 1
fi

# Run the demo
main "$@"
