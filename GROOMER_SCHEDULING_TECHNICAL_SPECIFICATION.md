# Individual Groomer Scheduling System - Technical Specification

## 1. Overview

This document outlines the design and implementation of an individual groomer scheduling system that operates within the constraints of salon-level working hours. The system allows groomers to define their personal work schedules while ensuring they cannot work outside of salon operating hours.

## 2. System Architecture

### 2.1 Relationship Between Salon and Groomer Schedules

```
Salon Working Hours (Master Schedule)
├── Monday: 09:00-17:00 (Lunch: 12:00-13:00)
├── Tuesday: 09:00-17:00 (Lunch: 12:00-13:00)
├── Wednesday: 09:00-17:00 (Lunch: 12:00-13:00)
├── Thursday: 09:00-17:00 (Lunch: 12:00-13:00)
├── Friday: 09:00-17:00 (Lunch: 12:00-13:00)
├── Saturday: 10:00-15:00 (No lunch break)
└── Sunday: CLOSED

Groomer Individual Schedules (Constrained by Salon Hours)
├── Groomer A: Mon-Wed-Fri (09:00-16:00, Lunch: 12:30-13:30)
├── Groomer B: <PERSON><PERSON><PERSON><PERSON>hu-<PERSON><PERSON> (10:00-15:00, Lunch: 13:00-14:00)
└── Groomer C: Mon-<PERSON><PERSON>-<PERSON>hu-<PERSON><PERSON> (09:30-17:00, Lunch: 12:00-13:00)
```

### 2.2 Core Principles

1. **Hierarchical Constraint**: Groomer schedules are always constrained by salon working hours
2. **Flexible Patterns**: Support for various rotation patterns (2-on-2-off, 3-on-1-off, etc.)
3. **Personal Customization**: Each groomer can set their own working hours and lunch breaks
4. **Validation Layer**: Multi-level validation ensures schedule consistency
5. **Authorization Control**: Role-based access for schedule management

## 3. Database Schema Design

### 3.1 Core Tables

```sql
-- Groomer schedule templates (reusable patterns)
CREATE TABLE groomer_schedule_templates (
    id VARCHAR(255) PRIMARY KEY DEFAULT gen_random_uuid()::text,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    pattern_type VARCHAR(50) NOT NULL, -- WEEKLY, ROTATION, CUSTOM
    pattern_config JSONB NOT NULL,
    created_by VARCHAR(255) NOT NULL,
    salon_id VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (salon_id) REFERENCES salons(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- Individual groomer schedules
CREATE TABLE groomer_schedules (
    id VARCHAR(255) PRIMARY KEY DEFAULT gen_random_uuid()::text,
    groomer_id VARCHAR(255) NOT NULL,
    salon_id VARCHAR(255) NOT NULL,
    template_id VARCHAR(255), -- Optional reference to template
    schedule_name VARCHAR(255) NOT NULL,
    effective_from DATE NOT NULL,
    effective_until DATE, -- NULL means indefinite
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (groomer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (salon_id) REFERENCES salons(id) ON DELETE CASCADE,
    FOREIGN KEY (template_id) REFERENCES groomer_schedule_templates(id),
    UNIQUE(groomer_id, salon_id, effective_from)
);

-- Daily schedule entries for groomers
CREATE TABLE groomer_daily_schedules (
    id VARCHAR(255) PRIMARY KEY DEFAULT gen_random_uuid()::text,
    groomer_schedule_id VARCHAR(255) NOT NULL,
    day_of_week VARCHAR(10) NOT NULL, -- monday, tuesday, etc.
    is_working_day BOOLEAN DEFAULT false,
    start_time TIME,
    end_time TIME,
    break_start_time TIME,
    break_end_time TIME,
    break_duration_minutes INTEGER DEFAULT 60,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (groomer_schedule_id) REFERENCES groomer_schedules(id) ON DELETE CASCADE,
    UNIQUE(groomer_schedule_id, day_of_week)
);

-- Schedule exceptions (one-time overrides)
CREATE TABLE groomer_schedule_exceptions (
    id VARCHAR(255) PRIMARY KEY DEFAULT gen_random_uuid()::text,
    groomer_id VARCHAR(255) NOT NULL,
    salon_id VARCHAR(255) NOT NULL,
    exception_date DATE NOT NULL,
    exception_type VARCHAR(50) NOT NULL, -- DAY_OFF, CUSTOM_HOURS, VACATION
    start_time TIME,
    end_time TIME,
    break_start_time TIME,
    break_end_time TIME,
    reason VARCHAR(255),
    notes TEXT,
    created_by VARCHAR(255) NOT NULL,
    approved_by VARCHAR(255),
    approval_status VARCHAR(50) DEFAULT 'PENDING', -- PENDING, APPROVED, REJECTED
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (groomer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (salon_id) REFERENCES salons(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (approved_by) REFERENCES users(id),
    UNIQUE(groomer_id, salon_id, exception_date)
);

-- Rotation patterns for complex scheduling
CREATE TABLE groomer_rotation_patterns (
    id VARCHAR(255) PRIMARY KEY DEFAULT gen_random_uuid()::text,
    groomer_schedule_id VARCHAR(255) NOT NULL,
    pattern_name VARCHAR(255) NOT NULL,
    cycle_length_days INTEGER NOT NULL,
    start_date DATE NOT NULL,
    pattern_sequence JSONB NOT NULL, -- Array of work/off indicators
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (groomer_schedule_id) REFERENCES groomer_schedules(id) ON DELETE CASCADE
);
```

### 3.2 Indexes for Performance

```sql
CREATE INDEX idx_groomer_schedules_groomer_salon ON groomer_schedules(groomer_id, salon_id);
CREATE INDEX idx_groomer_schedules_effective_dates ON groomer_schedules(effective_from, effective_until);
CREATE INDEX idx_groomer_daily_schedules_schedule_day ON groomer_daily_schedules(groomer_schedule_id, day_of_week);
CREATE INDEX idx_groomer_exceptions_groomer_date ON groomer_schedule_exceptions(groomer_id, exception_date);
CREATE INDEX idx_groomer_exceptions_salon_date ON groomer_schedule_exceptions(salon_id, exception_date);
```

## 4. Business Rules and Validation Logic

### 4.1 Core Validation Rules

1. **Salon Constraint Validation**:
   - Groomer cannot work when salon is closed
   - Groomer working hours must be within salon operating hours
   - Groomer lunch breaks must be within their working hours

2. **Schedule Consistency**:
   - No overlapping active schedules for the same groomer
   - Effective dates must be logical (from <= until)
   - Break times must be within working hours

3. **Authorization Rules**:
   - Groomers can only modify their own schedules
   - Chief groomers can view and approve all schedules in their salon
   - Schedule exceptions may require approval

### 4.2 Validation Algorithm

```kotlin
fun validateGroomerSchedule(
    groomerSchedule: GroomerDailySchedule,
    salonWorkingHours: WorkingHoursSettings,
    date: LocalDate
): ValidationResult {
    val dayOfWeek = date.dayOfWeek
    val salonDaySchedule = salonWorkingHours.getWorkingHoursFor(date)

    // Check if salon is open
    if (salonDaySchedule == null || !salonDaySchedule.isWorkingDay) {
        return ValidationResult.error("Salonul este închis în această zi")
    }

    // Validate working hours are within salon hours
    if (groomerSchedule.startTime.isBefore(salonDaySchedule.startTime) ||
        groomerSchedule.endTime.isAfter(salonDaySchedule.endTime)) {
        return ValidationResult.error("Programul frizorului trebuie să fie în intervalul de funcționare al salonului")
    }

    // Validate break times
    groomerSchedule.breakStart?.let { breakStart ->
        groomerSchedule.breakEnd?.let { breakEnd ->
            if (breakStart.isBefore(groomerSchedule.startTime) ||
                breakEnd.isAfter(groomerSchedule.endTime)) {
                return ValidationResult.error("Pauza trebuie să fie în intervalul de lucru")
            }
        }
    }

    return ValidationResult.success()
}
```

## 5. Scheduling Patterns and Algorithms

### 5.1 Pattern Types

#### 5.1.1 Weekly Pattern
Fixed weekly schedule that repeats every week.
```json
{
  "type": "WEEKLY",
  "schedule": {
    "monday": {"working": true, "start": "09:00", "end": "17:00"},
    "tuesday": {"working": true, "start": "09:00", "end": "17:00"},
    "wednesday": {"working": false},
    "thursday": {"working": true, "start": "09:00", "end": "17:00"},
    "friday": {"working": true, "start": "09:00", "end": "17:00"},
    "saturday": {"working": false},
    "sunday": {"working": false}
  }
}
```

#### 5.1.2 Rotation Pattern
Cyclical pattern that repeats over a specified number of days.
```json
{
  "type": "ROTATION",
  "cycleLengthDays": 4,
  "pattern": ["WORK", "WORK", "OFF", "OFF"],
  "workingHours": {
    "start": "09:00",
    "end": "17:00",
    "breakStart": "12:00",
    "breakEnd": "13:00"
  }
}
```

#### 5.1.3 Custom Pattern
Flexible pattern for irregular schedules.
```json
{
  "type": "CUSTOM",
  "specificDates": {
    "2024-01-15": {"working": true, "start": "10:00", "end": "16:00"},
    "2024-01-16": {"working": false},
    "2024-01-17": {"working": true, "start": "09:00", "end": "18:00"}
  }
}
```

### 5.2 Schedule Calculation Algorithm

```kotlin
class GroomerScheduleCalculator {

    fun calculateWorkingDaysForPeriod(
        groomerSchedule: GroomerSchedule,
        startDate: LocalDate,
        endDate: LocalDate,
        salonWorkingHours: WorkingHoursSettings
    ): List<GroomerWorkingDay> {

        val workingDays = mutableListOf<GroomerWorkingDay>()
        var currentDate = startDate

        while (!currentDate.isAfter(endDate)) {
            val groomerDay = calculateDaySchedule(groomerSchedule, currentDate)
            val salonDay = salonWorkingHours.getWorkingHoursFor(currentDate)

            // Apply salon constraints
            val finalDay = applyConstraints(groomerDay, salonDay, currentDate)

            if (finalDay.isWorkingDay) {
                workingDays.add(finalDay)
            }

            currentDate = currentDate.plusDays(1)
        }

        return workingDays
    }

    private fun calculateDaySchedule(
        schedule: GroomerSchedule,
        date: LocalDate
    ): GroomerWorkingDay {

        // Check for exceptions first
        val exception = schedule.getExceptionForDate(date)
        if (exception != null) {
            return exception.toWorkingDay()
        }

        // Apply pattern-based calculation
        return when (schedule.patternType) {
            PatternType.WEEKLY -> calculateWeeklyPattern(schedule, date)
            PatternType.ROTATION -> calculateRotationPattern(schedule, date)
            PatternType.CUSTOM -> calculateCustomPattern(schedule, date)
        }
    }
}
```

## 6. API Endpoints Design

### 6.1 Groomer Schedule Management

#### 6.1.1 Create Groomer Schedule
```
POST /api/salons/{salonId}/groomers/{groomerId}/schedules
Authorization: CHIEF_GROOMER or self (groomerId matches current user)

Request Body:
{
  "scheduleName": "Programul meu de lucru",
  "effectiveFrom": "2024-01-01",
  "effectiveUntil": "2024-12-31",
  "patternType": "WEEKLY",
  "weeklySchedule": {
    "monday": {
      "isWorkingDay": true,
      "startTime": "09:00",
      "endTime": "17:00",
      "breakStart": "12:00",
      "breakEnd": "13:00"
    },
    // ... other days
  }
}
```

#### 6.1.2 Get Groomer Schedule
```
GET /api/salons/{salonId}/groomers/{groomerId}/schedules
Authorization: CHIEF_GROOMER or self

Query Parameters:
- effectiveDate: Date to get active schedule for
- includeExceptions: Include schedule exceptions

Response:
{
  "success": true,
  "data": {
    "scheduleId": "schedule-uuid",
    "groomerName": "Ion Popescu",
    "scheduleName": "Programul meu de lucru",
    "effectiveFrom": "2024-01-01",
    "effectiveUntil": "2024-12-31",
    "weeklySchedule": { ... },
    "exceptions": [ ... ]
  }
}
```

#### 6.1.3 Update Groomer Schedule
```
PUT /api/salons/{salonId}/groomers/{groomerId}/schedules/{scheduleId}
Authorization: CHIEF_GROOMER or self

Request Body: Same as create
```

#### 6.1.4 Get Working Days for Period
```
GET /api/salons/{salonId}/groomers/{groomerId}/working-days
Authorization: CHIEF_GROOMER or self

Query Parameters:
- startDate: Start date for period
- endDate: End date for period
- includeAppointments: Include existing appointments

Response:
{
  "success": true,
  "data": {
    "groomerName": "Ion Popescu",
    "period": {
      "startDate": "2024-01-01",
      "endDate": "2024-01-31"
    },
    "workingDays": [
      {
        "date": "2024-01-01",
        "startTime": "09:00",
        "endTime": "17:00",
        "breakStart": "12:00",
        "breakEnd": "13:00",
        "availableSlots": 8,
        "bookedSlots": 3
      }
    ]
  }
}
```

### 6.2 Schedule Exceptions

#### 6.2.1 Create Schedule Exception
```
POST /api/salons/{salonId}/groomers/{groomerId}/schedule-exceptions
Authorization: CHIEF_GROOMER or self

Request Body:
{
  "exceptionDate": "2024-01-15",
  "exceptionType": "DAY_OFF",
  "reason": "Concediu medical",
  "notes": "Programare la doctor"
}
```

#### 6.2.2 Get Schedule Exceptions
```
GET /api/salons/{salonId}/groomers/{groomerId}/schedule-exceptions
Authorization: CHIEF_GROOMER or self

Query Parameters:
- startDate: Filter from date
- endDate: Filter to date
- status: Filter by approval status
```

### 6.3 Schedule Templates

#### 6.3.1 Create Schedule Template
```
POST /api/salons/{salonId}/schedule-templates
Authorization: CHIEF_GROOMER

Request Body:
{
  "name": "Program 2-2 (2 zile lucru, 2 zile libere)",
  "description": "Pattern de rotație pentru echipe",
  "patternType": "ROTATION",
  "patternConfig": {
    "cycleLengthDays": 4,
    "pattern": ["WORK", "WORK", "OFF", "OFF"],
    "workingHours": {
      "start": "09:00",
      "end": "17:00",
      "breakStart": "12:00",
      "breakEnd": "13:00"
    }
  }
}
```

#### 6.3.2 Get Schedule Templates
```
GET /api/salons/{salonId}/schedule-templates
Authorization: CHIEF_GROOMER or REGULAR_GROOMER

Response:
{
  "success": true,
  "data": [
    {
      "id": "template-uuid",
      "name": "Program 2-2",
      "description": "Pattern de rotație pentru echipe",
      "patternType": "ROTATION",
      "isActive": true,
      "createdBy": "Chief Groomer Name"
    }
  ]
}
```

## 7. Integration with Appointment Booking System

### 7.1 Availability Calculation

The groomer scheduling system integrates with the appointment booking system to provide real-time availability:

```kotlin
class GroomerAvailabilityService {

    fun getAvailableSlots(
        groomerId: UserId,
        salonId: SalonId,
        date: LocalDate,
        serviceDuration: Duration
    ): List<TimeSlot> {

        // Get groomer's schedule for the date
        val groomerSchedule = getGroomerScheduleForDate(groomerId, salonId, date)
        if (!groomerSchedule.isWorkingDay) {
            return emptyList()
        }

        // Get salon working hours
        val salonHours = getSalonWorkingHours(salonId, date)
        if (!salonHours.isOpenOn(date)) {
            return emptyList()
        }

        // Calculate effective working hours (intersection of salon and groomer)
        val effectiveHours = calculateEffectiveHours(groomerSchedule, salonHours)

        // Get existing appointments
        val existingAppointments = getExistingAppointments(groomerId, date)

        // Generate available slots
        return generateAvailableSlots(effectiveHours, existingAppointments, serviceDuration)
    }
}
```

### 7.2 Conflict Detection

```kotlin
class ScheduleConflictDetector {

    fun detectConflicts(
        groomerId: UserId,
        salonId: SalonId,
        newSchedule: GroomerSchedule
    ): List<ScheduleConflict> {

        val conflicts = mutableListOf<ScheduleConflict>()

        // Check for appointment conflicts
        val existingAppointments = getAppointmentsInPeriod(
            groomerId,
            newSchedule.effectiveFrom,
            newSchedule.effectiveUntil
        )

        existingAppointments.forEach { appointment ->
            val groomerAvailable = newSchedule.isAvailableAt(
                appointment.date,
                appointment.startTime,
                appointment.endTime
            )

            if (!groomerAvailable) {
                conflicts.add(ScheduleConflict.appointmentConflict(appointment))
            }
        }

        return conflicts
    }
}
```

## 8. Error Messages (Romanian Localization)

### 8.1 Validation Error Messages

```kotlin
object GroomerScheduleErrorMessages {
    const val SALON_CLOSED = "Salonul este închis în această zi"
    const val OUTSIDE_SALON_HOURS = "Programul frizorului trebuie să fie în intervalul de funcționare al salonului"
    const val INVALID_BREAK_TIME = "Pauza trebuie să fie în intervalul de lucru"
    const val OVERLAPPING_SCHEDULES = "Există deja un program activ pentru această perioadă"
    const val INVALID_DATE_RANGE = "Data de început trebuie să fie înainte de data de sfârșit"
    const val APPOINTMENT_CONFLICT = "Există programări în conflict cu noul program"
    const val UNAUTHORIZED_ACCESS = "Nu aveți permisiunea să modificați acest program"
    const val SCHEDULE_NOT_FOUND = "Programul de lucru nu a fost găsit"
    const val INVALID_PATTERN = "Tipul de program specificat nu este valid"
    const val EXCEPTION_ALREADY_EXISTS = "Există deja o excepție pentru această dată"
    const val FUTURE_DATE_REQUIRED = "Data trebuie să fie în viitor"
    const val INVALID_TIME_FORMAT = "Formatul orei nu este valid (folosiți HH:MM)"
    const val BREAK_LONGER_THAN_WORK = "Pauza nu poate fi mai lungă decât programul de lucru"
}
```

### 8.2 Success Messages

```kotlin
object GroomerScheduleSuccessMessages {
    const val SCHEDULE_CREATED = "Programul de lucru a fost creat cu succes"
    const val SCHEDULE_UPDATED = "Programul de lucru a fost actualizat cu succes"
    const val SCHEDULE_DELETED = "Programul de lucru a fost șters cu succes"
    const val EXCEPTION_CREATED = "Excepția de program a fost creată cu succes"
    const val EXCEPTION_APPROVED = "Excepția de program a fost aprobată"
    const val TEMPLATE_CREATED = "Șablonul de program a fost creat cu succes"
}
```

## 9. Implementation Phases

### 9.1 Phase 1: Core Domain Models
- GroomerSchedule domain entity
- GroomerDailySchedule value object
- ScheduleException domain entity
- Validation logic implementation

### 9.2 Phase 2: Database and Persistence
- Database migration for groomer schedule tables
- JPA entities implementation
- Repository implementations
- Data mappers

### 9.3 Phase 3: Business Logic
- Schedule calculation algorithms
- Conflict detection logic
- Availability calculation service
- Integration with salon working hours

### 9.4 Phase 4: REST API
- Controller implementations
- DTO classes and mappers
- Authorization logic
- Error handling

### 9.5 Phase 5: Advanced Features
- Schedule templates
- Rotation patterns
- Bulk operations
- Reporting and analytics

## 10. Testing Strategy

### 10.1 Unit Tests
- Domain model validation
- Schedule calculation algorithms
- Conflict detection logic
- Pattern matching algorithms

### 10.2 Integration Tests
- Database operations
- API endpoint testing
- Authorization testing
- Cross-service integration

### 10.3 Test Scenarios
- Basic weekly schedule creation
- Rotation pattern validation
- Salon constraint enforcement
- Appointment conflict detection
- Exception handling
- Authorization edge cases

## 11. Performance Considerations

### 11.1 Optimization Strategies
- Database indexing for fast lookups
- Caching of frequently accessed schedules
- Batch processing for bulk operations
- Efficient date range queries

### 11.2 Scalability
- Horizontal scaling support
- Database partitioning by salon
- Asynchronous processing for complex calculations
- Event-driven architecture for real-time updates

This comprehensive specification provides the foundation for implementing a robust individual groomer scheduling system that seamlessly integrates with the existing salon working hours management system.
